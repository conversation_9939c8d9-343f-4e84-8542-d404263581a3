<template>
  <div class="mint-radiolist" @change="$emit('change', currentValue)">
    <div class="radio-list-item" v-for="(option, index) in options">
      <label class="mint-radiolist-label">
        <span class="label-title">{{ option.name }}</span>
        <span class="mint-radio is-right">
          <input :id="'radio_' + option.value" class="mint-radio-input" type="radio" v-model="currentValue" :disabled="option.disabled"
            :value="option.value || option">
          <span class="mint-radio-core"></span>
        </span>
      </label>
      <div class="divider" v-if="index !== options.length - 1"></div>
    </div>
  </div>
</template>

<script>
/**
 * mt-radio
 * @module components/radio
 * @desc 单选框列表，依赖 cell 组件
 *
 * @param {string[], object[]} options - 选项数组，可以传入 [{label: 'label', value: 'value', disabled: true}] 或者 ['ab', 'cd', 'ef']
 * @param {string} value - 选中值
 * @param {string} title - 标题
 * @param {string} [align=left] - checkbox 对齐位置，`left`, `right`
 *
 * @example
 * <mt-radio v-model="value" :options="['a', 'b', 'c']"></mt-radio>
 */
export default {
  name: 'mt-radio',

  props: {
    title: String,
    align: String,
    options: {
      type: Array,
      required: true
    },
    value: [String,Number],
    idStr: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      currentValue: this.value
    };
  },

  watch: {
    value(val) {
      this.currentValue = val;
    },

    currentValue(val) {
      console.log(val)
      this.$emit('input', val);
    }
  },
};
</script>

<style lang="css">
.mint-radiolist .mint-cell {
  padding: 0;
}

.mint-radiolist-label {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.mint-radiolist-title {
  font-size: 1.2rem;
  margin: 0.8rem;
  display: block;
  color: #888;
}

.radio-list-item {
  height: 4.8rem;
  padding: 0 2.4rem;
  position: relative;
  position: relative;
}
.divider {
    position: absolute;
    height: 1px;
    left: 2.4rem;
    right: 2.4rem;
    bottom: 0;
    transform: scaleY(0.25);
    background: var(--emui_color_divider_horizontal);
  }
.mint-radio {}

.mint-radio.is-right {
  float: right;
}

.mint-radio-label {
  vertical-align: middle;
  margin-left: 6px;
}

.label-title {
  font-size: 1.6rem;
  font-weight: 500;
  line-height: 4.8rem;
  color: var(--emui_text_primary);
}

.mint-radio-input {
  display: none;
}

.mint-radio-input:checked+.mint-radio-core {
  background-color: var(--emui_slide_progress_bg);
  border-color: var(--emui_slide_progress_bg);
}

.mint-radio-input:checked+.mint-radio-core::after {
  background-color: #fff;
  -webkit-transform: scale(1);
  transform: scale(1);
}

.mint-radio-input[disabled]+.mint-radio-core {
  opacity: 0.4;
}

.mint-radio-core {
  box-sizing: border-box;
  display: inline-block;
  background-color: var(--emui_radio_bg);
  border-radius: 100%;
  border: 1px solid var(--emui_radio_border);
  position: relative;
  width: 2rem;
  height: 2rem;
  vertical-align: middle;
}

.mint-radio-core::after {
  content: " ";
  border-radius: 100%;
  top: 0.4rem;
  left: 0.4rem;
  position: absolute;
  width: 1rem;
  height: 1rem;
  -webkit-transition: -webkit-transform .2s;
  transition: -webkit-transform .2s;
  transition: transform .2s;
  transition: transform .2s, -webkit-transform .2s;
  -webkit-transform: scale(0);
  transform: scale(0);
}
</style>
