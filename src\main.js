// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.

import Vue from 'vue';
import App from './App';
import router from './router';
import store from './store';
import i18n from './i18n/index';
import initApi from '@/api/initApi.js';

import twcui from '../lib/index'
Vue.use(twcui)
import moment from 'moment'
var momentDurationFormatSetup = require("moment-duration-format");
momentDurationFormatSetup(moment);
Vue.prototype.$moment = moment;

// 初始化原型方法等
initApi();
/* eslint-disable */
new Vue({
    el: "#app",
    router,
    store,
    i18n,
    components: { App },
    template: "<App/>"
});
