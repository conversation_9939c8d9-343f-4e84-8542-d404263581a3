<template>
  <div id="status-bar">
    <div class="box">
      <div class="left">
        <div>{{ statusName }}</div>
      </div>

      <div class="middle">
        <slot name="middle"></slot>
      </div>

      <div class="right">
        <!-- 重新连接 -->
        <span v-show="connectStatus === 0"
              @click="reconnect">{{ $t('reconnect') }}</span>
        <!-- loading -->
        <img v-show="connectStatus ===1"
             class="loading"
             src="../assets/loading.png"/>
        <!-- 已连接 -->
        <div v-show="connectStatus === 2"
             class="icon lock-container">
          <div id="com.huawei.smarthome:id/status-bar-switch" :class="['switch_icon',{'scale_animation':isSwitching}]"
               ref="switchIcon"
               @click="controlAppliance" v-if="sid">
          </div>
          <slot></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import lottie from 'lottie-web';

export default {
  name: "BleStatusBar",
  props: {
    sid: {
      default: null,
      type: String
    },
    chara: {
      default: null,
      type: String
    },
    switchValue: {
      default: 0,
      type: Number
    },
    connectStatus: {
      default: 0,
      type: Number
    },
    statusText:{
      default: null,
      type: String
    },
    type: { //1 ble 2wifi
      default: 1,
      type: Number
    }
  },
  data() {
    return {
      isSwitching: false,
      tempSwitchOn: 0
    };
  },
  computed: {
    statusName() {
      console.hLog('连接状态:' + this.connectStatus);
      if (this.connectStatus === 0) {
        return this.$t('no_connect');
      } else if (this.connectStatus === 1) {
        return this.$t('connecting');
      } else if (this.connectStatus === 2) {
        if (this.statusText && this.statusText.length > 0) {
          return this.statusText
        } else {
          if (this.switchValue === 1) {
            return this.$t('opened');//已开启
          }
          if (this.sid) { //没有sid没有开关
              return this.$t('status_closed');//已连接
          } else {
              return this.$t('connected');//已连接
          }
        }
      }
    },
    lottieParam() {
      const Range = [0, 229],
        StatusOff = 0,
        StatusOn = 114,
        OpenStart = [36, 56],
        OpenLoading = [56, 70],
        OpenEnd = [70, StatusOn],
        CloseStart = [152, 186],
        CloseLoading = [186, 195],
        CloseEnd = [195, 229];
      return {
        range: Range,
        status: this.tempSwitchOn ? StatusOn : StatusOff,
        start: this.tempSwitchOn ? CloseStart : OpenStart,
        loading: this.tempSwitchOn ? CloseLoading : OpenLoading,
        end: !this.tempSwitchOn ? CloseEnd : OpenEnd // end状态时tempswitchOn数据已改
      };
    },
    characteristics(){
      return (this.chara && this.chara.length > 0) ? this.chara : this.sid
    }
  },
  watch: {
    switchValue(val) { //profile,开关状态
      if (this.tempSwitchOn !== val) {
        this.tempSwitchOn = val;
        this.updateSwitch();
      }
    },
    connectStatus(val) {
      if (val === 0) {
        this.tempSwitchOn = false;
        this.updateSwitch();
      } else if (val === 2) {
        this.tempSwitchOn = this.switchValue;
        this.updateSwitch();
      }
    }
  },
  methods: {
    // 重新连接
    reconnect() {
      if (!this.$store.state.bleOn) {
        this.$store.dispatch('openBluetoothAdapter');
        return;
      }

      // 规避重连失败，需要先断连再重连
      this.$store.dispatch('disconnectBle').then((res = {}) => {
        this.$store.dispatch('subscribeBleEvent')
        this.$store.dispatch('connectBle')
        // 记录重新连接的次数
        this.$store.commit('UPDATE_STATE', {
          name: 'isReconnect',
          value: true
        })
      });
    },
    //控制设备开关
    controlAppliance() {
      if(this.type == 2){
        this.switchWifi()
        return
      }
      console.hLog('this.NoControl：', this.$store.state.NoControl);
      if (this.$store.state.NoControl) {
        let data = {
          sid: this.sid,
          data: {
            [this.characteristics]: Number(!this.switchValue)
          }
        };
        this.$store.dispatch('modifyBleDeviceProperty', {
          data: data
        }).then((res) => {
          if (res.errcode === 0) { //成功
            this.$store.commit('UPDATE_DEVICE_DATA', data)
          }
        })
      }
    },
    //动画加载
    initAnimation() {
      this.lottieAnimation = lottie.loadAnimation({
        container: this.$refs.switchIcon,
        animationData: require(`../assets/${this.$store.getters.imgPath}switch.json`),
        renderer: 'svg',
        loop: false,
        autoplay: true
      });
    },
    //动画显示
    updateSwitch() {
      this.lottieAnimation.playSegments(this.lottieParam.range, true);
      this.lottieAnimation.goToAndStop(this.lottieParam.status, true);
    },
    switchWifi() {
      let data = {
        [this.sid]: {
          [this.characteristics]: Number(!this.switchValue)
        }
      };
      this.$store.dispatch('setDevInfo', data).then(() => {
      }).catch(() => {

      })
    }
  },
  mounted() {
    setTimeout(() => {
      this.tempSwitchOn = this.switchValue;
      this.initAnimation();
      this.updateSwitch();
    }, 10);

  }
};
</script>

<style lang='less' scoped>
@import url("../style/public.less");

#status-bar {
  width: 100%;
  color: var(--emui_text_primary);

  .box {
    position: relative;
    height: 8.8rem;
    margin: 0 var(--emui_dimens_default_start);
    display: flex;
    justify-content: space-between;
    align-items: center;
    .cardStyle();

    .left {
      display: flex;
      flex-direction: column;
      justify-content: center;
      overflow: hidden;
      font-size: 1.8rem;
      .medium();
    }

    .middle {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }

    .right {
      height: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-size: 1.6rem;
      color: var(--emui_accent);
    }

    .loading {
      width: 6rem;
      height: 6rem;
    }

    .openOn {
      width: 6rem;
      height: 6rem;
    }

    .openOff {
      width: 6rem;
      height: 6rem;
    }

    .icon {
      width: 5rem;
      height: 5rem;
    }

    .lock-container {
      position: relative;

      &:active {
        opacity: 1;
      }

      .switch_icon {
        width: 5rem;
        height: 5rem;
        border-radius: 50%;

        &.scale_animation {
          animation: scale 600ms cubic-bezier(0.2, 0, 0.2, 1);
        }
      }
    }
  }
}

.pad #status-bar .box {
  padding: 0px;
}

.pad #status-bar .box .left {
  width: calc(100% / 3);
  display:flex;
  justify-content:center;
  align-items:center;
}

.pad #status-bar .box .middle{
  width: calc(100% / 3);
  display:flex;
  justify-content:center;
  align-items:center;
}

.pad #status-bar .box .right {
  width: calc(100% / 3);
  display:flex;
  justify-content:center;
  align-items:center;
}

.tahiti #status-bar .box{
  padding: 0px;
}

.tahiti #status-bar .box .left{
  width: calc(100% / 3);
  display:flex;
  justify-content:center;
  align-items:center;
}

.tahiti #status-bar .box .middle{
  width: calc(100% / 3);
  display:flex;
  justify-content:center;
  align-items:center;
}

.tahiti #status-bar .box .right{
  width: calc(100% / 3);
  display:flex;
  justify-content:center;
  align-items:center;
}

</style>
