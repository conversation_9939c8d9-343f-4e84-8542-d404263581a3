<template>
    <div id="app"
         :class="{ dark: isDarkMode,
            'pad': changeScreen, // 折叠屏展开/pad竖屏
            'tahiti':  changeScreen && !isPad // 折叠屏展开（非pad竖屏）
          }">
        <transition :name="transitionName">
            <keep-alive>
                <router-view class="router"/>
            </keep-alive>
        </transition>
    </div>
</template>

<script>
import {mapState} from 'vuex';

export default {
    name: 'App',
    data() {
        return {
            transitionName: '',
            resizeTimer: null
        };
    },
    computed: {
        ...mapState(['isDarkMode', 'isPad', 'isFold', 'h5_build_time', 'changeScreen'])
    },
    methods: {
        initApp() {
            this.$store.dispatch('getStatusBarHeight');
            this.$store.dispatch('getPhoneInfo');
            this.$store.dispatch('modifyTitleBar');
            this.$store.dispatch('getDevInfoAll');
            this.$store.dispatch('getRoomList');
            this.$store.dispatch('overrideBackPressed', true);
        }
    },
    created() {
        console.hLog('H5代码打包时间为 ' + this.h5_build_time);
        this.initApp();
        window.onResume = () => { // 后台恢复
            console.hLog('onResume');

            // 检查倒计时弹窗状态
            const countDownDialogShowing = localStorage.getItem('countDownDialogShowing');
            console.hLog('countDownDialogShowing:', countDownDialogShowing);
            if (countDownDialogShowing === '1' && this.$store.state.type != 'ios') {
                console.hLog('检测到倒计时弹窗关闭，恢复Home页面手势返回后直接返回');
                localStorage.removeItem('countDownDialogShowing');

                // 直接调用Home页面的setupGoBack方法
                if (window.homeSetupGoBack && typeof window.homeSetupGoBack === 'function') {
                    console.hLog('调用window.homeSetupGoBack恢复手势返回');
                    window.homeSetupGoBack();
                                this.$store.dispatch('getStatusBarHeight');
                    this.$store.dispatch('getPhoneInfo');
                    this.$store.dispatch('modifyTitleBar');
                    this.$store.dispatch('getDevInfoAll');
                    this.$store.dispatch('getRoomList');
                } else {
                    console.hLog('window.homeSetupGoBack不存在');
                }

                // 恢复手势返回后直接返回，不执行initApp
                return;
            }
            this.initApp();
        };
        window.onPause = () => {
            if (this.$store.state.type != 'ios') {
                localStorage.setItem('countDownDialogShowing', '1');
                window.goBack = () => {}
            }
        }
        window.addEventListener('resize', () => {
            console.hLog('resize');
            if (this.resizeTimer !== null) {
                clearTimeout(this.resizeTimer);
                this.resizeTimer = null;
            }
            this.resizeTimer = setTimeout(() => {
                this.$store.dispatch('getStatusBarHeight');
                // this.$store.dispatch('getPhoneInfo');
                clearTimeout(this.resizeTimer);
                this.resizeTimer = null;
            }, 300);
        });
    },
    watch: {
        '$route'(to, from) {
            let isBack = this.$router.isBack; // 监听路由变化时的状态为前进还是后退
            this.$router.to = to;
            this.$router.from = from;
            if (
                (
                    to &&
                    to.query &&
                    to.query.from === 'intelligent'
                ) || (
                    to.name &&
                    to.name === 'Security'
                )
            ) {
                this.transitionName = '';
            } else if (isBack) {
                this.transitionName = 'slide-right';
            } else {
                this.transitionName = 'slide-left';
            }
            this.$router.isBack = false;
        }
    }
};
</script>
<style lang="less">
#app {
    background-color: var(--emui_color_subbg);
}

#loading {
    width: 100%;
    height: 100%;
    background-color: var(--emui_color_subbg);
    position: fixed;
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
        width: 5.6rem;
        height: 5.6rem;
    }
}

.router {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    transition: all 0.3s ease;
}

.slide-left-enter,
.slide-right-leave-active {
    opacity: 0;
    transform: translateX(100%);
}

.slide-right-enter,
.slide-left-leave-active {
    opacity: 0;
    transform: translateX(-100%);
}
</style>
