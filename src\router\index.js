import Vue from 'vue';
import Router from 'vue-router';
const Name = name => () => import(`@/views/${name}`);
Vue.use(Router);
Router.prototype.goBack = function (url) {
    this.isBack = true;
    url ? this.push(url) : this.go(-1);
};
export default new Router({
    routes: [
        {
            path: '/',
            redirect: '/Home'
        },
        {
            path: '/Home',
            name: 'Home',
            component: Name('Home')
        },
        {
            path: '/Timer',
            name: 'Timer',
            component: Name('Timer')
        },
        {
            path: '/TimerSetting',
            name: 'TimerSetting',
            component: Name('TimerSetting')
        },
        {
            path: '/Setting',
            name: 'Setting',
            component: Name('Setting')
        }
    ]
});
