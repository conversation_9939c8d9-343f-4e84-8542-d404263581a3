<template>
  <div class="dialog">
    <div class="dialog-container">
      <div class="mod-head">
        <span id="com.huawei.smarthome:id/dialog_radio_title" class="title">{{ title }}</span>
        <span class="subtitle" v-if="subtitle">{{subtitle}}</span>
      </div>
      <div class="content" style="max-height:25rem;  overflow-y: auto;">
        <div class="list-item" v-for="(item, index) in values" :key="index">
          <div class="left">
            <span class="name">{{ item.name }}</span>
          </div>
          <div
            :id="idStr !== '' ? 'com.huawei.smarthome:id/switch_' + idStr : ''"
            class="right"
            @click.stop="item.checkStatus = !item.checkStatus"
          >
            <img
              class="img"
              :src="item.checkStatus == 0 ? require(`../assets/${$store.getters.imgPath}checkbox_unselect.png`) : require(`../assets/${$store.getters.imgPath}checkbox_select.png`)"
              alt
            />
          </div>
          <div v-if="index != values.length - 1" class="list-item-divider"></div>
        </div>
      </div>
      <div class="dialog-btns">
        <div id="com.huawei.smarthome:id/dialog_radio_cancel" @click.stop="cancel(true)">
          <p>{{ $t('cancel') }}</p>
        </div>
        <span class="line"></span>
        <div id="com.huawei.smarthome:id/dialog_time_confirm" @click.stop="confirm">
          <p>{{ $t('ok') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      default: '',
      type: String
    },
    subtitle: {
      default: null,
      type: String
    },
    values: {
      default: Array(),
      type: Array
    },
    idStr: {
      default: '',
      type: String
    }
  },
  name: 'DialogCheckboxPicker',
  data() {
    return {}
  },
  computed: {},
  methods: {
    cancel(check) {
      this.$emit('cancel', check)
    },
    confirm() {
      console.log(this.values)
      let value = this.values.map(item => {
        if (item.checkStatus) {
          return item.value
        }
      }).filter(item => item != undefined).join('')
      this.$emit('confirm', value)
      this.cancel(false)
    }
  }
}
</script>

<style lang="less" scoped>
@import url('../style/public.less');
.list-item {
  flex: 1;
  margin: 0 2.4rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  min-height: 4.8rem;
  .left {
    // width: 100%;
    max-width: 66%;
    margin: 0.8rem 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    .left-img {
      display: block;
      width: 2.4rem;
      height: 2.4rem;
      background-size: contain;
      margin-right: 1.6rem;
    }
    .name {
      // line-height: 4.8rem;
      font-size: 1.6rem;
      font-weight: 500;
      color: var(--emui_text_primary);
    }
  }

  .right {
    // flex: 1;
    display: flex;
    align-items: center;
    padding-left: 1.6rem;
    margin: 0.8rem 0;
  }
  .img {
    width: 2.4rem;
    height: 2.4rem;
  }
  .list-item-divider {
    position: absolute;
    width: 100%;
    height: 1px;
    bottom: 0;
    transform: scaleY(0.25);
    background: var(--emui_color_divider_horizontal);
  }
}
</style>
