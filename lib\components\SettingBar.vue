<template>
    <div
        ref="controlRef"
        class="setting-bar-container"
        :class="{ inside: inside }"
        @touchstart="handlerTouchstart"
        @touchmove="handlerTouchmove"
        @touchend="handlerTouchend"
    >
        <div
            :id="
                idStr != ''
                    ? 'com.huawei.smarthome:id/control_bar_' + idStr
                    : ''
            "
            :class="['setting-bar', disabled && !inside ? 'disabled' : '']"
            ref="setting-bar"
            @click="handleClick"
        >
            <div class="left" ref="modeTitle">
                <div class="title-icon">
                    <div class="title">
                        <span ref="name">{{ name }}</span>
                    </div>
                </div>
            </div>
            <div class="right" :style="{ backgroundImage: `url(${rightIcon}) ` }">
            </div>
            <div class="divider" v-if="divider"></div>
        </div>
    </div>
</template>

<script>
export default {
    name: "SettingBar",
    props: {
        name: {
            default: "Name",
            type: String,
        },

        disabled: {
            default: false,
            type: Boolean,
        },
        inside: {
            type: Boolean,
            default: false,
        },
        divider: {
            type: Boolean,
            default: false,
        },
        idStr: {
            default: "",
            type: String,
        },
    },
    data() {
        return {
            lastValue: null,
            fontSize: "1.6rem",
            subFontSize: "1.2rem",
            isLongPress: false,
            titleWidth: "auto",
        };
    },
    mounted() {
        if (document.body.clientWidth / this.$refs.controlRef.offsetLeft > 2) {
            this.showTipRight = false;
        } else {
            this.showTipRight = true;
        }
        let self = this;
        document.body.addEventListener("touchstart", function (e) {
            if (self.showTip) {
                self.showTip = false;
            }
        });
    },
    watch: {
        name: {
            handler(val) {
                setTimeout(() => {
                    this.updateWidth();
                    this.titleWidth = "auto";
                    let divWidth =
                        this.$refs.modeTitle.getBoundingClientRect().width;
                    for (var i = 0; i < 5; i++) {
                        let textWidth = this.measureText(
                            this.name,
                            1.6 - i * 0.1
                        ).width;
                        if (textWidth <= divWidth) {
                            this.fontSize = 1.6 - i * 0.1 + "rem";
                            return;
                        }
                    }
                    this.fontSize = "1.2rem";
                }, 40);
            },
            immediate: true,
        },
        active: {
            handler(val) {
                if (!val) return;
                setTimeout(() => {
                    let divWidth =
                        this.$refs.modeTitle.getBoundingClientRect().width;
                    for (var i = 0; i < 4; i++) {
                        let textWidth = this.measureText(
                            this.info,
                            1.2 - i * 0.1
                        ).width;
                        if (textWidth <= divWidth) {
                            this.subFontSize = 1.2 - i * 0.1 + "rem";
                            return;
                        }
                    }
                    this.subFontSize = "0.9rem";
                }, 40);
            },
            immediate: true,
        },
    },
    computed: {
        rightIcon() {
            return require(`../../lib/assets/${this.$store.getters.imgPath}ic_arrow_right.png`);
        },
    },
    methods: {
        measureText(pText, pFontSize) {
            var lDiv = document.createElement("div");
            document.body.appendChild(lDiv);
            lDiv.style.fontSize = pFontSize + "rem";
            lDiv.style.position = "absolute";
            lDiv.style.left = -1000;
            lDiv.style.top = -1000;
            lDiv.innerHTML = pText;
            var lResult = {
                width: lDiv.clientWidth,
                height: lDiv.clientHeight,
            };
            document.body.removeChild(lDiv);
            lDiv = null;
            return lResult;
        },
        handleClick(e) {
            if (!this.disabled) {
                let top = e.clientY < 350 ? "bottom" : "top";
                this.$emit("handleClick", top);
            }
        },
        handlerTouchstart(e) {
            this.isLongPress = false;
            if (this.timeOut) {
                clearTimeout(this.timeOut);
                this.timeOut = null;
            }
            const self = this;
            this.timeOut = setTimeout(function () {
                self.isLongPress = true;
            }, 500);
        },
        handlerTouchmove() {
            if (this.timeOut) {
                clearTimeout(this.timeOut);
                this.timeOut = null;
            }
            this.isLongPress = false;
        },
        handlerTouchend(e) {
            if (this.isLongPress) {
                this.$emit("longPress");
                e.preventDefault();
            }
            if (this.timeOut) {
                clearTimeout(this.timeOut);
                this.timeOut = null;
            }
        },
        updateWidth() {
            setTimeout(() => {
                this.titleWidth =
                    this.$refs.name.getBoundingClientRect().width + 1 + "px";
            }, 100);
        },
    },
    beforeDestroy() {
        if (this.timeOut) {
            clearTimeout(this.timeOut);
            this.timeOut = null;
        }
        document.body.removeEventListener("touchstart");
    },
};
</script>

<style lang="less" scoped>
@import url("../style/public.less");

.setting-bar-container {
    padding: 0 0.6rem 1.2rem;
    width: 50%;
    box-sizing: border-box;
    position: relative;

    &.inside {
        padding: 0px;

        .setting-bar {
            padding: 0px 0.8rem;
        }
    }

    .setting-bar {
        flex: 1;
        height: 6.4rem;
        .cardStyle();
        margin: 0;
        display: flex;
        justify-content: space-between;
        color: var(--emui_text_primary);
        align-items: center;
        &.disabled {
            opacity: 0.4;
        }

        .divider {
            position: absolute;
            width: calc(100% - 1.6rem);
            height: 1px;
            bottom: 0;
            left: 0.8rem;
            transform: scaleY(0.25);
            background: var(--emui_color_divider_horizontal);
        }

        .left {
            flex: 1;
            width: 0;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .title-icon {
                display: flex;
                flex-direction: row;
                align-items: center;

                .title {
                    font-size: 1.6rem;
                    text-overflow: -o-ellipsis-lastline;
                    overflow: hidden; //溢出内容隐藏
                    text-overflow: ellipsis; //文本溢出部分用省略号表示
                    display: -webkit-box; //特别显示模式
                    -webkit-line-clamp: 2; //行数
                    line-clamp: 2;
                    -webkit-box-orient: vertical; //盒子中内容竖直排列
                    word-break: break-word;
                }

                .icon-box {
                    width: 1.6rem;
                    height: 1.6rem;
                    position: relative;

                    .icon {
                        margin-left: 0.4rem;
                        width: 1.6rem;
                        height: 1.6rem;
                    }
                }
            }

            .info {
                margin-top: 0.3rem;
                font-size: 1.2rem;
                white-space: nowrap;
                color: var(--emui_text_secondary);
                text-overflow: ellipsis;
                overflow: hidden;
                max-lines: 1;
            }
        }

        .right {
            flex-shrink: 0;
            width: 1.2rem;
            height: 2.4rem;
            margin-left: 0.8rem;
            background-repeat: no-repeat;
            background-size: 1.2rem;
            background-position: center center;
        }
    }
}
</style>
