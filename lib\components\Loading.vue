<template>
  <div class="loading-comp">
    <img
      :src="require(`../assets/${this.$store.getters.imgPath}loading/loading 48dp${this.$store.getters.imgPath == 'dark/' ? ' dark' : ''}_00${num}.png`)"
      alt
    />
  </div>
</template>

<script>
export default {
  name: 'Loading',
  data() {
    return {
      num: '00',
      timer: null
    }
  },
  mounted() {
    let i = 0
    this.timer = setInterval(() => {
      if (i < 72) {
        this.num = i < 10 ? '0' + i : i
      } else {
        i = 0
      }
      i++
    }, 20)
  },
  beforeDestroy() {
    clearInterval(this.timer)
    this.timer = null
  }
}
</script>

<style lang="less" scoped>
.loading-comp {
  width: 4rem;
  height: 4rem;
  img {
    width: 4rem;
    height: 4rem;
  }
}
</style>