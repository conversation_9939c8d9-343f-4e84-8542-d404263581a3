<template>
  <div class="dialog" @click="cancel(false)">
    <div class="dialog-container">
      <div class="mod-head">
        <span id="com.huawei.smarthome:id/dialog_picker_title" class="title">{{ title }}</span>
      </div>
      <div class="content">{{content}}</div>
      <div class="dialog-btns">
        <div id="com.huawei.smarthome:id/dialog_picker_cancel" @click.stop="cancel(true)">
          <p>{{ $t('cancel') }}</p>
        </div>
        <span class="line"></span>
        <div id="com.huawei.smarthome:id/dialog_picker_confirm" @click.stop="confirm">
          <p>{{ $t('ok') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'dialogDelete',
  props: {
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  methods: {
    confirm() {
      this.$emit('confirm')
    },
    cancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="less" scoped>
@import url('../../lib/style/public.less');
.dialog-container {
  & > span {
    font-size: 1.6rem;
    line-height: 2.2rem;
    color: var(--emui_text_primary);
  }
  .content {
    padding: 0 2.4rem;
    font-size: 16px;
    font-weight: 400;
    line-height: 2.2rem;
    color: var(--emui_text_primary);
  }
}
</style>