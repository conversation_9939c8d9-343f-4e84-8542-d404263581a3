<template>
    <div id="page" @click="resetSlide">
        <Titlebar
            :title="$t('Timer')"
            @leftClick="$router.goBack()"
            :showRightIcon="false"
        />
        <div class="content" :style="{ paddingTop: `${statusBarHeight}px` }">
            <template v-if="TimerArr.length > 0">
                <div class="module-box">
                    <div
                        class="timer-slider"
                        v-for="(item, index) in TimerArr"
                        :key="index"
                    >
                        <transition name="slide">
                            <div
                                class="timer-wrapper"
                                :style="wrapperStyle(index)"
                                @touchstart.stop="touchStart(index, $event)"
                                @touchmove.stop="touchMove(index, $event)"
                                @touchend.stop="touchEnd(index, $event)"
                                @click="goEdit(index, item)"
                            >
                                <div class="timer-left">
                                    <div class="timer-time">
                                        {{ item.startTime }} -
                                        {{ item.endTime }}
                                    </div>
                                    <div class="timer-week">
                                        {{ parseWeek(item.week) }}
                                    </div>
                                </div>
                                
                                <div>
                                    <HCSwitch
                                        v-model="item.value"
                                    />
                                </div>
                            </div>
                        </transition>
                        <div
                            class="timer-delete"
                            @click.stop="deleteItem($event, index)"
                            :style="wrapperDelStyle(index)"
                        >
                            <img
                                src="../assets/circle_delete.png"
                                class="timer-delete-img"
                                alt
                            />
                        </div>
                    </div>
                </div>
            </template>
        </div>
        <template v-if="TimerArr.length == 0">
            <div class="empty">
                <img src="../assets/no-empty.png" class="empty-img" alt />
                <div class="empty-text">{{ $t("no_data") }}</div>
            </div>
        </template>
        <button class="btn" @click="clickAdd">
            {{ $t("add_task") }}
        </button>
        <DialogSave v-if="showDialog" :name="$t('address_delete')" />
    </div>
</template>
<script>
import { mapState, mapGetters, mapMutations, mapActions } from "vuex";
import HCSwitch from "../../lib/components/HCSwitch.vue";
import { goBack } from "../util/mixins";
import DialogSave from "../components/dialogSave.vue";

export default {
    name: "Timer",
    mixins: [goBack],
    components: {
        HCSwitch,
        DialogSave
    },
    data() {
        return {
            // dialogList: ['showDialog'],
            TimerArr: [],
            touchStartX: 0,
            touchEndX: 0,
            deltaX: 0,
            currentSliderIndex: null,
            showDialog: false,
            maxTimers: 8, // 最大定时数量
        };
    },
    computed: {
        ...mapGetters(["statusBarHeight"]),
        ...mapState({
            timerNum: state => state.timer.num,  // 定时器数量
            timerList: state => state.timer.timer // 定时器数组
        })
    },
    watch: {
        timerList: {
            handler(newVal) {
                if (!newVal || !newVal.timer) return;
                
                this.TimerArr = newVal.timer.map(item => {
                    // 解析时间
                    const [startHours, startMinutes] = item.start.split(':').map(Number);
                    const [endHours, endMinutes] = item.end.split(':').map(Number);
                    
                    // 计算总分钟数用于比较
                    const startTime = startHours * 60 + startMinutes;
                    const endTime = endHours * 60 + endMinutes;
                    
                    // 如果结束时间小于开始时间，说明是跨天
                    const isNextDay = endTime < startTime;

                    return {
                        id: item.id,
                        startTime: item.start,
                        endTime: item.end + (isNextDay ? ` (${this.$t('next_day')})` : ''),
                        week: this.parseWeek(item.week),
                        value: item.enable
                    };
                });
            },
            deep: true
        }
    },
    methods: {
        resetSlide() {
            if (this.TimerArr.length > 0) {
                this.TimerArr.forEach((item, index) => {
                    this.$set(this.TimerArr[index], "isSwiped", false);
                });
                this.deltaX = 0;
            }
        },
        touchStart(index, event) {
            event.stopPropagation();
            this.touchStartX = event.touches[0].clientX;
            this.currentSliderIndex = index;
            this.TimerArr.forEach((item, idx) => {
                if (this.currentSliderIndex != idx) {
                    this.$set(this.TimerArr[idx], "isSwiped", false);
                }
            });
            this.deltaX = 0;
        },
        touchMove(index, event) {
            if (this.currentSliderIndex !== index) return;
            this.touchEndX = event.touches[0].clientX;
            this.deltaX = this.touchStartX - this.touchEndX;
        },
        touchEnd(index, event) {
            if (this.deltaX > 10) {
                this.$set(this.TimerArr[index], "isSwiped", true);
            } else {
                this.$set(this.TimerArr[index], "isSwiped", false);
            }
        },
        //更改滑块样式
        wrapperStyle(index) {
            return this.TimerArr[index] && this.TimerArr[index]["isSwiped"]
                ? {
                      transform: "translate3D(-6.4rem, 0, 0)",
                      transitionDuration: "0.6s",
                  }
                : {
                      transform: "translate3D(0, 0, 0)",
                      transitionDuration: "0.6s",
                  };
        },
        // 删除滑块样式
        wrapperDelStyle(index) {
            return this.TimerArr[index] && this.TimerArr[index]["isSwiped"]
                ? {
                      transform: "translate3D(-5.4rem, -50%, 0)",
                      transitionDuration: "0.6s",
                  }
                : {
                      transform: "translate3D(0, -50%, 0)",
                      transitionDuration: "0.6s",
                  };
        },
        deleteItem(event, index) {
            event.stopPropagation();
            let self = this;
            self.$messagebox({
                title: '',
                message: self.$t('timer_delete_confirm'),
                confirmButtonText: self.$t('delete'),
                cancelButtonText: self.$t('cancel'),
                confirmButtonClass: 'red'
            }).then(type => {
                if (type == 'confirm') {
                    this.showDialog = true;
                    // 发送删除指令
                    const data = {
                        timer: {
                            action: 2, // 2表示删除
                            num: 1,
                            id: this.TimerArr[index].id
                        }
                    };
                    this.$store.dispatch("setDevInfo", data);
                    
                    setTimeout(() => {
                        this.showDialog = false;
                    }, 3000);
                }
            });
        },
        goEdit(index, item) {
            console.log("item", item);
            // 将二进制的 week 值转换为数组
            const weekArray = [];
            const weekValue = parseInt(item.week) || 0; // 添加默认值 0
            
            // 检查每一位是否为1
            for (let i = 0; i < 7; i++) {
                if (weekValue & (1 << i)) {
                    weekArray.push(i);
                }
            }

            this.$router.push({
                path: '/TimerSetting',
                query: {
                    type: 2,
                    start: item.startTime,
                    end: item.endTime,
                    week: weekValue.toString(), // 直接传递数值
                    index: index,
                    id: item.id
                }
            });
        },
        clickAdd() {
            if (this.TimerArr.length >= this.maxTimers) {
                Toast(this.$t('timer_max_limit')); // 添加提示文案
                return;
            }
            this.$router.push({ path: '/TimerSetting', query: { type: 1 } });
        },

        // 切换定时开关
        toggleTimer(item) {
            const data = {
                timer: {
                    action: 3, // 3表示修改
                    num: 1,
                    id: item.id,
                    enable: item.value
                }
            };
            this.$store.dispatch("setDevInfo", data);
        },
        // 解析星期数值为显示文本
        parseWeek(weekValue) {
            const weekDays = [
                "sunday",
                "monday",
                "tuesday",
                "wednesday",
                "thursday",
                "friday",
                "saturday",
            ];
            
            // 将二进制值转换为数组
            const selectedDays = [];
            for (let i = 0; i < 7; i++) {
                if (weekValue & (1 << i)) {
                    selectedDays.push(i);
                }
            }

            let days = [];
            let sequence = [];

            for (let i = 0; i < selectedDays.length; i++) {
                let day = selectedDays[i];
                let nextDay = selectedDays[i + 1];

                sequence.push(day);

                if (i === selectedDays.length - 1 || !(nextDay - day === 1 || nextDay - day === -6)) {
                    if (sequence.length >= 3) {
                        days.push(
                            this.$t(weekDays[sequence[0]]) +
                            this.$t("to") +
                            this.$t(weekDays[sequence[sequence.length - 1]])
                        );
                    } else {
                        let sequenceDays = sequence.map((day) => this.$t(weekDays[day]));
                        days.push(sequenceDays.join("、"));
                    }

                    if (i < selectedDays.length - 1) {
                        days.push("、");
                    }

                    sequence = [];
                }
            }

            return days.join("");
        }
    },
    // 监听路由，处理从TimerSetting返回的数据
    beforeRouteEnter(to, from, next) {
        next(vm => {
            // 只处理从TimerSetting返回且有保存操作的情况
            if (from.path === '/TimerSetting') {
                if (to.query.saved && to.query.data) {
                    const data = JSON.parse(to.query.data || '{}');
                    // 使用 Number 转换进行比较
                    if (Number(data.type) === 1) {
                        // 添加新定时
                        vm.TimerArr.push({
                            startTime: data.startTime,
                            endTime: data.endTime,
                            week: data.weekValue, // 使用二进制值而不是显示文本
                            value: true
                        });
                    } else if (Number(data.type) === 2) {
                        // 编辑已有定时
                        const index = data.index;
                        if (vm.TimerArr[index]) {
                            vm.TimerArr[index].startTime = data.startTime;
                            vm.TimerArr[index].endTime = data.endTime;
                            vm.TimerArr[index].week = data.weekValue; // 使用二进制值而不是显示文本
                        }
                    }
                    // 处理完数据后清除路由参数
                    vm.$router.replace({ path: '/Timer' });
                }
            }
        });
    },
};
</script>
<style lang="less" scoped>
@import url("../../lib/style/public.less");
#page {
    overflow-y: auto;

    .content {
        // pad适配
        margin: 5.6rem var(--home_margin) 0px var(--home_margin);
        position: relative;
    }

    .module-box {
        margin: 0.8rem 0.6rem 0;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        align-items: flex-start;
        justify-content: flex-start;

        .timer-slider {
            width: 100%;
            position: relative;
            overflow: hidden;

            .timer-wrapper {
                flex: 1;
                height: 4.8rem;
                .cardStyle();
                padding: 0.8rem 1.6rem;
                margin: 0 0.6rem 1.2rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                color: var(--emui_text_primary);

                &.disabled {
                    opacity: 0.4;
                }

                .timer-left {
                    flex: 1;
                    margin-right: 1.6rem;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;

                    .timer-time {
                        line-height: 2.2rem;
                        font-size: 1.6rem;
                        color: var(--emui_text_primary);
                    }
                    .timer-week {
                        line-height: 1.8rem;
                        font-size: 1.4rem;
                        color: var(--emui_text_secondary);
                    }
                }
            }

            .timer-delete {
                position: absolute;
                right: -4rem;
                top: 4rem;
                transform: translateY(-50%);
                width: 4rem;
                height: 4rem;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;

                .timer-delete-img {
                    width: 4rem;
                    height: 4rem;
                }
            }
        }
    }

    .slide-enter-active,
    .slide-leave-active {
        transition: all 0.3s ease-out;
    }

    .slide-enter,
    .slide-leave-to {
        transform: translateX(-4rem);
    }
}

.dark #page {
}
.empty {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .empty-img {
        width: 12rem;
        height: 12rem;
        margin-bottom: 0.8rem;
    }
    .empty-text {
        font-size: 14px;
        color: var(--emui_text_tertiary);
    }
}
.btn {
    position: fixed;
    bottom: 2.4rem;
    left: 50%;
    transform: translateX(-50%);
    width: 18rem;
    height: 4rem;
    background-color: var(--emui_btn);
    color: var(--emui_accent);
    border-radius: 2rem;
    line-height: 4rem;
    font-size: 1.6rem;
    text-align: center;
}
.btn:active {
    color: var(--emui_accent_active);
}
</style>
