<template>
  <div class="dialog" @click.stop="cancel(false)">
    <div class="dialog-container">
      <div class="mod-head">
        <span id="com.huawei.smarthome:id/dialog_radio_title" class="title">{{ title }}</span>
        <span class="subtitle" v-if="subtitle">{{subtitle}}</span>
      </div>
      <radio :options="values" @change="selectValue" :value="defaultIndex"></radio>
      <div class="dialog-btns">
        <div id="com.huawei.smarthome:id/dialog_radio_cancel" @click.stop='cancel(true)'>
          <p>{{ $t('cancel') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import radio from './radio/radio';

export default {
  props: {
    title: {
      default: '',
      type: String
    },
    subtitle: {
      default: null,
      type: String
    },
    values: {
      default: Array(),
      type: Array
    },
    defaultIndex: {
      default: 0,
      type: [Number, String]
    }
  },
  name: 'DialogRadioPicker',
  components: {radio},
  data() {
    return {
    };
  },
  computed: {
  },
  methods: {
    cancel(check) {
      this.$emit('cancel', check);
    },
    selectValue(value) {
      this.$emit('confirm', value)
    }
  }
};
</script>

<style lang="less" scoped>
@import url("../style/public.less");

.red{
  color: var(--emui_functional_red);
}

.picker {
  margin: 0.8rem 2.4rem 0px 2.4rem;
}
</style>
