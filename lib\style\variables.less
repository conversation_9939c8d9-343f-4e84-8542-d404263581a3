#app {
  // 系统色
  --emui_color_fg: #000000; // 前景色
  --emui_color_fg_inverse: #FFFFFF; // 前景色反色
  --emui_color_bg: #FFFFFF; // 界面背景颜色
  --emui_color_subbg: #F1F3F5; // 界面背景颜色
  --emui_accent: #0A59F7; // 高亮色/控件选中状态高亮色
  --emui_accent_inverse: #317AF7; // 高亮色反色
  --emui_functional_red: #E84026; // 警告色
  --emui_color_warning: #ED6F21; // 警告色
  --emui_color_handup: #E84026; // 应用色-挂断色
  --emui_color_connected: #64BB5C; // 应用色-通话
  --emui_control_normal: rgba(0,0,0,.1); // 控件通用背景色
  --emui_color_divider_horizontal: rgba(0,0,0,.2); // 分割线颜色
  --emui_color_list_divider: rgba(0,0,0,.2); // 分割线颜色
  --emui_color_list_divider_light: rgba(0,0,0,.05); // 分割线颜色
  --emui_color_subheader_divider: rgba(0,0,0,0.03); // 列表分隔条颜色
  --emui_clickeffic_default_color: rgba(0,0,0,.1); // 通用点击效果背景
  // 系统蒙黑
  --emui_mask_thin: rgba(0,0,0,.2);  // 蒙黑
  --emui_mask_light: rgba(0,0,0,.4);
  --emui_mask_regular: rgba(0,0,0,.6);
  --emui_mask_thick: rgba(0,0,0,.8);
  --emui_color_bg_floating: rgba(255,255,255,.85);
  // 文本
  // 不可点击文本颜色
  --emui_text_primary: rgba(0, 0, 0, .9); // 一级文本颜色 弹出框文本色
  --emui_text_primary_inverse: rgba(255, 255, 255, .9); // 一级文本颜色反色
  --emui_text_secondary: rgba(0, 0, 0, .6); // 二级文本颜色
  --emui_text_secondary_inverse: rgba(255, 255, 255, .6); // 二级文本颜色反色
  --emui_text_tertiary: rgba(0, 0, 0, .4); // 三级文本颜色
  --emui_text_tertiary_inverse: rgba(255, 255, 255, .4); // 三级文本颜色反色
  // 卡片
  --emui_card_bg: #FFFFFF;
  --emui_card_panel_bg: #FFFFFF;
  // 下拉菜单背景色
  --emui_dropbox_bg: #FFFFFF;
  --emui_dropbox_bg_shadow: rgba(0, 0, 30, 0.15);
  // 弹框背景色
  --emui_popup_bg: #FFFFFF;
  --emui_popup_bg2: rgba(247, 247, 247, 1);
  // 边距
  --emui_dimens_default: 1.2rem; // /屏幕左右边距（热区场景）
  --emui_dimens_default_start: 0.6rem; // 屏幕左侧边距（热区场景）
  --emui_dimens_max_start: 2.4rem; // 屏幕左侧边距（不带热区场景）
  --emui_dimens_default_end: 0.6rem; // 屏幕右侧边距（热区场景）
  --emui_dimens_max_end: 2.4rem; // 屏幕右侧边距（不带热区场景）
  --emui_dimens_default_top: 2.4rem; // 屏幕顶部边距
  --emui_dimens_default_bottom_flexible: 0; // 屏幕底部边距动态
  --emui_dimens_default_bottom_fixed: 2.4rem; // 屏幕底部边距固定
  --emui_dimens_dialog_start: 1.2rem; // dialog屏幕侧边边距
  --emui_dimens_dialog_end: 1.2rem;
  --emui_dimens_dialog_bottom: 1.6rem; // dialog屏幕底部边距
  --emui_dimens_notification_start: 1.2rem; // 通知卡片左右边距
  --emui_dimens_notification_end: 1.2rem;
  --emui_dimens_card_start: 0.6rem; // 卡片两侧的边距
  --emui_dimens_card_end: 0.6rem;
  --emui_dimens_card_inner_start: 1.6rem; // 卡片内两侧的边距
  --emui_dimens_card_inner_end: 1.6rem;
  --emui_dimens_card_middle: 1.2rem; // 卡片间间隔
  --emui_dimens_text_vertical: .2rem; // 主次文本上下间隔
  --emui_dimens_text_horizontal: .8rem; // 主次文本左右间隔
  --emui_dimens_text_margin_primary: 4.8rem; // 第一层级文本段落间隔
  --emui_dimens_text_margin_secondary: 2.4rem; // 第二层级文本段落间隔
  --emui_dimens_text_margin_tertiary: 1.6rem; // 第三层级文本段落间隔
  --emui_dimens_text_margin_fourth: .8rem; // 第四层级文本段落间隔
  --emui_dimens_text_margin_fifth: .4rem; // 第五层级文本段落间隔
  --emui_dimens_element_vertical_large: 1.6rem; // 上下方向较大间隔
  --emui_dimens_element_vertical_middle: .8rem; // 上下方向普通间隔
  --emui_dimens_element_horizontal_large: 1.6rem; // 左右方向较大间隔
  --emui_dimens_element_horizontal_middle: .8rem; // 左右方向普通间隔
  --emui_text_size_space_short: 1.1; // 小段落文本
  --emui_text_size_space_large: 1.4; // 大段落文本
  --emui_dimens_text_vertical: .2rem; // 主次文本上下间隔
  --emui_dimens_text_horizontal: .8rem; // 主次文本左右间隔
  // 字体大小
  --emui_text_size_headline7_px:	24px; // 普通app bar标题
  --emui_text_size_headline8_px:	20px; // 普通app bar小标题
  --emui_text_size_headline9_px:	18px; // 普通app bar子页签（未选中状态）
  --emui_text_size_overline_px: 14px; // 大标题下的描述文本
  --emui_text_size_subtitle1:	1.8rem; // 内容或设置项的分组标题
  --emui_text_size_subtitle2:	1.6rem; // 段落文本的分组标题
  --emui_text_size_subtitle3:	1.4rem; // 段落文本的分组标题
  --emui_text_size_button1:	1.6rem; // 内容区的大文本按钮
  --emui_text_size_button2:	1.4rem; // 列表内的小文本按钮
  --emui_text_size_button3:	1.2rem; //
  --emui_text_size_body1:	1.6rem; // 列表正文/段落文本
  --emui_text_size_body2:	1.4rem; // 列表正文/段落文本
  --emui_text_size_body3:	1.2rem; // 功能入口icon说明文本/段落文本
  --emui_text_size_caption: 1.0rem; // toolbar/tabbar描述文本
  --emui_text_size_caption1: 1.0rem; // 隐私的描述文本
  --emui_text_size_chart1: 1.0rem; // 图表格刻度
  // 圆角
  --emui_corner_radius_xlarge: 2.4rem;
  --emui_corner_radius_large:	1.6rem;
  --emui_corner_radius_mediums:  1.2rem;
  --emui_corner_radius_small:	.8rem;
  --emui_corner_radius_xsmal:	.4rem;
  --emui_corner_radius_clicked: .8rem; // 点击效果圆角
  // 自定义
  --gradient_top: linear-gradient(to bottom, rgba(255,255,255,1), rgba(255,255,255,0));
  --gradient_bottom: linear-gradient(to top, rgba(255,255,255,1), rgba(255,255,255,0));
  --color_bar_bg: rgba(0, 0, 0, .1);
  --switch_bar_bg: rgba(0, 0, 0, .1);
  --pad_column: calc((100% - 1.2rem * 9) / 8 );
  --pad_column_1: calc(var(--pad_column) * 1 + 1.2rem);
  --pad_column_3: calc(var(--pad_column) * 3 + 1.2rem * 2);
  --pad_column_4: calc(var(--pad_column) * 4 + 1.2rem * 3);
  --pad_column_6: calc(var(--pad_column) * 6 + 1.2rem * 5);
  --home_margin: 0;
  --home_margin_px: 0;
  --second_margin: 0;
  --color_tip_warn: #fdeade;
  --color_tip_error: #fce3df;
  --color_tip_normal: #e5e7e9;
  --device-opacity: 1.0; // 暗黑模式下设备图片和logo的透明度
  --picker-upfilter-bg: -webkit-gradient(linear, 0% 100%, 0% 0%, from(rgba(255, 255, 255, 0)), to(#fff));
  --picker-downfilter-bg:  -webkit-gradient(linear, 0% 100%, 0% 0%, from(#fff), to(rgba(255, 255, 255, 0)));
  --option_select_bg: rgba(10, 89, 247, 0.1); //
  --color_thumb_shadow: rgba(0, 0, 0, 0.15); //
  --color_thumb_border: rgba(0, 0, 0, 0.04); //
  --mode-icon-unselect-bg: rgba(0, 0, 0, 0.06); //模式为选择吧背景
  --color_tip_bk_error: #FCE3DF;
  --color_tip_text_error: #E84026;
  --img_ic_back: url(../assets/ic_back.png);
  --img_ic_right_arrow: url(../assets/ic_arrow_right.png);
  --emui_color_F7CE00: #F7CE00;
  --emui_color_F9A01E: #F9A01E;
  --emui_color_E64566: #E64566;
  --emui_slide_progress_bg: #0A59F7; // slidebar 进度背景色
  --emui_radio_bg: #FFFFFF;
  --emui_radio_border: rgba(0, 0, 0, 0.4);
  --emui_tip_bg: #4D4D4D;
  --emui_tip_text: #FFFFFF;
  --emui_btn: rgba(0, 0, 0, 0.06);
}

#app.dark {
  // 系统色
  --emui_color_fg: #FFFFFF; // 前景色
  --emui_color_fg_inverse: #E5E5E5; // 前景色反色
  --emui_color_bg: #000000; // 界面背景颜色
  --emui_color_subbg: #000000; // 界面背景颜色
  --emui_accent: #5291ff; // 高亮色/控件选中状态高亮色
  --emui_accent_inverse: #5291ff; // 高亮色反色
  --emui_functional_red: #D94838; // 警告色
  --emui_color_warning: #DB6B42; // 警告色
  --emui_color_handup: #D94838; // 应用色-挂断色
  --emui_color_connected: #5BA854; // 应用色-通话
  --emui_control_normal: rgba(255, 255, 255, .1); // 控件通用背景色
  --emui_color_divider_horizontal: rgba(255, 255, 255, .2); // 分割线颜色
  --emui_color_list_divider: rgba(255, 255, 255, .2); // 分割线颜色
  --emui_color_list_divider_light: rgba(255, 255, 255, .1); // 分割线颜色
  --emui_color_subheader_divider: rgba(255 ,255 ,255 , 0.1); // 列表分隔条颜色
  --emui_clickeffic_default_color: rgba(255 ,255 ,255 , .15); // 通用点击效果背景
  // 系统蒙黑
  --emui_mask_thin: rgba(0, 0, 0, .4);  // 蒙黑
  --emui_mask_light: rgba(0, 0, 0, .6);
  --emui_mask_regular: rgba(0, 0, 0, .7);
  --emui_mask_thick: rgba(0, 0, 0, .9);
  --emui_color_bg_floating: rgba(255, 255, 255, .85);
  // 文本
  // 不可点击文本颜色
  --emui_text_primary: rgba(255, 255, 255, .86); // 一级文本颜色 弹出框文本色
  --emui_text_primary_inverse: #E5E5E5; // 一级文本颜色反色
  --emui_text_secondary: rgba(255, 255, 255, .6); // 二级文本颜色
  --emui_text_secondary_inverse: rgba(255, 255, 255, .6); // 二级文本颜色反色
  --emui_text_tertiary: rgba(255, 255, 255, .4); // 三级文本颜色
  --emui_text_tertiary_inverse: rgba(255, 255, 255, .4); // 三级文本颜色反色
  // 卡片
  --emui_card_bg: #2E3033;
  --emui_card_panel_bg: #303030;
  // 下拉菜单背景色
  --emui_dropbox_bg: #363636;
  --emui_dropbox_bg_shadow: rgba(0, 0, 0, 0.5);
  // 弹框背景色
  --emui_popup_bg: #363636;
  --emui_popup_bg2: rgba(0, 0, 0, 0.85);
  // 自定义
  --gradient_top: linear-gradient(to bottom, rgba(32, 34, 36 ,1), rgba(32, 34, 36 ,0));
  --gradient_bottom: linear-gradient(to top, rgba(32, 34, 36 ,1), rgba(32, 34, 36 ,0));
  --color_bar_bg: rgba(255, 255, 255, .2);
  --switch_bar_bg: rgba(255, 255, 255, .4);
  --color_tip_warn: #21100a;
  --color_tip_error: #3B2122;
  --color_tip_normal: #262626;
  --device-opacity: 0.86; // 暗黑模式下设备图片和logo的透明度
  --picker-upfilter-bg: -webkit-gradient(linear, 0% 100%, 0% 0%, from(rgba(38, 38, 38, 0)), to(var(--emui_popup_bg)));
  --picker-downfilter-bg: -webkit-gradient(linear, 0% 100%, 0% 0%, from(var(--emui_popup_bg)), to(rgba(38, 38, 38, 0)));
  --option_select_bg: rgba(82,145,255, 0.4); //
  --mode-icon-unselect-bg: rgba(255, 255, 255, 0.1); //模式为选择吧背景
  --color_tip_bk_error: #2B0E0B;
  --color_tip_text_error: #D94838;
  --img_ic_back: url(../assets/dark/ic_back.png);
  --img_ic_right_arrow: url(../assets/dark/ic_arrow_right.png);
  --emui_color_F7CE00: #D1A738;
  --emui_color_F9A01E: #E08C3A;
  --emui_color_E64566: #CC5286;
  --emui_slide_progress_bg: #317AF7; // 高亮色反色
  --emui_radio_bg: rgba(0, 0, 0, .4);
  --emui_radio_border: rgba(255, 255, 255, .4);
  --emui_tip_text: rgba(255, 255, 255, .86);
  --emui_btn: rgba(255,255,255,0.1);
}

#app.pad:not(.tahiti) {
  --home_margin: 1.2rem;
  --home_margin_px: 12px;
}

#app.pad{
  --second_margin: calc(((100% - var(--pad_column_6)) / 2) - 1.2rem);
}
