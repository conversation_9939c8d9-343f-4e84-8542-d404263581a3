<template>
  <div class="dialog" @click="cancel" v-if="value">
    <div class="dialog-container">
      <h3 v-if="title != ''" class="title">{{ title }}</h3>
      <p  :class="title != '' ? '' : 'p1'" v-html="message"></p>
      <div class="dialog-btns">
        <div @click="cancel" :class="[ cancelButtonClasses ]">
          <p>{{ cancelButtonText }}</p>
        </div>
        <span class="line"></span>
        <div @click='confirm' :class="[ confirmButtonClasses ]">
          <p>{{ confirmButtonText }}</p>
        </div>
      </div>
    </div>
  </div>

</template>
<script>
let CONFIRM_TEXT = '确定';
let CANCEL_TEXT = '取消';

export default {
  name: 'MessageBox',
  methods: {
    cancel() {
      this.value = false
      this.callback('cancel');
    },
    confirm() {
      this.value = false
      this.callback('confirm');
    }
  },
  data() {
    return {
      callback: null,
      value: false,
      title: '',
      message: '',
      type: '',
      showConfirmButton: true,
      showCancelButton: false,
      confirmButtonText: CONFIRM_TEXT,
      cancelButtonText: CANCEL_TEXT,
      confirmButtonClass: '',
      confirmButtonDisabled: false,
      cancelButtonClass: '',
    };
  },
  computed: {
    confirmButtonClasses() {
      let classes = (this.confirmButtonClass && this.confirmButtonClass.length > 0) ? this.confirmButtonClass : '';
      return classes;
    },
    cancelButtonClasses() {
      let classes = (this.cancelButtonClass && this.cancelButtonClass.length > 0) ? this.cancelButtonClass : '';
      return classes;
    }
  },

};
</script>

<style lang="less" scoped>
@import url("../../style/public.less");

</style>
