<template>
    <div id="class-mode-edit-page">
        <Titlebar
            :title="Title"
            :leftIcon="
                require(`../assets/${$store.getters.imgPath}left_icon_close.png`)
            "
            @leftClick="handleClose"
            :rightIcon="
                require(`../assets/${$store.getters.imgPath}right_icon_submit.png`)
            "
            @rightClick="edit"
        />
        <div class="content" :style="{ paddingTop: `${headerHeight}px` }">
            <div class="module-box">
                <List-Container>
                    <List-Item-Time
                        :name="$t('start_time')"
                        :info="startInfo"
                        :defaultIndex="startIndex"
                        :subtitle="subtitle"
                        :countDown="false"
                        :limitLength="limitLength"
                        :startTime="startTime"
                        @select="(val) => select(1, val)"
                        v-model="startPickerVisible"
                        key="start"
                    ></List-Item-Time>
                    <List-Item-Time
                        :name="$t('end_time')"
                        :info="endInfo"
                        :defaultIndex="endIndex"
                        :subtitle="subtitle"
                        :countDown="false"
                        :startTime="startTime"
                        :limitLength="limitLength"
                        :divider="true"
                        @select="(val) => select(2, val)"
                        key="end"
                        v-model="endPickerVisible"
                    ></List-Item-Time>
                    <ListItemCheckbox
                        style="padding: 0"
                        :name="$t('repeat')"
                        :info="info"
                        :divider="false"
                        :options="options"
                        @select="selectWeek"
                        v-model="repeatPickerVisible"
                    ></ListItemCheckbox>
                </List-Container>
            </div>
        </div>
        <DialogSave v-if="showDialog" :name="$t('add_save')" />
    </div>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from "vuex";
import { goBack } from "../util/mixins";
import Toast from "../../lib/components/Toast/Toast.js";
import DialogSave from "../components/dialogSave.vue";

export default {
    name: "TimerSetting",
    mixins: [goBack],
    components: {
        DialogSave
    },
    data() {
        return {
            dialogList: ['startPickerVisible', 'endPickerVisible', 'repeatPickerVisible'],
            type: 1, // 1添加 2编辑
            subtitle: "", // 弹窗子标题
            startIndex: 0, // 开始时间默认值
            endIndex: 0, // 结束时间默认值
            startTime: 0, // 开始时间
            limitLength: 1439, // 时长
            start: "", // 开始时间显示文本
            end: "", // 结束时间显示文本
            options: [
                {
                    name: this.$t("monday"),
                    value: 1,
                    checkStatus: 0,
                },
                {
                    name: this.$t("tuesday"),
                    value: 2,
                    checkStatus: 0,
                },
                {
                    name: this.$t("wednesday"),
                    value: 3,
                    checkStatus: 0,
                },
                {
                    name: this.$t("thursday"),
                    value: 4,
                    checkStatus: 0,
                },
                {
                    name: this.$t("friday"),
                    value: 5,
                    checkStatus: 0,
                },
                {
                    name: this.$t("saturday"),
                    value: 6,
                    checkStatus: 0,
                },
                {
                    name: this.$t("sunday"),
                    value: 0,
                    checkStatus: 0,
                },
            ],
            selectValue: [],
            showDialog: false,
            originalData: null, // 保存初始数据用于比较
            initializing: false, // 添加初始化标记
        };
    },
    created() {
        console.log("TimerSetting created");
        this.type = Number(this.$route.query.type);
        this.initData();
    },
    // 组件被激活时触发
    activated() {
        console.log("TimerSetting activated");
        this.type = Number(this.$route.query.type);
        this.initData();
    },
    // 组件被停用时触发
    deactivated() {
        console.log("TimerSetting deactivated");
        // 清空数据
        this.start = "";
        this.end = "";
        this.startIndex = 0;
        this.endIndex = 0;
        this.selectValue = [];
        this.options.forEach(option => {
            option.checkStatus = false;
        });
    },
    watch: {
        // 移除这些 watch，因为它们会干扰初始化
        // start: { ... },
        // end: { ... }
    },
    computed: {
        ...mapGetters(["headerHeight", "statusBarHeight", "canControl"]),

        Title() {
            return this.type == 1
                ? this.$t("add_task_title")
                : this.$t("edit_task_title");
        },

        startInfo() {
            let hour =
                parseInt(this.startIndex / 60) >= 10
                    ? parseInt(this.startIndex / 60)
                    : "0" + parseInt(this.startIndex / 60);
            let min =
                this.startIndex % 60 >= 10
                    ? this.startIndex % 60
                    : "0" + (this.startIndex % 60);
            return hour + ":" + min;
        },
        endInfo() {
            let hour =
                parseInt(this.endIndex / 60) >= 10
                    ? parseInt(this.endIndex / 60)
                    : "0" + parseInt(this.endIndex / 60);
            let min =
                this.endIndex % 60 >= 10
                    ? this.endIndex % 60
                    : "0" + (this.endIndex % 60);
            return hour + ":" + min;
        },
        info() {
            const weekDays = [
                "sunday",
                "monday",
                "tuesday",
                "wednesday",
                "thursday",
                "friday",
                "saturday",
            ];

            let days = [];
            let sequence = [];

            for (let i = 0; i < this.selectValue.length; i++) {
                let day = this.selectValue[i];
                let nextDay = this.selectValue[i + 1];

                sequence.push(day);

                // Check if the next day is not consecutive or it's the last day
                if (
                    i === this.selectValue.length - 1 ||
                    !(nextDay - day === 1 || nextDay - day === -6)
                ) {
                    if (sequence.length >= 3) {
                        // If the sequence is three days or longer, use '至'
                        days.push(
                            this.$t(weekDays[sequence[0]]) +
                                this.$t("to") +
                                this.$t(weekDays[sequence[sequence.length - 1]])
                        );
                    } else {
                        // If the sequence is less than three days, use '、'
                        let sequenceDays = sequence.map((day) =>
                            this.$t(weekDays[day])
                        );
                        days.push(sequenceDays.join("、"));
                    }

                    // Add '、' between non-consecutive sequences
                    if (i < this.selectValue.length - 1) {
                        days.push("、");
                    }

                    // Reset the sequence
                    sequence = [];
                }
            }

            return days.join("");
        },
    },
    methods: {
        initData() {
            this.initializing = true;
            
            if (this.type === 1) {
                // 添加模式 - 使用当前时间作为默认值
                const now = new Date();
                this.startIndex = now.getHours() * 60 + now.getMinutes();
                this.endIndex = this.startIndex + 60;
                this.selectValue = []; // 清空选择
            } else {
                // 编辑模式 - 使用传入的值
                const start = this.$route.query.start;
                const end = this.$route.query.end;
                const weekValue = parseInt(this.$route.query.week) || 0; // 添加默认值 0

                // 解析开始时间
                const [startHours, startMinutes] = start.split(':');
                this.startIndex = parseInt(startHours) * 60 + parseInt(startMinutes);

                // 解析结束时间
                const [endHours, endMinutes] = end.split(':');
                this.endIndex = parseInt(endHours) * 60 + parseInt(endMinutes);

                // 解析星期值为数组
                this.selectValue = [];
                for (let i = 0; i < 7; i++) {
                    if (weekValue & (1 << i)) {
                        this.selectValue.push(i);
                    }
                }

                // 设置选中状态
                this.options.forEach(option => {
                    option.checkStatus = this.selectValue.includes(option.value);
                });
            }

            // 保存初始数据用于比较
            this.originalData = {
                startIndex: this.startIndex,
                endIndex: this.endIndex,
                selectValue: [...this.selectValue]
            };

            this.$nextTick(() => {
                this.initializing = false;
            });
        },
        // 选择时间
        select(type, value) {
            if (type == 1) {
                this.startIndex = value;
            } else {
                this.endIndex = value;
            }
        },
        selectWeek(value) {
            // 将字符串形式的数字组合转换为数组
            this.selectValue = value.toString().split('').map(Number);
            console.log('selectValue after:', this.selectValue);
        },
        // 添加/编辑定时
        saveTimer() {
            if (!Array.isArray(this.selectValue)) {
                this.selectValue = [];
            }

            const weekValue = this.selectValue.length > 0 
                ? this.selectValue.reduce((acc, curr) => acc + Math.pow(2, curr), 0)
                : 0;
            
            const timerItem = {
                id: this.type === 2 ? Number(this.$route.query.index) + 1 : undefined,
                start: this.startInfo,
                end: this.endInfo,
                week: weekValue,
                enable: true,
                para: "",
                para2: "",
                para3: "",
                sid: "",
                sid2: "",
                sid3: "",
                paraValue: 0,
                paraValue2: 0,
                paraValue3: 0
            };

            const data = {
                timer: {
                    action: this.type === 1 ? 1 : 3,
                    timer: [timerItem]
                }
            };

            this.$store.dispatch("setDevInfo", data);
            this.showDialog = true;
            
            setTimeout(() => {
                this.showDialog = false;
                // 直接返回上一页,不需要传递数据
                this.$router.go(-1);
            }, 3000);
        },
        edit() {
            this.saveTimer();
        },
        // 检查是否有更改
        hasChanges() {
            if (this.type == 1) {
                // 新增模式：检查是否有实际输入内容
                return this.selectValue.length > 0 || 
                       this.startIndex !== this.originalData.startIndex ||
                       this.endIndex !== this.originalData.endIndex;
            } else {
                // 编辑模式：检查是否与原始数据不同
                return this.startIndex !== this.originalData.startIndex ||
                       this.endIndex !== this.originalData.endIndex ||
                       JSON.stringify(this.selectValue) !== JSON.stringify(this.originalData.selectValue);
            }
        },
        // 处理关闭按钮点击
        handleClose() {
            if (this.hasChanges()) {
                this.$messagebox({
                    title: '',
                    message: this.$t('save_changes_message'),
                    showCancelButton: true,
                    confirmButtonText: this.$t('save'),
                    cancelButtonText: this.$t('discard'),
                    showClose: true
                }).then(action => {
                    if (action === 'confirm') {
                        this.edit();
                    } else {
                        // 直接返回
                        this.$router.back()
                    }
                }).catch(() => {
                    // 点击关闭按钮，不做任何操作
                });
            } else {
                // 没有更改，直接返回
                console.log("没有更改，直接返回")
                this.$router.back()
            }
        }
    },
};
</script>
<style lang="less" scoped>
@import url("../../lib/style/public.less");
#class-mode-edit-page {
    .content {
        // pad适配
        margin: 0 var(--second_margin) 0px var(--second_margin);
        position: relative;
    }
    .module-box {
        margin: 0.8rem 0.6rem 0;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-items: flex-start;
        justify-content: flex-start;
    }
}
</style>
