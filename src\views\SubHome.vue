<!--
 * @Author: Numb丶 <EMAIL>
 * @Date: 2025-06-10 22:09:00
 * @LastEditors: Numb丶 <EMAIL>
 * @LastEditTime: 2025-08-01 12:02:33
 * @FilePath: \H5_WIFI_2QBO\src\views\SubHome.vue
 * @Description: 室内环境质量页面，显示环境概览和统计数据报表
-->
<template>
    <div id="subhome-page">
        <Titlebar :title="title" :showRightIcon="false" @leftClick="$router.goBack()" />
        <div class="content" :style="{ paddingTop: `${statusBarHeight}px` }">
            <div class="module-box">
                <!-- 自定义Tab栏 -->
                <div class="tab-wrapper">
                    <van-tabs v-model="selectIndex" swipeable @change="toggle" title-active-color="var(--emui_accent)"
                        title-inactive-color="var(--emui_text_secondary)" :line-width="selectIndex == 0
                            ? width1
                            : selectIndex == 1
                                ? width2
                                : selectIndex == 2
                                    ? width3
                                    : width4
                            ">
                        <van-tab v-for="(tab, index) in tabs" :key="index" :title="tab"></van-tab>
                    </van-tabs>
                </div>

                <!-- 日期选择器 -->
                <div class="date-selector">
                    <!-- 左箭头按钮 -->
                    <div class="date-nav-btn left" @click="navigateDate('prev')">
                        <img style="width: 1.2rem;height: 2.4rem;"
                            :src="require(`../assets/${$store.state.isDarkMode ? 'dark/' : ''}ic_left_arrow.png`)"
                            alt="Left Arrow" />
                    </div>

                    <!-- 中间日期显示区域 -->
                    <div class="date-display" @click="showDatePicker">
                        <span class="date-text">{{ displayDateText }}</span>
                        <img style="width: 2.4rem;height: 2.4rem;"
                            :src="require(`../assets/${$store.state.isDarkMode ? 'dark/' : ''}ic_down_arrow.png`)"
                            alt="Down Arrow" />
                    </div>

                    <!-- 右箭头按钮 -->
                    <div class="date-nav-btn right" @click="navigateDate('next')">
                        <img style="width: 1.2rem;height: 2.4rem;"
                            :src="require(`../assets/${$store.state.isDarkMode ? 'dark/' : ''}ic_right_arrow.png`)"
                            alt="Right Arrow" />
                    </div>
                </div>

                <!-- Tab内容区域 -->
                <div class="tab-content">
                    <template v-if="type == 1">
                        <div v-if="selectIndex === 0" class="report-content">
                            <!-- 环境质量概览 -->
                            <div class="environment-section">
                                <GaugeChart :width="260" :height="240" :value="qualityLevel" :disabled="disabled"
                                    :showSubText="true" :subText="$t('daily_average_quality')" />
                            </div>

                            <SubTitle :title="$t('today_data')"
                                style="font-size: 1.4rem;color:var(--emui_text_secondary)"></SubTitle>

                            <InfoBar :disabled="false" style="width: auto;">
                                <div class="block-info info-item">
                                    <div>{{ Co2Current }}<span>{{ $t('Co2Current_unit') }}</span></div>
                                    <div><span>{{
                                        $t('Co2Current_sub') }}</span></div>
                                </div>
                                <div class="block-info info-item">
                                    <div>{{ HchoCurrent }}<span>{{ $t('HchoCurrent_unit') }}</span></div>
                                    <div><span>{{
                                        $t('HchoCurrent_sub') }}</span></div>
                                </div>
                            </InfoBar>

                            <InfoBar :disabled="false" style="width: auto;">
                                <div class="block-info info-item">
                                    <div>{{ HeatCurrentFloat }}<span>{{ $t('HeatCurrentFloat_unit') }}</span></div>
                                    <div><span>{{
                                        $t('HeatCurrentFloat_sub') }}</span></div>
                                </div>
                                <div class="block-info info-item">
                                    <div>{{ MoistureCurrent }}<span>{{ $t('MoistureCurrent_unit') }}</span></div>
                                    <div><span>{{
                                        $t('MoistureCurrent_sub') }}</span></div>
                                </div>
                            </InfoBar>

                        </div>
                        <div v-else-if="selectIndex === 1" class="report-content">
                            <!-- 周报内容 -->
                            <!-- 环境质量概览 -->
                            <div class="environment-section">
                                <GaugeChart :width="260" :height="240" :value="qualityLevel" :disabled="disabled"
                                    :showSubText="true" :subText="$t('weekly_average_quality')" />
                            </div>
                            <SubTitle :title="$t('weekly_average_data')"
                                style="font-size: 1.4rem;color:var(--emui_text_secondary)"></SubTitle>

                            <InfoBar :disabled="false" style="width: auto;">
                                <div class="block-info info-item">
                                    <div>{{ Co2Current }}<span>{{ $t('Co2Current_unit') }}</span></div>
                                    <div><span>{{
                                        $t('Co2Current_sub') }}</span></div>
                                </div>
                                <div class="block-info info-item">
                                    <div>{{ HchoCurrent }}<span>{{ $t('HchoCurrent_unit') }}</span></div>
                                    <div><span>{{
                                        $t('HchoCurrent_sub') }}</span></div>
                                </div>
                            </InfoBar>

                            <InfoBar :disabled="false" style="width: auto;">
                                <div class="block-info info-item">
                                    <div>{{ HeatCurrentFloat }}<span>{{ $t('HeatCurrentFloat_unit') }}</span></div>
                                    <div><span>{{
                                        $t('average_temp') }}</span></div>
                                </div>
                                <div class="block-info info-item">
                                    <div>{{ MoistureCurrent }}<span>{{ $t('MoistureCurrent_unit') }}</span></div>
                                    <div><span>{{
                                        $t('average_humi') }}</span></div>
                                </div>
                            </InfoBar>

                        </div>
                        <div v-else-if="selectIndex === 2" class="report-content">
                            <!-- 月报内容 -->
                            <!-- 环境质量概览 -->
                            <div class="environment-section">
                                <GaugeChart :width="260" :height="240" :value="qualityLevel" :disabled="disabled"
                                    :showSubText="true" :subText="$t('monthly_average_quality')" />
                            </div>
                            <SubTitle :title="$t('monthly_average_data')"
                                style="font-size: 1.4rem;color:var(--emui_text_secondary)"></SubTitle>
                            <InfoBar :disabled="false" style="width: auto;">
                                <div class="block-info info-item">
                                    <div>{{ Co2Current }}<span>{{ $t('Co2Current_unit') }}</span></div>
                                    <div><span>{{
                                        $t('Co2Current_sub') }}</span></div>
                                </div>
                                <div class="block-info info-item">
                                    <div>{{ HchoCurrent }}<span>{{ $t('HchoCurrent_unit') }}</span></div>
                                    <div><span>{{
                                        $t('HchoCurrent_sub') }}</span></div>
                                </div>
                            </InfoBar>
                            <InfoBar :disabled="false" style="width: auto;">
                                <div class="block-info info-item">
                                    <div>{{ HeatCurrentFloat }}<span>{{ $t('HeatCurrentFloat_unit') }}</span></div>
                                    <div><span>{{
                                        $t('average_temp') }}</span></div>
                                </div>
                                <div class="block-info info-item">
                                    <div>{{ MoistureCurrent }}<span>{{ $t('MoistureCurrent_unit') }}</span></div>
                                    <div><span>{{
                                        $t('average_humi') }}</span></div>
                                </div>
                            </InfoBar>
                        </div>
                        <div v-else-if="selectIndex === 3" class="report-content">
                            <!-- 年报内容 -->
                            <!-- 环境质量概览 -->
                            <div class="environment-section">
                                <GaugeChart :width="260" :height="240" :value="qualityLevel" :disabled="disabled"
                                    :showSubText="true" :subText="$t('yearly_average_quality')" />
                            </div>
                            <SubTitle :title="$t('yearly_average_data')"
                                style="font-size: 1.4rem;color:var(--emui_text_secondary)"></SubTitle>
                            <InfoBar :disabled="false" style="width: auto;">
                                <div class="block-info info-item">
                                    <div>{{ Co2Current }}<span>{{ $t('Co2Current_unit') }}</span></div>
                                    <div><span>{{
                                        $t('Co2Current_sub') }}</span></div>
                                </div>
                                <div class="block-info info-item">
                                    <div>{{ HchoCurrent }}<span>{{ $t('HchoCurrent_unit') }}</span></div>
                                    <div><span>{{
                                        $t('HchoCurrent_sub') }}</span></div>
                                </div>
                            </InfoBar>
                            <InfoBar :disabled="false" style="width: auto;">
                                <div class="block-info info-item">
                                    <div>{{ HeatCurrentFloat }}<span>{{ $t('HeatCurrentFloat_unit') }}</span></div>
                                    <div><span>{{
                                        $t('average_temp') }}</span></div>
                                </div>
                                <div class="block-info info-item">
                                    <div>{{ MoistureCurrent }}<span>{{ $t('MoistureCurrent_unit') }}</span></div>
                                    <div><span>{{
                                        $t('average_humi') }}</span></div>
                                </div>
                            </InfoBar>
                        </div>
                    </template>
                    <template v-else-if="type == 2">
                        <div class="report-content">
                            <!-- 二氧化碳图表卡片 -->
                            <div class="co2-chart-section">
                                <!-- 数值显示 -->
                                <div class="co2-value-header">
                                    <span style="color: var(--emui_text_secondary);font-size: 1.2rem;"
                                        v-html="getDisplayValueText('co2')">
                                    </span>
                                    <span v-if="selectIndex === 0" :class="Co2LevelClassObject"
                                        style="font-size: 2.4rem;margin-left: 6.4rem;">{{ Co2StatusText }}</span>
                                </div>

                                <!-- 图表 -->
                                <div class="chart-container">
                                    <template v-if="chartData.co2 && chartData.co2.length > 0">
                                        <LineChart :reportType="selectIndex" :dataType="'co2'"
                                            :chartDataFromParent="chartData.co2"
                                            @getValue="onChartValueSelected" />
                                    </template>
                                    <template v-else>
                                        <div class="no-data">
                                            <img src="../assets/no-data.png" class="no-data-img" alt />
                                            <div class="no-data-text">暂无数据</div>
                                        </div>
                                    </template>
                                </div>
                            </div>

                            <SubTitle :title="$t('level_area_desc')"
                                style="font-size: 1.4rem;color:var(--emui_text_secondary)"></SubTitle>

                            <!-- 等级说明内容 -->
                            <div class="level-description">
                                <p class="level-intro">{{ $t('level_desc_intro') }}</p>

                                <div class="level-item">
                                    <span class="level-dot level-dot-normal"></span>
                                    <span class="level-text">
                                        <span class="level-name">{{ $t('level_normal') }}：</span>
                                        <span class="level-range">{{ $t('level_normal_range') }}</span>
                                    </span>
                                </div>

                                <div class="level-item">
                                    <span class="level-dot level-dot-exceeded"></span>
                                    <span class="level-text">
                                        <span class="level-name">{{ $t('level_exceeded') }}：</span>
                                        <span class="level-range">{{ $t('level_exceeded_range') }}</span>
                                    </span>
                                </div>

                                <div class="level-item">
                                    <span class="level-dot level-dot-severe"></span>
                                    <span class="level-text">
                                        <span class="level-name">{{ $t('level_severely_exceeded') }}：</span>
                                        <span class="level-range">{{ $t('level_severely_exceeded_range') }}</span>
                                    </span>
                                </div>
                            </div>

                        </div>
                    </template>
                    <template v-else-if="type == 3">
                        <div class="report-content">
                            <!-- 甲醛图表卡片 -->
                            <div class="co2-chart-section">
                                <!-- 数值显示 -->
                                <div class="co2-value-header">
                                    <span style="color: var(--emui_text_secondary);font-size: 1.2rem;"
                                        v-html="getDisplayValueText('hcho')">
                                    </span>
                                    <span v-if="selectIndex === 0" :class="HchoLevelClassObject"
                                        style="font-size: 2.4rem;margin-left: 6.4rem;">{{ HchoStatusText }}</span>
                                </div>

                                <!-- 图表 -->
                                <div class="chart-container">
                                    <template v-if="chartData.hcho && chartData.hcho.length > 0">
                                        <LineChart :reportType="selectIndex" :dataType="'hcho'"
                                            :chartDataFromParent="chartData.hcho"
                                            @getValue="onChartValueSelected" />
                                    </template>
                                    <template v-else>
                                        <div class="no-data">
                                            <img src="../assets/no-data.png" class="no-data-img" alt />
                                            <div class="no-data-text">暂无数据</div>
                                        </div>
                                    </template>
                                </div>
                            </div>

                            <SubTitle :title="$t('level_area_desc')"
                                style="font-size: 1.4rem;color:var(--emui_text_secondary)"></SubTitle>

                            <!-- 甲醛等级说明内容 -->
                            <div class="level-description">
                                <p class="level-intro">{{ $t('level_desc_intro') }}</p>

                                <div class="level-item">
                                    <span class="level-dot level-dot-normal"></span>
                                    <span class="level-text">
                                        <span class="level-name">{{ $t('level_normal') }}：</span>
                                        <span class="level-range">{{ $t('hcho_level_normal_range') }}</span>
                                    </span>
                                </div>

                                <div class="level-item">
                                    <span class="level-dot level-dot-exceeded"></span>
                                    <span class="level-text">
                                        <span class="level-name">{{ $t('level_exceeded') }}：</span>
                                        <span class="level-range">{{ $t('hcho_level_exceeded_range') }}</span>
                                    </span>
                                </div>

                                <div class="level-item">
                                    <span class="level-dot level-dot-severe"></span>
                                    <span class="level-text">
                                        <span class="level-name">{{ $t('level_severely_exceeded') }}：</span>
                                        <span class="level-range">{{ $t('hcho_level_severely_exceeded_range') }}</span>
                                    </span>
                                </div>
                            </div>

                        </div>
                    </template>
                    <template v-else-if="type == 4">
                        <div class="report-content">
                            <!-- 温度图表卡片 -->
                            <div class="co2-chart-section">
                                <!-- 数值显示 -->
                                <div class="co2-value-header">
                                    <span style="color: var(--emui_text_secondary);font-size: 1.2rem;"
                                        v-html="getDisplayValueText('heat')">
                                    </span>
                                </div>

                                <!-- 图表 -->
                                <div class="chart-container">
                                    <template v-if="chartData.heat && chartData.heat.length > 0">
                                        <LineChart :reportType="selectIndex" :dataType="'heat'"
                                            :chartDataFromParent="chartData.heat"
                                            @getValue="onChartValueSelected" />
                                    </template>
                                    <template v-else>
                                        <div class="no-data">
                                            <img src="../assets/no-data.png" class="no-data-img" alt />
                                            <div class="no-data-text">暂无数据</div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template v-else-if="type == 5">
                        <div class="report-content">
                            <!-- 湿度图表卡片 -->
                            <div class="co2-chart-section">
                                <!-- 数值显示 -->
                                <div class="co2-value-header">
                                    <span style="color: var(--emui_text_secondary);font-size: 1.2rem;"
                                        v-html="getDisplayValueText('moisture')">
                                    </span>
                                    <span v-if="selectIndex === 0" :class="MoistureeLevelClassObject"
                                        style="font-size: 2.4rem;margin-left: 6.4rem;">{{ MoistureStatusText }}</span>
                                </div>

                                <!-- 图表 -->
                                <div class="chart-container">
                                    <LineChart :reportType="selectIndex" :dataType="'moisture'"
                                        :chartDataFromParent="chartData.moisture"
                                        @getValue="onChartValueSelected" />
                                </div>
                            </div>

                            <SubTitle :title="$t('level_area_desc')"
                                style="font-size: 1.4rem;color:var(--emui_text_secondary)"></SubTitle>

                            <!-- 湿度等级说明内容 -->
                            <div class="level-description">
                                <p class="level-intro">{{ $t('level_desc_intro') }}</p>

                                <div class="level-item">
                                    <span class="level-dot level-dot-moisture-comfortable"></span>
                                    <span class="level-text">
                                        <span class="level-name">{{ $t('moisture_level_comfortable') }}：</span>
                                        <span class="level-range">{{ $t('moisture_level_comfortable_range') }}</span>
                                    </span>
                                </div>

                                <div class="level-item">
                                    <span class="level-dot level-dot-moisture-dry"></span>
                                    <span class="level-text">
                                        <span class="level-name">{{ $t('moisture_level_dry') }}：</span>
                                        <span class="level-range">{{ $t('moisture_level_dry_range') }}</span>
                                    </span>
                                </div>

                                <div class="level-item">
                                    <span class="level-dot level-dot-moisture-humid"></span>
                                    <span class="level-text">
                                        <span class="level-name">{{ $t('moisture_level_humid') }}：</span>
                                        <span class="level-range">{{ $t('moisture_level_humid_range') }}</span>
                                    </span>
                                </div>
                            </div>

                        </div>
                    </template>

                </div>
            </div>
        </div>

        <!-- 日期选择弹窗 -->
        <DialogDatePicker v-if="showDatePickerPopup" :title="getDatePickerTitle()" :mode="getDatePickerMode()"
            :defaultValue="getDefaultValue()" :maxDate="maxDate" :minDate="minDate" @confirm="confirmDatePicker"
            @cancel="cancelDatePicker" />

        <!-- 全局Loading遮罩 -->
        <div v-if="isGlobalLoading" class="global-loading-mask">
            <div class="global-loading">
                <img class="loading-icon" :src="require('../../lib/assets/loading.png')" alt="Loading" />
                <span class="loading-text">数据加载中...</span>
            </div>
        </div>

    </div>
</template>
<script>
import { mapState, mapGetters } from "vuex";
import GaugeChart from "./GaugeChart.vue";
import LineChart from "./LineChart.vue";
import { goBack } from "../util/mixins";
export default {
    name: "SubHome",
    mixins: [goBack],
    components: {
        GaugeChart,
        LineChart,
    },
    data () {
        return {
            dialogList: ['showDatePickerPopup'],
            type: 1,
            title: "",
            selectIndex: 0, // 默认选中日报
            tabs: [
                this.$t("daily_report"),
                this.$t("weekly_report"),
                this.$t("monthly_report"),
                this.$t("yearly_report"),
            ],
            width1: 0,
            width2: 0,
            width3: 0,
            width4: 0,
            // 日期选择相关
            showDatePickerPopup: false,
            selectedDate: new Date(), // 日报选中的日期
            selectedWeek: [], // 周报选中的周范围
            selectedWeekDate: new Date(), // 周报选中的具体日期
            selectedMonth: new Date(), // 月报选中的日期（保持具体日期）
            selectedYear: new Date(), // 年报选中的年份
            maxDate: new Date(),
            minDate: new Date(2020, 0, 1), // 最小日期设为2020年1月1日
            deviceBindDate: null, // 设备绑定日期（通过第一条数据时间推算）
            // 图表数据相关
            chartData: {}, // 存储各传感器类型的图表数据
            selectedChartData: {}, // 存储选中的图表数据点
            isLoadingData: false, // 数据加载状态

            // 传感器类型配置
            sensorConfig: {
                2: { sid: 'co2', character: 'current', dataType: 'co2' },      // CO2
                3: { sid: 'hcho', character: 'currentFloat', dataType: 'hcho' },     // 甲醛
                4: { sid: 'heat', character: 'currentFloat', dataType: 'heat' }, // 温度
                5: { sid: 'moisture', character: 'current', dataType: 'moisture' }  // 湿度
            },

            // 环境质量概览统计数据
            environmentStats: {
                co2: { avg: null, loading: false },
                hcho: { avg: null, loading: false },
                heat: { avg: null, loading: false },
                moisture: { avg: null, loading: false }
            },

            // 全局loading状态
            isGlobalLoading: false
        };
    },
    computed: {
        ...mapGetters(["headerHeight", "canControl", "statusBarHeight"]),
        ...mapState({
        }),

        // 动态获取CO2数据（环境质量概览使用统计平均值，其他使用实时值）
        Co2Current () {
            if (this.type == 1 && this.environmentStats.co2.avg !== null) {
                return Math.round(this.environmentStats.co2.avg);
            }
            return '--'
        },

        // 动态获取甲醛数据
        HchoCurrent () {
            if (this.type == 1 && this.environmentStats.hcho.avg !== null) {
                return this.environmentStats.hcho.avg.toFixed(2); // 保留3位小数
            }
            return '--'
        },

        // 动态获取温度数据
        HeatCurrentFloat () {
            if (this.type == 1 && this.environmentStats.heat.avg !== null) {
                return Math.round(this.environmentStats.heat.avg * 10) / 10; // 保留1位小数
            }
            return '--'
        },

        // 动态获取湿度数据
        MoistureCurrent () {
            if (this.type == 1 && this.environmentStats.moisture.avg !== null) {
                return Math.round(this.environmentStats.moisture.avg);
            }
            return '--'
        },
        // 显示的日期文本
        displayDateText () {
            switch (this.selectIndex) {
                case 0: // 日报
                    return this.formatDate(this.selectedDate, 'daily');
                case 1: // 周报
                    if (this.selectedWeek.length === 2) {
                        return this.formatDateRange(this.selectedWeek[0], this.selectedWeek[1]);
                    }
                    return this.$t('this_week');
                case 2: // 月报
                    return this.formatDate(this.selectedMonth, 'monthly');
                case 3: // 年报
                    return this.formatDate(this.selectedYear, 'yearly');
                default:
                    return '';
            }
        },
        // 质量等级（环境质量概览使用统计平均值，其他使用实时值）
        qualityLevel () {
            // 决策理由：环境质量概览使用4个物模型统计数据的平均值计算质量等级，与其他数据获取方式保持一致
            let co2Value, hchoValue;

            if (this.type == 1) {
                // 环境质量概览：使用统计平均值
                co2Value = this.environmentStats.co2.avg;
                hchoValue = this.environmentStats.hcho.avg; // 已经是毫克单位
            }

            // 如果数据还未加载完成，返回默认值
            if (co2Value === null || hchoValue === null) {
                return 2; // 默认为"良"
            }

            // 首先检查是否为"差"（任一指标超标）
            if (co2Value > 1000 || hchoValue > 0.08) {
                return 3; // 差
            }

            // 检查是否为"优"（两个指标都在优秀范围）
            if (co2Value <= 450 && hchoValue <= 0.05) {
                return 1; // 优
            }

            // 检查是否为"良"（两个指标都在良好范围）
            if ((co2Value >= 450 && co2Value <= 1000) ||
                (hchoValue > 0.05 && hchoValue <= 0.08)) {
                return 2; // 良
            }

            // 其他情况（指标不在同一等级范围内）默认为"差"
            return 3; // 差
        },
        // 是否禁用
        disabled () {
            return !this.canControl;
        },

        // 判断当前数据类型是否有数据
        hasCurrentData () {
            if (this.type == 1) {
                // 环境质量概览：检查是否有统计数据
                return this.environmentStats.co2.avg !== null ||
                       this.environmentStats.hcho.avg !== null ||
                       this.environmentStats.heat.avg !== null ||
                       this.environmentStats.moisture.avg !== null;
            } else {
                // 图表页面：检查对应数据类型的图表数据
                const dataTypeMap = {
                    2: 'co2',
                    3: 'hcho',
                    4: 'heat',
                    5: 'moisture'
                };
                const dataType = dataTypeMap[this.type];
                const chartDataArray = this.chartData[dataType];
                return chartDataArray && Array.isArray(chartDataArray) && chartDataArray.length > 0 &&
                       chartDataArray.some(item => item !== null);
            }
        },

        // CO2等级计算（基于当前数据状态）
        Co2Level () {
            // 检查图表数据是否为空
            const chartDataArray = this.chartData.co2;
            const hasChartData = chartDataArray && Array.isArray(chartDataArray) && chartDataArray.length > 0;

            // 如果没有图表数据，返回null表示无数据状态
            if (!hasChartData) {
                return null;
            }

            // 获取当前CO2值进行等级判断
            let co2Value = 0;
            if (this.type == 1) {
                // 环境质量概览页面：使用统计平均值
                co2Value = this.environmentStats.co2.avg || 0;
                console.log('🎯 CO2等级计算 - 环境概览页面，使用平均值:', co2Value);
            } else {
                // 图表页面：使用最新数据或选中数据
                const selectedData = this.selectedChartData.co2;
                console.log('🎯 CO2等级计算 - 图表页面，选中数据:', selectedData);

                if (selectedData && selectedData.value) {
                    const value = selectedData.value;
                    if (typeof value === 'object' && value.min !== undefined && value.max !== undefined) {
                        co2Value = (value.min + value.max) / 2;
                        console.log('🎯 使用选中数据的区间平均值:', `(${value.min} + ${value.max}) / 2 = ${co2Value}`);
                    } else if (typeof value === 'number') {
                        co2Value = value;
                        console.log('🎯 使用选中数据的数值:', co2Value);
                    }
                } else if (chartDataArray.length > 0) {
                    const latestData = chartDataArray[chartDataArray.length - 1];
                    if (latestData && typeof latestData === 'object' && latestData.min !== undefined && latestData.max !== undefined) {
                        co2Value = (latestData.min + latestData.max) / 2;
                        console.log('🎯 使用最新数据的区间平均值:', `(${latestData.min} + ${latestData.max}) / 2 = ${co2Value}`);
                    } else if (latestData && typeof latestData === 'number') {
                        co2Value = latestData;
                        console.log('🎯 使用最新数据的数值:', co2Value);
                    }
                }
            }

            // 根据CO2值计算等级（使用i18n中的标准）
            let level;
            if (co2Value <= 1000) {
                level = 1; // 正常
            } else if (co2Value <= 2000) {
                level = 2; // 超标
            } else {
                level = 3; // 严重超标
            }

            // 简化等级计算日志
            console.log(`🎯 CO2: ${co2Value}ppm → 等级${level}`);

            return level;
        },

        // CO2超标状态的颜色类
        Co2LevelClassObject () {
            if (this.Co2Level === null) {
                return {}; // 无数据时不应用任何颜色类
            }
            return {
                'color-64BB5C': 0 < this.Co2Level && this.Co2Level <= 1,
                'color-F7CE00': 1 < this.Co2Level && this.Co2Level <= 2,
                'color-E84026': 2 < this.Co2Level && this.Co2Level <= 3
            }
        },
        // CO2状态文本
        Co2StatusText () {
            if (this.selectIndex === 0) {
                // 日报显示超标状态
                if (this.Co2Level === null) {
                    return '--'; // 无数据时显示--
                }
                if (this.Co2Level > 1) {
                    
                    return this.$t('level_exceeded');
                } else {
                    return this.$t('level_normal');
                }
            } else {
                // 周报和月报显示平均值
                return this.$t('average_value');
            }
        },
        // 甲醛等级计算（基于当前数据状态）
        HchoLevel () {
            // 检查图表数据是否为空
            const chartDataArray = this.chartData.hcho;
            const hasChartData = chartDataArray && Array.isArray(chartDataArray) && chartDataArray.length > 0;

            // 如果没有图表数据，返回null表示无数据状态
            if (!hasChartData) {
                return null;
            }

            // 获取当前甲醛值进行等级判断
            let hchoValue = 0;
            if (this.type == 1) {
                // 环境质量概览页面：使用统计平均值
                hchoValue = this.environmentStats.hcho.avg || 0;
            } else {
                // 图表页面：使用最新数据或选中数据
                const selectedData = this.selectedChartData.hcho;
                if (selectedData && selectedData.value) {
                    const value = selectedData.value;
                    if (typeof value === 'object' && value.min !== undefined && value.max !== undefined) {
                        hchoValue = (value.min + value.max) / 2;
                    } else if (typeof value === 'number') {
                        hchoValue = value;
                    }
                } else if (chartDataArray.length > 0) {
                    const latestData = chartDataArray[chartDataArray.length - 1];
                    if (latestData && typeof latestData === 'object' && latestData.min !== undefined && latestData.max !== undefined) {
                        hchoValue = (latestData.min + latestData.max) / 2;
                    } else if (latestData && typeof latestData === 'number') {
                        hchoValue = latestData;
                    }
                }
            }

            // 根据甲醛值计算等级（使用i18n中的标准）
            let level;
            if (hchoValue <= 0.08) {
                level = 1; // 正常
            } else if (hchoValue <= 0.2) {
                level = 2; // 超标
            } else {
                level = 3; // 严重超标
            }

            // 简化等级计算日志
            console.log(`🎯 甲醛: ${hchoValue}mg/m³ → 等级${level}`);

            return level;
        },
        // 甲醛超标状态的颜色类
        HchoLevelClassObject () {
            if (this.HchoLevel === null) {
                return {}; // 无数据时不应用任何颜色类
            }
            return {
                'color-64BB5C': this.HchoLevel === 1,
                'color-F7CE00': this.HchoLevel === 2,
                'color-E84026': this.HchoLevel === 3
            }
        },
        // 甲醛状态文本
        HchoStatusText () {
            if (this.selectIndex === 0) {
                // 日报显示超标状态
                if (this.HchoLevel === null) {
                    return '--'; // 无数据时显示--
                }
                if (this.HchoLevel > 1) {
                    return this.$t('level_exceeded');
                } else {
                    return this.$t('level_normal');
                }
            } else {
                // 周报和月报显示平均值
                return this.$t('average_value');
            }
        },

        // 湿度等级计算（基于当前数据状态）
        MoistureLevel () {
            // 检查图表数据是否为空
            const chartDataArray = this.chartData.moisture;
            const hasChartData = chartDataArray && Array.isArray(chartDataArray) && chartDataArray.length > 0;

            // 如果没有图表数据，返回null表示无数据状态
            if (!hasChartData) {
                return null;
            }

            // 获取当前湿度值进行等级判断
            let moistureValue = 0;
            if (this.type == 1) {
                // 环境质量概览页面：使用统计平均值
                moistureValue = this.environmentStats.moisture.avg || 0;
            } else {
                // 图表页面：使用最新数据或选中数据
                const selectedData = this.selectedChartData.moisture;
                if (selectedData && selectedData.value) {
                    const value = selectedData.value;
                    if (typeof value === 'object' && value.min !== undefined && value.max !== undefined) {
                        moistureValue = (value.min + value.max) / 2;
                    } else if (typeof value === 'number') {
                        moistureValue = value;
                    }
                } else if (chartDataArray.length > 0) {
                    const latestData = chartDataArray[chartDataArray.length - 1];
                    if (latestData && typeof latestData === 'object' && latestData.min !== undefined && latestData.max !== undefined) {
                        moistureValue = (latestData.min + latestData.max) / 2;
                    } else if (latestData && typeof latestData === 'number') {
                        moistureValue = latestData;
                    }
                }
            }

            // 根据湿度值计算等级（使用i18n中的标准）
            let level;
            if (moistureValue < 30) {
                level = 0; // 干燥
            } else if (moistureValue >= 30 && moistureValue <= 70) {
                level = 1; // 舒适
            } else {
                level = 2; // 潮湿
            }

            // 简化等级计算日志
            console.log(`🎯 湿度: ${moistureValue}% → 等级${level}`);

            return level;
        },
        // 湿度等级状态的颜色类
        MoistureeLevelClassObject () {
            if (this.MoistureLevel === null) {
                return {}; // 无数据时不应用任何颜色类
            }
            return {
                'moisture-level-dry': this.MoistureLevel === 0,        // 干燥 - 红色
                'moisture-level-comfortable': this.MoistureLevel === 1, // 舒适 - 绿色
                'moisture-level-humid': this.MoistureLevel === 2        // 潮湿 - 蓝色
            }
        },
        // 湿度状态文本
        MoistureStatusText () {
            if (this.selectIndex === 0) {
                // 日报显示舒适状态
                if (this.MoistureLevel === null) {
                    return '--'; // 无数据时显示--
                }
                if (this.MoistureLevel === 0) {
                    return this.$t('moisture_level_dry');
                } else if (this.MoistureLevel === 1) {
                    return this.$t('moisture_level_comfortable');
                } else {
                    return this.$t('moisture_level_humid');
                }
            } else {
                // 周报和月报显示平均值
                return this.$t('average_value');
            }
        }
    },
    async activated () {
        console.log("activated")
        this.type = this.$route.query.type
        console.log(this.$route.query.type)
        this.title =
            this.$route.query.type == 1
                ? this.$t("enviroment_title")
                : this.$route.query.type == 2
                    ? this.$t("co2_title")
                    : this.$route.query.type == 3
                        ? this.$t("hcho_title")
                        : this.$route.query.type == 4
                            ? this.$t("temperature_title")
                            : this.$t("humidity_title");



        // 初始化日期选择器
        this.initializeDates();

        // 获取统计数据
        this.$nextTick(() => {
            this.fetchDeviceStatistics();
        });
    },
    created () {
        this.width1 = this.measureText(this.$t("daily_report"), 1.6).width;
        this.width2 = this.measureText(this.$t("weekly_report"), 1.6).width;
        this.width3 = this.measureText(this.$t("monthly_report"), 1.6).width;
        this.width4 = this.measureText(this.$t("yearly_report"), 1.6).width;
    },
    methods: {
        toggle (index) {
            const previousIndex = this.selectIndex;
            this.selectIndex = index;

            // 智能切换日期：基于当前选中的日期计算对应的周期
            this.calculateDateForTab(previousIndex, index);

            // 切换报表类型后重新获取数据
            this.$nextTick(() => {
                this.fetchDeviceStatistics();
            });
        },
        measureText (pText, pFontSize) {
            var lDiv = document.createElement("div");
            document.body.appendChild(lDiv);
            lDiv.style.fontSize = pFontSize + "rem";
            lDiv.style.position = "absolute";
            lDiv.style.left = -1000;
            lDiv.style.top = -1000;
            lDiv.innerHTML = pText;
            var lResult = {
                width: lDiv.clientWidth,
                height: lDiv.clientHeight,
            };
            document.body.removeChild(lDiv);
            lDiv = null;
            return lResult;
        },
        // 初始化日期（仅在组件初始化时调用）
        initializeDates () {
            const today = new Date();
            this.selectedDate = new Date(today);
            this.tempSelectedDate = new Date(today);
            this.selectedMonth = new Date(today);
            this.tempSelectedMonth = new Date(today);
            this.selectedYear = new Date(today.getFullYear(), 0, 1);

            // 初始化周报相关数据
            this.selectedWeekDate = new Date(today);
            const startOfWeek = this.getStartOfWeek(today);
            const endOfWeek = new Date(startOfWeek);
            endOfWeek.setDate(startOfWeek.getDate() + 6);
            this.selectedWeek = [startOfWeek, endOfWeek];
            this.tempSelectedWeek = [new Date(startOfWeek), new Date(endOfWeek)];
        },

        // 智能计算切换tab时的日期
        calculateDateForTab (previousIndex, newIndex) {
            // 获取基准日期（优先使用日报的选中日期）
            let baseDate;

            if (previousIndex === 0) {
                // 从日报切换：使用日报选中的日期
                baseDate = new Date(this.selectedDate);
            } else if (previousIndex === 1 && this.selectedWeek.length === 2) {
                // 从周报切换：使用周报开始日期
                baseDate = new Date(this.selectedWeek[0]);
            } else if (previousIndex === 2) {
                // 从月报切换：使用月报选中日期（如果是月份，则使用该月第一天）
                baseDate = new Date(this.selectedMonth);
                if (baseDate.getDate() !== 1) {
                    baseDate = new Date(baseDate.getFullYear(), baseDate.getMonth(), 1);
                }
            } else if (previousIndex === 3) {
                // 从年报切换：使用年报选中年份的第一天
                baseDate = new Date(this.selectedYear.getFullYear(), 0, 1);
            } else {
                // 默认使用当前日报日期
                baseDate = new Date(this.selectedDate);
            }

            // 根据新的tab类型计算对应的日期
            switch (newIndex) {
                case 0: // 切换到日报
                    if (previousIndex === 1 && this.selectedWeek.length === 2) {
                        // 从周报切换到日报：使用周的开始日期
                        this.selectedDate = new Date(this.selectedWeek[0]);
                    } else if (previousIndex === 2) {
                        // 从月报切换到日报：使用月份的第一天
                        this.selectedDate = new Date(this.selectedMonth.getFullYear(), this.selectedMonth.getMonth(), 1);
                    } else if (previousIndex === 3) {
                        // 从年报切换到日报：使用年份的第一天
                        this.selectedDate = new Date(this.selectedYear.getFullYear(), 0, 1);
                    }
                    // 如果是从日报切换到日报，保持不变
                    break;

                case 1: // 切换到周报
                    // 周报现在使用单日选择模式，保存基准日期
                    // 系统会自动计算该日期所在的周范围
                    this.selectedWeekDate = new Date(baseDate);
                    // 同时计算周范围用于显示
                    const weekStart = this.getStartOfWeek(baseDate);
                    const weekEnd = new Date(weekStart);
                    weekEnd.setDate(weekStart.getDate() + 6);
                    this.selectedWeek = [weekStart, weekEnd];
                    this.tempSelectedWeek = [new Date(weekStart), new Date(weekEnd)];
                    break;

                case 2: // 切换到月报
                    // 计算baseDate所在的月
                    this.selectedMonth = new Date(baseDate.getFullYear(), baseDate.getMonth(), 1);
                    this.tempSelectedMonth = new Date(this.selectedMonth);
                    break;

                case 3: // 切换到年报
                    // 计算baseDate所在的年
                    this.selectedYear = new Date(baseDate.getFullYear(), 0, 1);
                    break;
            }
        },
        // 获取一周的开始日期（周一）
        getStartOfWeek (date) {
            const d = new Date(date);
            const day = d.getDay();
            const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
            return new Date(d.setDate(diff));
        },
        // 显示日期选择器
        async showDatePicker () {
            // 防止重复点击
            if (this.showDatePickerPopup) return;

            this.showDatePickerPopup = true;
        },
        // 取消日期选择
        cancelDatePicker () {
            this.showDatePickerPopup = false;
        },
        // 确认日期选择
        confirmDatePicker (result) {
            switch (this.selectIndex) {
                case 0: // 日报
                    this.selectedDate = new Date(result);
                    // 同步更新临时状态
                    this.tempSelectedDate = new Date(result);
                    break;
                case 1: // 周报
                    // result 仍然是周范围数组，但我们也要保存选中的具体日期
                    this.selectedWeek = [...result];
                    this.selectedWeekDate = new Date(result[0]); // 使用周的开始日期作为选中日期
                    // 同步更新临时状态
                    this.tempSelectedWeek = [new Date(result[0]), new Date(result[1])];
                    break;
                case 2: // 月报
                    this.selectedMonth = new Date(result);
                    // 同步更新临时状态
                    this.tempSelectedMonth = new Date(result);
                    break;
                case 3: // 年报
                    this.selectedYear = new Date(result);
                    break;
            }
            this.showDatePickerPopup = false;
            // 触发数据刷新
            this.refreshData();
        },
        // 获取日期选择器模式
        getDatePickerMode () {
            switch (this.selectIndex) {
                case 0:
                    return 'daily';
                case 1:
                    return 'weekly';
                case 2:
                    return 'monthly';
                case 3:
                    return 'yearly';
                default:
                    return 'daily';
            }
        },
        // 获取默认值
        getDefaultValue () {
            switch (this.selectIndex) {
                case 0:
                    return this.selectedDate;
                case 1:
                    // 周报模式：返回选中的具体日期，让日期选择器显示该日期
                    return this.selectedWeekDate || this.selectedDate;
                case 2:
                    return this.selectedMonth;
                case 3:
                    return this.selectedYear;
                default:
                    return new Date();
            }
        },
        // 获取日期选择器标题
        getDatePickerTitle () {
            switch (this.selectIndex) {
                case 0:
                    return this.$t('select_date');
                case 1:
                    return this.$t('select_week');
                case 2:
                    return this.$t('select_month');
                case 3:
                    return this.$t('select_year');
                default:
                    return '';
            }
        },
        // 格式化日期
        formatDate (date, type) {
            if (!date) return '';

            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');

            switch (type) {
                case 'daily':
                    return `${year}年${month}月${day}日`;
                case 'monthly':
                    return `${year}年${month}月`;
                case 'yearly':
                    return `${year}年`;
                default:
                    return `${year}-${month}-${day}`;
            }
        },
        // 格式化日期范围
        formatDateRange (startDate, endDate) {
            if (!startDate || !endDate) return this.$t('this_week');

            const startYear = startDate.getFullYear();
            const startMonth = String(startDate.getMonth() + 1).padStart(2, '0');
            const startDay = String(startDate.getDate()).padStart(2, '0');

            const endYear = endDate.getFullYear();
            const endMonth = String(endDate.getMonth() + 1).padStart(2, '0');
            const endDay = String(endDate.getDate()).padStart(2, '0');

            if (startYear === endYear && startMonth === endMonth) {
                return `${startYear}年${startMonth}月${startDay}日-${endDay}日`;
            } else if (startYear === endYear) {
                return `${startYear}年${startMonth}月${startDay}日-${endMonth}月${endDay}日`;
            } else {
                return `${startYear}年${startMonth}月${startDay}日-${endYear}年${endMonth}月${endDay}日`;
            }
        },
        // 刷新数据
        refreshData () {
            console.log('刷新数据', {
                selectIndex: this.selectIndex,
                selectedDate: this.selectedDate,
                selectedWeek: this.selectedWeek,
                selectedMonth: this.selectedMonth
            });

            // 获取当前传感器类型的统计数据
            this.fetchDeviceStatistics();
        },

        // 获取环境质量统计数据（4个传感器的平均值）
        async fetchEnvironmentStatistics () {
            console.log(`获取环境质量统计数据 - 报表类型: ${this.selectIndex}`);

            // 获取时间范围
            const timeRange = this.getTimeRange();
            if (!timeRange) {
                console.error('无法获取时间范围');
                return;
            }

            // 确定精度参数
            const accuracy = this.selectIndex === 0 ? 'HOUR' : 'DAILY';

            // 4个传感器配置
            const sensors = [
                { sid: 'co2', character: 'current', dataType: 'co2' },
                { sid: 'hcho', character: 'currentFloat', dataType: 'hcho' },
                { sid: 'heat', character: 'currentFloat', dataType: 'heat' },
                { sid: 'moisture', character: 'current', dataType: 'moisture' }
            ];

            // 设置全局loading状态
            this.isGlobalLoading = true;

            try {

                // 串行获取4个传感器的统计数据（逐个请求，避免并发问题）
                console.log('开始获取4个传感器数据，传感器配置:', sensors);

                for (let i = 0; i < sensors.length; i++) {
                    const sensor = sensors[i];

                    // 给每个请求添加递增延迟：0ms, 200ms, 400ms, 600ms
                    const delay = i * 200;
                    if (delay > 0) {
                        console.log(`${sensor.dataType} 等待 ${delay}ms 后开始请求`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }

                    console.log(`开始获取 ${sensor.dataType} 数据，sid: ${sensor.sid}, character: ${sensor.character}`);

                    try {
                        // 设置加载状态
                        this.$set(this.environmentStats[sensor.dataType], 'loading', true);

                        const response = await this.$store.dispatch('getDeviceStatistics', {
                            startTime: Math.floor(timeRange.startTime.getTime() / 1000), // 转换为秒级时间戳
                            endTime: Math.floor(timeRange.endTime.getTime() / 1000),     // 转换为秒级时间戳
                            sid: sensor.sid,
                            character: sensor.character,
                            accuracy: accuracy,
                            pageSize: 100
                        });

                        console.log(`${sensor.dataType} 环境统计API响应:`, response);

                        // 处理API返回的数据，计算平均值
                        if (response && response.devDatas && Array.isArray(response.devDatas)) {
                            const avgValue = this.calculateEnvironmentAverage(response.devDatas);
                            this.$set(this.environmentStats[sensor.dataType], 'avg', avgValue);
                            console.log(`${sensor.dataType} 计算平均值:`, avgValue);
                        } else {
                            console.warn(`${sensor.dataType} API返回数据格式异常`);
                            this.$set(this.environmentStats[sensor.dataType], 'avg', null);
                        }

                    } catch (error) {
                        console.error(`获取${sensor.dataType}统计数据失败:`, error);
                        console.error(`失败的传感器配置:`, sensor);
                        this.$set(this.environmentStats[sensor.dataType], 'avg', null);
                    } finally {
                        console.log(`${sensor.dataType} 数据获取完成，设置loading为false`);
                        this.$set(this.environmentStats[sensor.dataType], 'loading', false);
                    }
                }

                console.log('所有传感器数据获取完成，最终结果:', this.environmentStats);

            } catch (error) {
                console.error('获取环境质量统计数据失败:', error);
            } finally {
                // 关闭全局loading状态
                this.isGlobalLoading = false;
            }
        },

        // 计算环境数据平均值
        calculateEnvironmentAverage (devDatas) {
            if (!devDatas || devDatas.length === 0) {
                return null;
            }

            let totalSum = 0;
            let totalCount = 0;

            devDatas.forEach(item => {
                const statsValue = this.extractStatisticsValue(item);
                if (statsValue && statsValue.avg !== null) {
                    totalSum += statsValue.avg;
                    totalCount++;
                }
            });

            return totalCount > 0 ? totalSum / totalCount : null;
        },

        // 获取设备统计数据（用于图表显示）
        async fetchDeviceStatistics () {
            // 环境质量概览页面使用专门的方法获取4个传感器数据
            if (this.type == 1) {
                await this.fetchEnvironmentStatistics();
                return;
            }

            // 只有在有传感器配置的情况下才获取数据
            if (!this.sensorConfig[this.type]) {
                console.log('当前类型不支持统计数据获取:', this.type);
                return;
            }

            const config = this.sensorConfig[this.type];
            console.log(`获取 ${config.dataType} 统计数据 - 报表类型: ${this.selectIndex}`);

            // 设置加载状态
            this.isLoadingData = true;

            try {
                // 获取时间范围
                const timeRange = this.getTimeRange();
                if (!timeRange) {
                    console.error('无法获取时间范围');
                    this.$set(this.chartData, config.dataType, []);
                    return;
                }

                // 确定精度参数
                const accuracy = this.selectIndex === 0 ? 'HOUR' : 'DAILY';
                console.log(`📊 精度参数确定: selectIndex=${this.selectIndex}, accuracy=${accuracy}`);

                // 构建API请求参数
                const apiParams = {
                    startTime: Math.floor(timeRange.startTime.getTime() / 1000), // 转换为秒级时间戳
                    endTime: Math.floor(timeRange.endTime.getTime() / 1000),     // 转换为秒级时间戳
                    sid: config.sid,
                    character: config.character,
                    accuracy: accuracy,
                    pageSize: 100
                };

                console.log(`🚀 ${config.dataType} API请求参数详情:`, {
                    dataType: config.dataType,
                    selectIndex: this.selectIndex,
                    accuracy: accuracy,
                    fullParams: apiParams
                });

                // 调用API获取统计数据
                const response = await this.$store.dispatch('getDeviceStatistics', apiParams);

                console.log(`${config.dataType} API响应:`, response);

                // 处理API返回的数据
                if (response && response.devDatas && Array.isArray(response.devDatas)) {
                    console.log(`🔍 ${config.dataType} API返回原始数据:`, response.devDatas);
                    const processedData = this.processStatisticsData(response.devDatas);
                    console.log(`🔄 ${config.dataType} 处理后数据:`, processedData);
                    this.updateChartWithData(config.dataType, processedData);
                } else {
                    console.warn(`❌ ${config.dataType} API返回数据格式异常:`, response);
                    this.$set(this.chartData, config.dataType, []);
                    // 同时清空选中的图表数据，避免显示之前的值
                    this.$set(this.selectedChartData, config.dataType, null);
                }

            } catch (error) {
                console.error('获取统计数据失败:', error);
                // API调用失败时清空图表数据
                this.$set(this.chartData, config.dataType, []);
            } finally {
                this.isLoadingData = false;
            }
        },



        // 获取时间范围
        getTimeRange () {
            let startTime, endTime;

            switch (this.selectIndex) {
                case 0: // 日报 - 动态调整17:00时间范围
                    const now = new Date();
                    const selectedDate = new Date(this.selectedDate);

                    // 判断是否是今天且当前时间已过17:00
                    const isToday = now.toDateString() === selectedDate.toDateString();
                    const currentHour = now.getHours();

                    if (isToday && currentHour >= 17) {
                        // 今天17:00以后：获取今天17:00到明天17:00的数据
                        startTime = new Date(selectedDate);
                        startTime.setHours(17, 0, 0, 0); // 今天17:00:00
                        endTime = new Date(selectedDate);
                        endTime.setDate(endTime.getDate() + 1); // 明天
                        endTime.setHours(17, 0, 0, 0); // 明天17:00:00
                        console.log('📅 日报时间范围（17:00后）:', {
                            startTime: startTime.toLocaleString(),
                            endTime: endTime.toLocaleString(),
                            currentHour: currentHour
                        });
                    } else {
                        // 今天17:00前或非今天：获取昨天17:00到今天17:00的数据
                        startTime = new Date(selectedDate);
                        startTime.setDate(startTime.getDate() - 1); // 前一天
                        startTime.setHours(17, 0, 0, 0); // 昨天17:00:00
                        endTime = new Date(selectedDate);
                        endTime.setHours(17, 0, 0, 0); // 今天17:00:00
                        console.log('📅 日报时间范围（17:00前）:', {
                            startTime: startTime.toLocaleString(),
                            endTime: endTime.toLocaleString(),
                            currentHour: currentHour,
                            isToday: isToday
                        });
                    }
                    break;

                case 1: // 周报
                    if (this.selectedWeek.length === 2) {
                        startTime = new Date(this.selectedWeek[0]);
                        startTime.setHours(0, 0, 0, 0);
                        endTime = new Date(this.selectedWeek[1]);
                        endTime.setHours(23, 59, 59, 999);
                    } else {
                        return null;
                    }
                    break;

                case 2: // 月报
                    startTime = new Date(this.selectedMonth.getFullYear(), this.selectedMonth.getMonth(), 1);
                    startTime.setHours(0, 0, 0, 0);
                    endTime = new Date(this.selectedMonth.getFullYear(), this.selectedMonth.getMonth() + 1, 0);
                    endTime.setHours(23, 59, 59, 999);
                    break;

                case 3: // 年报
                    startTime = new Date(this.selectedYear.getFullYear(), 0, 1);
                    startTime.setHours(0, 0, 0, 0);
                    endTime = new Date(this.selectedYear.getFullYear(), 11, 31);
                    endTime.setHours(23, 59, 59, 999);
                    break;

                default:
                    return null;
            }

            return { startTime, endTime };
        },

        // 格式化为UTC时间字符串
        formatToUTC (date) {
            if (!date) return '';

            const year = date.getUTCFullYear();
            const month = String(date.getUTCMonth() + 1).padStart(2, '0');
            const day = String(date.getUTCDate()).padStart(2, '0');
            const hours = String(date.getUTCHours()).padStart(2, '0');
            const minutes = String(date.getUTCMinutes()).padStart(2, '0');
            const seconds = String(date.getUTCSeconds()).padStart(2, '0');

            return `${year}${month}${day}T${hours}${minutes}${seconds}Z`;
        },

        // 处理统计数据
        processStatisticsData (dataList) {
            console.log('开始处理统计数据:', dataList);

            if (!dataList || dataList.length === 0) {
                console.warn('统计数据为空');
                return [];
            }

            try {
                // 统计数据格式: { time, max, min, avg, sum, accuracy, count }
                const processedData = dataList.map((item, index) => {
                    console.log(`处理数据项 ${index}:`, item);

                    try {
                        const statsValue = this.extractStatisticsValue(item);
                        if (!statsValue) {
                            console.warn(`数据项 ${index} 提取统计值失败`);
                            return null;
                        }

                        const timestamp = this.parseStatisticsTime(item.time);
                        if (!timestamp) {
                            console.warn(`数据项 ${index} 时间解析失败:`, item.time);
                            return null;
                        }

                        const result = {
                            timestamp: timestamp,
                            value: statsValue.avg, // 使用平均值作为主要显示数据
                            min: statsValue.min,
                            max: statsValue.max,
                            count: statsValue.count
                        };

                        // 移除详细的数据项日志，避免性能问题
                        return result;
                    } catch (error) {
                        console.error(`处理数据项 ${index} 时出错:`, error, item);
                        return null;
                    }
                }).filter(item => item !== null);

                console.log('过滤后的处理数据:', processedData);

                if (processedData.length === 0) {
                    console.warn('没有有效的统计数据');
                    return [];
                }

                // 生成完整的时间序列（填补缺失的时间点）
                console.log('开始填补缺失时间点...');
                const completeData = this.fillMissingTimePoints(processedData);
                console.log('填补后的完整数据:', completeData);

                // 根据报表类型格式化数据
                console.log('开始格式化统计数据...');
                const formattedData = this.formatStatisticsData(completeData);
                console.log('格式化后的数据:', formattedData);

                return formattedData;
            } catch (error) {
                console.error('processStatisticsData 处理过程中出错:', error);
                return [];
            }
        },

        // 处理历史数据（保留原方法以防需要）
        processHistoryData (dataList, dataType) {
            if (!dataList || dataList.length === 0) {
                console.warn('历史数据为空');
                return [];
            }

            // 解析时间戳并提取数值
            const parsedData = dataList.map(item => {
                const timestamp = this.parseUTCTimestamp(item.timestamp);
                const value = this.extractSensorValue(item.data, dataType);
                return { timestamp, value };
            }).filter(item => item.timestamp && item.value !== null);

            if (parsedData.length === 0) {
                console.warn('没有有效的历史数据');
                return [];
            }

            // 根据报表类型进行数据聚合
            return this.aggregateData(parsedData);
        },

        // 解析统计数据时间字符串
        parseStatisticsTime (timeStr) {
            if (!timeStr) return null;

            try {
                // 时间格式可能是 yyyyMMddhh (小时粒度) 或 yyyyMMdd (日粒度)
                const timeString = String(timeStr);

                let result = null;
                if (timeString.length === 10) {
                    // yyyyMMddhh 格式
                    const year = parseInt(timeString.substring(0, 4));
                    const month = parseInt(timeString.substring(4, 6)) - 1; // 月份从0开始
                    const day = parseInt(timeString.substring(6, 8));
                    const hour = parseInt(timeString.substring(8, 10));
                    result = new Date(year, month, day, hour, 0, 0, 0);
                } else if (timeString.length === 8) {
                    // yyyyMMdd 格式
                    const year = parseInt(timeString.substring(0, 4));
                    const month = parseInt(timeString.substring(4, 6)) - 1; // 月份从0开始
                    const day = parseInt(timeString.substring(6, 8));
                    result = new Date(year, month, day, 0, 0, 0, 0);
                } else {
                    console.warn('未知的时间格式:', timeString);
                    return null;
                }

                // 移除时间解析的详细日志

                return result;
            } catch (error) {
                console.error('统计时间解析失败:', timeStr, error);
                return null;
            }
        },

        // 解析UTC时间戳
        parseUTCTimestamp (timestampStr) {
            if (!timestampStr) return null;

            try {
                // 处理格式：yyyyMMddTHHmmssZ
                const year = parseInt(timestampStr.substr(0, 4));
                const month = parseInt(timestampStr.substr(4, 2)) - 1; // 月份从0开始
                const day = parseInt(timestampStr.substr(6, 2));
                const hours = parseInt(timestampStr.substr(9, 2));
                const minutes = parseInt(timestampStr.substr(11, 2));
                const seconds = parseInt(timestampStr.substr(13, 2));

                return new Date(Date.UTC(year, month, day, hours, minutes, seconds));
            } catch (error) {
                console.error('时间戳解析失败:', timestampStr, error);
                return null;
            }
        },

        // 提取传感器数值
        extractSensorValue (data, dataType) {
            if (!data) return null;

            try {
                const parsedData = typeof data === 'string' ? JSON.parse(data) : data;

                // 根据传感器类型提取对应的数值
                switch (dataType) {
                    case 'co2':
                        return parsedData.co2 && parsedData.co2.current ? parsedData.co2.current : null;
                    case 'hcho':
                        return parsedData.hcho && parsedData.hcho.currentFloat ? parsedData.hcho.currentFloat : null;
                    case 'heat':
                        return parsedData.heat && parsedData.heat.current ? parsedData.heat.current : null;
                    case 'moisture':
                        return parsedData.moisture && parsedData.moisture.current ? parsedData.moisture.current : null;
                    default:
                        return null;
                }
            } catch (error) {
                console.error('数据解析失败:', data, error);
                return null;
            }
        },

        // 数据聚合
        aggregateData (parsedData) {
            switch (this.selectIndex) {
                case 0: // 日报：4个区间（每6小时）
                    return this.aggregateByIntervals(parsedData, 4);
                case 1: // 周报：7天数据
                    return this.aggregateByDays(parsedData, 7);
                case 2: // 月报：6-7个区间（每4-5天）
                    return this.aggregateByIntervals(parsedData, 6);
                case 3: // 年报：12个月
                    return this.aggregateByMonths(parsedData, 12);
                default:
                    return [];
            }
        },

        // 按区间聚合数据
        aggregateByIntervals (data, intervalCount) {
            if (data.length === 0) return [];

            const timeRange = this.getTimeRange();
            const totalDuration = timeRange.endTime.getTime() - timeRange.startTime.getTime();
            const intervalDuration = totalDuration / intervalCount;

            const result = [];
            for (let i = 0; i < intervalCount; i++) {
                const intervalStart = new Date(timeRange.startTime.getTime() + i * intervalDuration);
                const intervalEnd = new Date(timeRange.startTime.getTime() + (i + 1) * intervalDuration);

                const intervalData = data.filter(item =>
                    item.timestamp >= intervalStart && item.timestamp < intervalEnd
                );

                if (intervalData.length > 0) {
                    const values = intervalData.map(item => item.value);
                    const min = Math.min(...values);
                    const max = Math.max(...values);
                    result.push({ min, max });
                } else {
                    // 没有数据的区间使用默认值
                    result.push({ min: 0, max: 0 });
                }
            }

            return result;
        },

        // 按天聚合数据
        aggregateByDays (data, dayCount) {
            if (data.length === 0) return [];

            const timeRange = this.getTimeRange();
            const result = [];

            for (let i = 0; i < dayCount; i++) {
                const dayStart = new Date(timeRange.startTime);
                dayStart.setDate(dayStart.getDate() + i);
                dayStart.setHours(0, 0, 0, 0);

                const dayEnd = new Date(dayStart);
                dayEnd.setHours(23, 59, 59, 999);

                const dayData = data.filter(item =>
                    item.timestamp >= dayStart && item.timestamp <= dayEnd
                );

                if (dayData.length > 0) {
                    const values = dayData.map(item => item.value);
                    const min = Math.min(...values);
                    const max = Math.max(...values);
                    result.push({ min, max });
                } else {
                    result.push({ min: 0, max: 0 });
                }
            }

            return result;
        },

        // 按月聚合数据
        aggregateByMonths (data, monthCount) {
            if (data.length === 0) return [];

            const timeRange = this.getTimeRange();
            const result = [];

            for (let i = 0; i < monthCount; i++) {
                const monthStart = new Date(timeRange.startTime.getFullYear(), i, 1);
                const monthEnd = new Date(timeRange.startTime.getFullYear(), i + 1, 0, 23, 59, 59, 999);

                const monthData = data.filter(item =>
                    item.timestamp >= monthStart && item.timestamp <= monthEnd
                );

                if (monthData.length > 0) {
                    const values = monthData.map(item => item.value);
                    const min = Math.min(...values);
                    const max = Math.max(...values);
                    result.push({ min, max });
                } else {
                    result.push({ min: 0, max: 0 });
                }
            }

            return result;
        },

        // 提取统计数据数值
        extractStatisticsValue (item) {
            if (!item) return null;

            try {
                // 统计数据格式: { time, max, min, avg, sum, accuracy, count }
                // 我们主要使用 avg (平均值) 作为显示数据
                let avgValue = parseFloat(item.avg);
                let maxValue = parseFloat(item.max);
                let minValue = parseFloat(item.min);

                // 检查是否有NaN值
                if (isNaN(avgValue) || isNaN(maxValue) || isNaN(minValue)) {
                    console.warn('⚠️ 数据解析出现NaN:', {
                        原始数据: item,
                        avg: item.avg, avgValue: avgValue, avgIsNaN: isNaN(avgValue),
                        max: item.max, maxValue: maxValue, maxIsNaN: isNaN(maxValue),
                        min: item.min, minValue: minValue, minIsNaN: isNaN(minValue)
                    });
                }

                // 甲醛数据不再需要单位转换，直接使用原始值

                // 返回包含统计信息的对象
                return {
                    avg: isNaN(avgValue) ? null : avgValue,
                    max: isNaN(maxValue) ? null : maxValue,
                    min: isNaN(minValue) ? null : minValue,
                    time: item.time,
                    count: item.count
                };
            } catch (error) {
                console.error('统计数据解析失败:', item, error);
                return null;
            }
        },

        // 格式化统计数据
        formatStatisticsData (processedData) {
            if (!Array.isArray(processedData)) {
                console.error('formatStatisticsData: processedData不是数组', processedData);
                return [];
            }

            try {
                // 所有报表类型都返回区间数据，使用实际的min和max值
                return processedData.map((item, index) => {
                    if (!item) {
                        // null 项保持为 null，用于表示缺失数据
                        return null;
                    }

                    try {
                        // 确保min和max都是有效数值
                        let minValue = item.min;
                        let maxValue = item.max;

                        // 如果min或max无效，尝试使用value作为备选
                        if (minValue === null || minValue === undefined || isNaN(minValue)) {
                            minValue = item.value || 0;
                        }
                        if (maxValue === null || maxValue === undefined || isNaN(maxValue)) {
                            maxValue = item.value || minValue || 0;
                        }

                        // 确保max不小于min
                        if (maxValue < minValue) {
                            maxValue = minValue;
                        }

                        // 检查最终结果是否有NaN
                        if (isNaN(minValue) || isNaN(maxValue)) {
                            console.error(`⚠️ 格式化后仍有NaN值 - 数据项 ${index}:`, {
                                原始数据: item,
                                minValue: minValue, minIsNaN: isNaN(minValue),
                                maxValue: maxValue, maxIsNaN: isNaN(maxValue)
                            });
                            return null; // 返回null而不是包含NaN的对象
                        }

                        return {
                            min: minValue,
                            max: maxValue
                        };
                    } catch (error) {
                        console.error(`格式化数据项 ${index} 时出错:`, error, item);
                        return null;
                    }
                });
            } catch (error) {
                console.error('formatStatisticsData 处理过程中出错:', error);
                return [];
            }
        },



        // 更新图表数据
        updateChartWithData (dataType, processedData) {
            console.log(`📊 更新图表数据 - ${dataType}:`, processedData);

            if (!processedData || processedData.length === 0) {
                console.warn(`❌ ${dataType} 处理后的数据为空，清空图表数据`);
                this.$set(this.chartData, dataType, []);
                // 同时清空选中的图表数据，避免显示之前的值
                this.$set(this.selectedChartData, dataType, null);
                return;
            }

            // 确保传递给图表的是有效数组
            if (!Array.isArray(processedData)) {
                console.error(`❌ ${dataType} processedData不是数组:`, processedData);
                this.$set(this.chartData, dataType, []);
                return;
            }

            // 移除CO2数据的详细分析日志，避免性能问题

            this.$set(this.chartData, dataType, processedData);
            console.log(`✅ ${dataType} 图表数据已更新，长度:`, processedData.length);

            // 验证数据是否正确设置
            console.log(`🔍 验证 this.chartData.${dataType}:`, this.chartData[dataType]);
        },

        // 填补缺失的时间点（基于固定时间轴）
        fillMissingTimePoints (processedData) {
            console.log('fillMissingTimePoints 输入数据:', processedData);

            if (!processedData || processedData.length === 0) {
                console.log('没有数据，返回空数组');
                return [];
            }

            if (!Array.isArray(processedData)) {
                console.error('fillMissingTimePoints: processedData不是数组', processedData);
                return [];
            }

            try {
                // 根据报表类型处理数据位置对应
                console.log(`根据报表类型 ${this.selectIndex} 填补数据`);

                let result;
                switch (this.selectIndex) {
                    case 0: // 日报 - 25小时
                        result = this.fillDailyData(processedData);
                        break;
                    case 1: // 周报 - 7天
                        result = this.fillWeeklyData(processedData);
                        break;
                    case 2: // 月报 - 当月天数
                        result = this.fillMonthlyData(processedData);
                        break;
                    case 3: // 年报 - 12个月
                        result = this.fillYearlyData(processedData);
                        break;
                    default:
                        result = processedData;
                }

                console.log('fillMissingTimePoints 输出结果:', result);
                return result;
            } catch (error) {
                console.error('fillMissingTimePoints 处理过程中出错:', error);
                return processedData; // 出错时返回原始数据
            }
        },

        // 填充日报数据（25小时：动态17:00时间范围）
        fillDailyData (processedData) {
            console.log('🕐 开始填充日报数据，原始数据:', processedData);

            const filledData = new Array(25).fill(null);

            // 获取时间范围来判断是哪种模式
            const timeRange = this.getTimeRange();
            const startDate = new Date(timeRange.startTime);
            const endDate = new Date(timeRange.endTime);

            // 判断是否是17:00后模式（今天17:00到明天17:00）
            const now = new Date();
            const selectedDate = new Date(this.selectedDate);
            const isToday = now.toDateString() === selectedDate.toDateString();
            const currentHour = now.getHours();
            const isAfter17Mode = isToday && currentHour >= 17;

            console.log('🕐 日报填充模式:', {
                isAfter17Mode: isAfter17Mode,
                startTime: startDate.toLocaleString(),
                endTime: endDate.toLocaleString()
            });

            processedData.forEach((item) => {
                if (item && item.timestamp) {
                    const hour = item.timestamp.getHours();
                    const itemDateStr = item.timestamp.toDateString();
                    const startDateStr = startDate.toDateString();
                    const endDateStr = endDate.toDateString();

                    let arrayIndex;

                    if (isAfter17Mode) {
                        // 17:00后模式：今天17:00到明天17:00
                        if (itemDateStr === startDateStr) {
                            // 今天的数据：17:00-23:00 对应索引 0-6
                            if (hour >= 17) {
                                arrayIndex = hour - 17;
                            } else {
                                return; // 跳过这个数据项
                            }
                        } else if (itemDateStr === endDateStr) {
                            // 明天的数据：00:00-17:00
                            if (hour <= 16) {
                                // 明天00:00-16:00 对应索引 7-23
                                arrayIndex = hour + 7;
                            } else if (hour === 17) {
                                // 明天17:00 对应索引 24
                                arrayIndex = 24;
                            } else {
                                return; // 跳过这个数据项
                            }
                        } else {
                            return; // 跳过不在范围内的数据
                        }
                    } else {
                        // 17:00前模式：昨天17:00到今天17:00
                        if (itemDateStr === startDateStr) {
                            // 昨天的数据：17:00-23:00 对应索引 0-6
                            if (hour >= 17) {
                                arrayIndex = hour - 17;
                            } else {
                                return; // 跳过这个数据项
                            }
                        } else if (itemDateStr === endDateStr) {
                            // 今天的数据：00:00-17:00
                            if (hour <= 16) {
                                // 今天00:00-16:00 对应索引 7-23
                                arrayIndex = hour + 7;
                            } else if (hour === 17) {
                                // 今天17:00 对应索引 24
                                arrayIndex = 24;
                            } else {
                                return; // 跳过这个数据项
                            }
                        } else {
                            return; // 跳过不在范围内的数据
                        }
                    }

                    if (arrayIndex >= 0 && arrayIndex < 25) {
                        filledData[arrayIndex] = item;
                    }
                }
            });

            console.log('🕐 日报数据填充完成:', filledData);
            return filledData;
        },

        // 填充周报数据（7天）
        fillWeeklyData (processedData) {
            const filledData = new Array(7).fill(null);

            processedData.forEach(item => {
                if (item.timestamp) {
                    // 计算在一周中的位置
                    const timeRange = this.getTimeRange();
                    if (timeRange) {
                        const startTime = new Date(timeRange.startTime);
                        const daysDiff = Math.floor((item.timestamp - startTime) / (1000 * 60 * 60 * 24));
                        if (daysDiff >= 0 && daysDiff < 7) {
                            filledData[daysDiff] = item;
                        }
                    }
                }
            });

            return filledData;
        },

        // 填充月报数据（当月天数）
        fillMonthlyData (processedData) {
            if (!Array.isArray(processedData)) {
                console.warn('fillMonthlyData: processedData不是数组', processedData);
                return [];
            }

            const currentDate = new Date(this.selectedDate);
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();
            const daysInMonth = new Date(year, month + 1, 0).getDate();

            console.log(`月报数据填充: ${year}年${month + 1}月，共${daysInMonth}天`);

            const filledData = new Array(daysInMonth).fill(null);

            processedData.forEach((item, index) => {
                if (item && item.timestamp) {
                    const day = item.timestamp.getDate();
                    // 确保是同一个月的数据
                    if (item.timestamp.getFullYear() === year &&
                        item.timestamp.getMonth() === month &&
                        day >= 1 && day <= daysInMonth) {
                        console.log(`将第${day}天的数据放到索引${day - 1}位置:`, item);
                        filledData[day - 1] = item; // 转换为0-based索引
                    }
                } else if (item) {
                    console.warn(`月报数据项${index}缺少timestamp:`, item);
                }
            });

            console.log('月报填充后的数据:', filledData);
            return filledData;
        },

        // 填充年报数据（12个月）
        fillYearlyData (processedData) {
            const filledData = new Array(12).fill(null);

            processedData.forEach(item => {
                if (item.timestamp) {
                    const month = item.timestamp.getMonth(); // 0-based月份
                    if (month >= 0 && month < 12) {
                        filledData[month] = item;
                    }
                }
            });

            return filledData;
        },











        // 获取显示的数值文本
        getDisplayValueText (dataType) {
            const unitMap = {
                'co2': 'ppm',
                'hcho': this.$t('HchoCurrent_unit'),
                'heat': this.$t('HeatCurrentFloat_unit'),
                'moisture': this.$t('MoistureCurrent_unit')
            };

            const unit = unitMap[dataType] || '';

            // 检查图表数据是否为空
            const chartDataArray = this.chartData[dataType];
            const hasChartData = chartDataArray && Array.isArray(chartDataArray) && chartDataArray.length > 0;

            // 获取当前值的逻辑
            let currentValue = '--';

            if (this.type == 1) {
                // 环境质量概览页面：使用统计平均值
                const currentValueMap = {
                    'co2': this.Co2Current,
                    'hcho': this.HchoCurrent, // 甲醛数据不再需要转换
                    'heat': this.HeatCurrentFloat,
                    'moisture': this.MoistureCurrent
                };
                currentValue = currentValueMap[dataType] || '--';
            } else {
                // 其他页面（图表页面）：当有图表数据时显示最新数据，否则显示"--"
                if (hasChartData) {
                    const latestData = chartDataArray[chartDataArray.length - 1];
                    if (latestData && typeof latestData === 'object' && latestData.min !== undefined && latestData.max !== undefined) {
                        // 区间数据：使用平均值
                        currentValue = this.formatDisplayValue((latestData.min + latestData.max) / 2, dataType);
                    } else if (latestData && typeof latestData === 'number') {
                        // 单一数值
                        currentValue = this.formatDisplayValue(latestData, dataType);
                    }
                }
            }

            // 检查是否有选中的图表数据
            const selectedData = this.selectedChartData[dataType];

            if (this.selectIndex === 0) {
                // 日报：显示选中柱状图数据的平均值
                if (selectedData && selectedData.value) {
                    const value = selectedData.value;
                    if (typeof value === 'object' && value.min !== undefined && value.max !== undefined) {
                        // 区间数据：计算平均值
                        const avgValue = (value.min + value.max) / 2;
                        const formattedValue = this.formatDisplayValue(avgValue, dataType);
                        return `<span style="color: var(--emui_text_primary);font-size: 1.6rem;">${formattedValue}</span> ${unit}`;
                    } else if (typeof value === 'number') {
                        // 单一数值
                        const formattedValue = this.formatDisplayValue(value, dataType);
                        return `<span style="color: var(--emui_text_primary);font-size: 1.6rem;">${formattedValue}</span> ${unit}`;
                    }
                }
                // 没有选中数据时显示当前值
                return `<span style="color: var(--emui_text_primary);font-size: 1.6rem;">${currentValue}</span> ${unit}`;
            } else {
                // 周报、月报、年报：显示选中数据的最小值~最大值范围
                if (selectedData && selectedData.value) {
                    const value = selectedData.value;
                    if (typeof value === 'object' && value.min !== undefined && value.max !== undefined) {
                        const minValue = this.formatDisplayValue(value.min, dataType);
                        const maxValue = this.formatDisplayValue(value.max, dataType);
                        return `<span style="color: var(--emui_text_primary);font-size: 1.6rem;">${minValue} - ${maxValue}</span> ${unit}`;
                    } else if (typeof value === 'number') {
                        // 单一数值时显示该值
                        const formattedValue = this.formatDisplayValue(value, dataType);
                        return `<span style="color: var(--emui_text_primary);font-size: 1.6rem;">${formattedValue}</span> ${unit}`;
                    }
                }

                // 没有选中数据时，显示全局范围
                const chartData = this.chartData[dataType];
                if (chartData && chartData.length > 0) {
                    let globalMin = Infinity;
                    let globalMax = -Infinity;

                    chartData.forEach(item => {
                        if (item && item.min !== undefined && item.max !== undefined) {
                            globalMin = Math.min(globalMin, item.min);
                            globalMax = Math.max(globalMax, item.max);
                        }
                    });

                    if (globalMin !== Infinity && globalMax !== -Infinity) {
                        const minValue = this.formatDisplayValue(globalMin, dataType);
                        const maxValue = this.formatDisplayValue(globalMax, dataType);
                        return `<span style="color: var(--emui_text_primary);font-size: 1.6rem;">${minValue} - ${maxValue}</span> ${unit}`;
                    }
                }

                // 如果没有图表数据，显示当前值
                return `<span style="color: var(--emui_text_primary);font-size: 1.6rem;">${currentValue}</span> ${unit}`;
            }
        },

        // 格式化显示数值
        formatDisplayValue (value, dataType) {
            if (dataType === 'hcho') {
                // 甲醛数据保留3位小数
                return parseFloat(value).toFixed(3);
            }
            // 保留一位小数，防止浮点数精度问题
            return parseFloat(value).toFixed(1);
        },

        // 处理图表选中事件
        onChartValueSelected (data) {
            const { name, value } = data;
            const currentDataType = this.sensorConfig[this.type].dataType;

            if (currentDataType) {
                this.$set(this.selectedChartData, currentDataType, {
                    name,
                    value,
                    reportType: this.selectIndex
                });

                console.log(`选中图表数据 - ${currentDataType}:`, data);
            }
        },



        // 日期导航（上一个/下一个时间段）
        navigateDate (direction) {
            switch (this.selectIndex) {
                case 0: // 日报 - 切换天
                    this.navigateDaily(direction);
                    break;
                case 1: // 周报 - 切换周
                    this.navigateWeekly(direction);
                    break;
                case 2: // 月报 - 切换月
                    this.navigateMonthly(direction);
                    break;
                case 3: // 年报 - 切换年
                    this.navigateYearly(direction);
                    break;
            }
            // 导航后刷新数据
            this.refreshData();
        },
        // 日报导航
        navigateDaily (direction) {
            const newDate = new Date(this.selectedDate);
            if (direction === 'prev') {
                newDate.setDate(newDate.getDate() - 1);
            } else {
                newDate.setDate(newDate.getDate() + 1);
            }

            // 检查是否超出范围
            if (newDate <= this.maxDate && newDate >= this.minDate) {
                this.selectedDate = newDate;
                this.tempSelectedDate = new Date(newDate);
            }
        },
        // 周报导航
        navigateWeekly (direction) {
            // 基于选中的具体日期进行导航
            const currentDate = new Date(this.selectedWeekDate);

            if (direction === 'prev') {
                currentDate.setDate(currentDate.getDate() - 7);
            } else {
                currentDate.setDate(currentDate.getDate() + 7);
            }

            // 计算新的周范围
            const newWeekStart = this.getStartOfWeek(currentDate);
            const newWeekEnd = new Date(newWeekStart);
            newWeekEnd.setDate(newWeekStart.getDate() + 6);

            // 检查是否超出范围
            if (newWeekEnd <= this.maxDate && newWeekStart >= this.minDate) {
                this.selectedWeekDate = new Date(currentDate);
                this.selectedWeek = [newWeekStart, newWeekEnd];
                this.tempSelectedWeek = [new Date(newWeekStart), new Date(newWeekEnd)];
            }
        },
        // 月报导航
        navigateMonthly (direction) {
            const newDate = new Date(this.selectedMonth);
            if (direction === 'prev') {
                newDate.setMonth(newDate.getMonth() - 1);
            } else {
                newDate.setMonth(newDate.getMonth() + 1);
            }

            // 检查是否超出范围
            if (newDate <= this.maxDate && newDate >= this.minDate) {
                this.selectedMonth = newDate;
                this.tempSelectedMonth = new Date(newDate);
            }
        },

        // 年报导航
        navigateYearly (direction) {
            const newDate = new Date(this.selectedYear);
            if (direction === 'prev') {
                newDate.setFullYear(newDate.getFullYear() - 1);
            } else {
                newDate.setFullYear(newDate.getFullYear() + 1);
            }

            // 检查是否超出范围
            if (newDate <= this.maxDate && newDate >= this.minDate) {
                this.selectedYear = newDate;
            }
        },
    },
    watch: {},
};
</script>

<style lang="less">
@import url("../style/SubHome.less");

#subhome-page {
    .van-tabs {
        width: 100%;
        margin: 0 auto;
        height: 5.6rem;
        overflow: auto;
    }

    .van-tabs__wrap {
        margin: 0 auto;
        height: 5.6rem !important;
    }

    .van-tabs__nav {
        padding: 0;
        background: transparent;
    }

    .van-tab {
        padding: 0;
        font-size: 1.6rem;
    }

    .van-tabs__line {
        height: 0.2rem;
        background-color: var(--emui_accent);
        bottom: 0.8rem;
    }

    // CO2状态颜色
    .color-64BB5C {
        color: var(--emui_color_connected);
    }

    .color-E84026 {
        color: var(--emui_functional_red);
    }

    .color-F7CE00 {
        color: var(--emui_color_F7CE00);
    }

    // 湿度等级颜色类
    .moisture-level-dry {
        color: var(--emui_functional_red); // 干燥 - 红色
    }

    .moisture-level-comfortable {
        color: var(--emui_color_connected); // 舒适 - 绿色
    }

    .moisture-level-humid {
        color: var(--emui_accent); // 潮湿 - 蓝色
    }

    // CO2图表卡片区域
    .co2-chart-section {
        .cardStyle();
        margin-bottom: 2.4rem;
        padding: 1.6rem;

        .co2-value-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 3.2rem;
        }

        .chart-container {
            width: 100%;
        }
    }

    // 等级说明区域
    .level-description {
        .cardStyle();
        padding: 1.6rem;
        margin-bottom: 2.4rem;

        .level-intro {
            font-size: 1.4rem;
            color: var(--emui_text_secondary);
            line-height: 1.6;
            margin-bottom: 2.4rem;
            margin-top: 0;
        }

        .level-item {
            display: flex;
            align-items: center;
            margin-bottom: 1.4rem;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .level-dot {
            width: 1.2rem;
            height: 1.2rem;
            border-radius: 50%;
            margin-right: 1.2rem;
            flex-shrink: 0;

            &.level-dot-normal {
                background-color: var(--emui_color_connected);
            }

            &.level-dot-exceeded {
                background-color: var(--emui_color_F7CE00);
            }

            &.level-dot-severe {
                background-color: var(--emui_functional_red);
            }

            // 湿度等级点样式
            &.level-dot-moisture-comfortable {
                background-color: var(--emui_color_connected); // 舒适 - 绿色
            }

            &.level-dot-moisture-dry {
                background-color: var(--emui_functional_red); // 干燥 - 红色
            }

            &.level-dot-moisture-humid {
                background-color: var(--emui_accent); // 潮湿 - 蓝色
            }
        }

        .level-text {
            font-size: 1.4rem;
            line-height: 1.5;

            .level-name {
                color: var(--emui_text_primary);
                font-weight: 500;
            }

            .level-range {
                color: var(--emui_text_secondary);
            }
        }
    }

    // 全局Loading遮罩样式
    .global-loading-mask {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;

        .global-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: var(--emui_mask_thick);
            border-radius: 1.2rem;
            padding: 3.2rem 2.4rem;

            .loading-icon {
                width: 4.8rem;
                height: 4.8rem;
                margin-bottom: 1.6rem;
            }

            .loading-text {
                font-size: 1.4rem;
                color: var(--emui_text_secondary_inverse);
                white-space: nowrap;
            }
        }
    }

}
</style>

<style lang="less" scoped>
@import url("../style/SubHome.less");
</style>
