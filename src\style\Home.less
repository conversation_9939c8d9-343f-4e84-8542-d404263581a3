@import url("../../lib/style/public.less");
#page {
overflow-y: auto;
.content {
// pad适配
margin: 5.6rem var(--home_margin) 0px var(--home_margin);
position: relative;
}
.module-box {
margin: 0 .6rem;
display: flex;
flex-wrap: wrap;
align-content: flex-start;
align-items: flex-start;
justify-content: flex-start;
}
.lrimg {
width: 2.4rem;
height: 2.4rem;
background-repeat: no-repeat;
background-size: cover;
&.disabled {
opacity: .4;
pointer-events: none;
cursor: default;
}
}
.color-64BB5C {
color: var(--emui_color_connected)
}
.color-E84026 {
color: var(--emui_functional_red)
}
.color-ED6F21 {
color: var(--emui_color_warning)
}
.color-F7CE00 {
color: var(--emui_color_F7CE00)
}
.color-F9A01E {
color: var(--emui_color_F9A01E)
}
.color-E64566 {
color: var(--emui_color_E64566)
}
}
/deep/ .TempClass {
  .mt-range-runway {
      background: linear-gradient(90deg, var(--emui_color_F9A01E) 0%, var(--emui_card_panel_bg) 100%);
  }

  .mt-range-progress {
      background-color: transparent;
  }

  .mt-range-thumb {
      background-color: var(--emui_color_F9A01E)
  }
}
.dark #page {
}