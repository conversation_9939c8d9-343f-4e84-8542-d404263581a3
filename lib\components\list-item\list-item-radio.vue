<template>
  <div class="list-item" @click="rightClick">
    <div class="left">
      <span class="name">{{ name }}</span>
    </div>
    <div :id="idStr !== '' ? 'com.huawei.smarthome:id/radio_' + idStr : ''" class="right">
      <span class="right-text">{{ info }}</span>
      <div class="arrow"></div>
    </div>
    <div v-if="divider" class="list-item-divider"></div>
    <DialogRadioPicker v-if="isShowDialog" :title="title != '' ? title : name" :subtitle="subtitle" :values="options" :defaultIndex="defaultValue" @cancel="cancel" @confirm="confirm"></DialogRadioPicker>
  </div>
</template>

<script>
export default {
  name: 'ListItemRadio',
  props: {
    name: {
      default: '',
      type: String
    },
    info: {
      default: '',
      type: String
    },
    idStr: {
      default: '',
      type: String
    },
    divider: {
      default: true,
      type: Boolean
    },
    title: {
      default: '',
      type: String
    },
    subtitle: {
      default: '',
      type: String
    },
    defaultIndex: {
      default: '',
      type: [String, Number]
    },
    options: {
      default: () => [],
      type: Array
    }
  },
  data() {
    return {
      isShowDialog: false
    }
  },
  watch: {
  },
  computed: {
  },
  methods: {
    rightClick() {
      this.isShowDialog = true
    },
    confirm(val) {
      this.$emit('select', val)
    },
    cancel() {
      this.isShowDialog = false
    }
  },
}
</script>

<style lang="less" scoped>
@import url("../../style/public.less");

.list-item {
  margin: 0px 1.2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  min-height: 4.8rem;
  .left {
    max-width: 66%;
    margin: 0.8rem 0;
    .name {
      font-size: 1.6rem;
      font-weight: 500;
      color: var(--emui_text_primary);
    }
  }

  .right {
    flex: 1;
    width: 0;
    display: flex;
    align-items: center;
    padding-left: 1.6rem;
    margin: 0.8rem 0;
    .right-text {
      text-align: right;
      width: 100%;
      font-size: 14px;
      color: var(--emui_text_secondary);
      font-weight: 400;
      white-space: wrap;
    }

    .arrow {
      flex-shrink: 0;
      width: 1.2rem;
      height: 2.4rem;
      margin-left: 0.4rem;
      border: 0 none;
      background-size: cover;
      background-repeat: no-repeat;
      background-image: var(--img_ic_right_arrow);
    }
  }

  .list-item-divider {
    position: absolute;
    width: 100%;
    height: 1px;
    bottom: 0;
    transform: scaleY(0.25);
    background: var(--emui_color_divider_horizontal);
  }
}
</style>