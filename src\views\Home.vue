<template>
    <div id="page">
        <Titlebar
            :title="devName"
            @leftClick="closeWindow()"
            @rightClick="jumpTo"
        />
        <div class="content" :style="{ paddingTop: `${statusBarHeight}px` }">
            <DeviceShow
                ref="deviceShow"
                :product="require('@/assets/256px.png.png')"
                :logo="require('@/assets/logo.png.png')"
            />
            <div class="module-box">
                <BleStatusBar
                    ref="statusBar"
                    sid="switch"
                    :type="2"
                    chara="on"
                    :switchValue="SwitchOn"
                    :connectStatus="2"
                ></BleStatusBar>

                <template v-if="commonMode >= 1 && commonMode !== 3">
                    <SwitchBar
                        idStr="MainSwitchOn"
                        :name="$t('MainSwitchOn')"
                        @handleClick="
                            sendCommond(
                                'mainSwitch',
                                'on',
                                1 - MainSwitchOn,
                                MainSwitchOn
                            )
                        "
                        :disabled="!canControl"
                        :active="
                            MainSwitchOn != 0 && canControl
                        "
                    ></SwitchBar>
                    <SwitchBar
                        idStr="AuxSwitchOn"
                        :name="$t('AuxSwitchOn')"
                        @handleClick="
                            sendCommond(
                                'auxSwitch',
                                'on',
                                1 - AuxSwitchOn,
                                AuxSwitchOn
                            )
                        "
                        :disabled="!canControl"
                        :active="
                            AuxSwitchOn != 0 && canControl
                        "
                    ></SwitchBar>
                </template>
                <SliderBar
                    idStr="Brightness"
                    :name="$t('Brightness')"
                    :info="
                        $t('Brightness_info', { value: BrightnessSliderValue })
                    "
                    v-model="BrightnessSliderValue"
                    :showXAxis="false"
                    :step="1"
                    :min="1"
                    :max="100"
                    @change="changeBrightness"
                    :disabled="!(canControl && (commonMode == 0 || commonMode == 3 ? SwitchOn == 1 : MainSwitchOn == 1))"
                ></SliderBar>
                <SliderBar
                    v-if="commonMode >= 3"
                    class="TempClass"
                    idStr="CctColorTemperature"
                    :name="$t('CctColorTemperature')"
                    :info="
                        $t('CctColorTemperature_info', {
                            value: CctColorTemperatureSliderValue,
                        })
                    "
                    v-model="CctColorTemperatureSliderValue"
                    :showXAxis="false"
                    :step="1"
                    :min="2700"
                    :max="6500"
                    @change="changeCctColorTemperature"
                    :disabled="!(canControl && (commonMode == 0 || commonMode == 3 ? SwitchOn == 1 : MainSwitchOn == 1))"
                ></SliderBar>
                <SliderBar
                    v-if="[2, 100].includes(commonMode)"
                    idStr="AuxBrightnessBrightness"
                    :name="$t('AuxBrightnessBrightness')"
                    :info="
                        $t('AuxBrightnessBrightness_info', {
                            value: AuxBrightnessBrightnessSliderValue,
                        })
                    "
                    v-model="AuxBrightnessBrightnessSliderValue"
                    :showXAxis="false"
                    :step="1"
                    :min="1"
                    :max="100"
                    @change="changeAuxBrightnessBrightness"
                    :disabled="!(canControl && AuxSwitchOn == 1)"
                ></SliderBar>
                <SliderBar
                    v-if="commonMode === 6"
                    class="TempClass"
                    idStr="AuxCctColorTemperature"
                    :name="$t('AuxCctColorTemperature')"
                    :info="
                        $t('AuxCctColorTemperature_info', {
                            value: AuxCctColorTemperatureSliderValue,
                        })
                    "
                    v-model="AuxCctColorTemperatureSliderValue"
                    :showXAxis="false"
                    :step="1"
                    :min="2700"
                    :max="6500"
                    @change="changeAuxCctColorTemperature"
                    :disabled="!(canControl && AuxSwitchOn == 1)"
                ></SliderBar>
                <ModeBar
                    :modes="LightModeModeItems"
                    :mode="LightModeMode"
                    :disabled="!(canControl && SwitchOn == 1)"
                    @switchMode="changeLightModeMode"
                />
                <ControlBar
                    idStr="TimerNum"
                    :name="$t('TimerNum')"
                    iconName="ic_TimerNum"
                    :info="TimerNumTimeText"
                    @handleClick="handleTimerNumClick"
                    :active="timerList.length != 0"
                ></ControlBar>
                <ControlBar
                    idStr="Countdown"
                    :name="
                        DelayArr.length > 0
                            ? $t(
                                  `dialog_title_Countdown_${
                                      SwitchOn == 0 ? 'on' : 'off'
                                  }`
                              )
                            : $t('countdown_title')
                    "
                    iconName="ic_Countdown"
                    :info="CountdownTimeText"
                    @handleClick="handleCountDownClick"
                    :active="DelayArr.length > 0"
                ></ControlBar>
                <SettingBar
                    :name="$t('Home_btn11')"
                    @handleClick="$router.push('/Setting')"
                    style="width: 100%"
                ></SettingBar>
                <SubTitle :title="$t('saleservice_title')"></SubTitle>
                <ControlBar
                    :name="$t('customerservice_title')"
                    iconName="icon_service"
                    style="width: 100%"
                    :info="servicePhone"
                    :active="true"
                    @handleClick="callSevicePhone"
                ></ControlBar>
            </div>
        </div>
        <!-- iOS倒计时弹窗 -->
    <DialogTimePicker
        v-if="isShowCountDownDialog"
        :title="DelayArr.length > 0 ? $t(`dialog_title_Countdown_${SwitchOn == 0 ? 'on' : 'off'}`) : $t('countdown_title')"
        :startTime="1"
        :limitLength="1439"
        :countDown="true"
        :leftBtnText="DelayArr.length > 0 ? $t('btn_close_Countdown') : $t('cancel')"
        :leftBtnRed="DelayArr.length > 0"
        @confirm="handleCountDownConfirm"
        @cancel="handleCountDownCancel"
    />
    </div>
</template>
<script>
import { mapState, mapGetters, mapMutations, mapActions } from "vuex";
export default {
    name: "home",
    data() {
        return {
            dialogList: [], // 首页没有弹窗，但需要这个属性给goBack mixin使用
            servicePhone: '+8617307600506',
            isShowCountDownDialog: false, // iOS倒计时弹窗显示状态
            BrightnessSliderValue: 1,
            CctColorTemperatureSliderValue: 2700,
            AuxBrightnessBrightnessSliderValue: 1,
            AuxCctColorTemperatureSliderValue: 2700,
            LightModeModeItems: [
                {
                    name: this.$t("LightModeMode_1"),
                    value: 1,
                    img: "ic_LightModeMode1",
                },
                {
                    name: this.$t("LightModeMode_2"),
                    value: 2,
                    img: "ic_LightModeMode2",
                },
                {
                    name: this.$t("LightModeMode_3"),
                    value: 3,
                    img: "ic_LightModeMode3",
                },
                {
                    name: this.$t("LightModeMode_4"),
                    value: 4,
                    img: "ic_LightModeMode4",
                },
                {
                    name: this.$t("LightModeMode_5"),
                    value: 5,
                    img: "ic_LightModeMode5",
                },
                {
                    name: this.$t("LightModeMode_6"),
                    value: 6,
                    img: "ic_LightModeMode6",
                },
                {
                    name: this.$t("LightModeMode_7"),
                    value: 7,
                    img: "ic_LightModeMode7",
                },
                {
                    name: this.$t("LightModeMode_8"),
                    value: 8,
                    img: "ic_LightModeMode8",
                },
            ],
            CountdownItems: [
                { name: this.$t("item_Countdown_1"), value: 1 },
                {
                    name: this.$t("item_Countdown_2"),
                    value: 2,
                },
                { name: this.$t("item_Countdown_3"), value: 3 },
                {
                    name: this.$t("item_Countdown_4"),
                    value: 5,
                },
                { name: this.$t("item_Countdown_5"), value: 5 },
                {
                    name: this.$t("item_Countdown_6"),
                    value: 6,
                },
                { name: this.$t("item_Countdown_7"), value: 7 },
                {
                    name: this.$t("item_Countdown_8"),
                    value: 8,
                },
                { name: this.$t("item_Countdown_9"), value: 9 },
                {
                    name: this.$t("item_Countdown_10"),
                    value: 10,
                },
                { name: this.$t("item_Countdown_11"), value: 11 },
                {
                    name: this.$t("item_Countdown_12"),
                    value: 12,
                },
                { name: this.$t("item_Countdown_13"), value: 13 },
                {
                    name: this.$t("item_Countdown_14"),
                    value: 14,
                },
                { name: this.$t("item_Countdown_15"), value: 15 },
                {
                    name: this.$t("item_Countdown_16"),
                    value: 16,
                },
                { name: this.$t("item_Countdown_17"), value: 17 },
                {
                    name: this.$t("item_Countdown_18"),
                    value: 18,
                },
                { name: this.$t("item_Countdown_19"), value: 19 },
                {
                    name: this.$t("item_Countdown_20"),
                    value: 20,
                },
                { name: this.$t("item_Countdown_21"), value: 21 },
                {
                    name: this.$t("item_Countdown_22"),
                    value: 22,
                },
                { name: this.$t("item_Countdown_23"), value: 23 },
                {
                    name: this.$t("item_Countdown_24"),
                    value: 24,
                },
                { name: this.$t("item_Countdown_25"), value: 25 },
                {
                    name: this.$t("item_Countdown_26"),
                    value: 26,
                },
                { name: this.$t("item_Countdown_27"), value: 27 },
                {
                    name: this.$t("item_Countdown_28"),
                    value: 28,
                },
                { name: this.$t("item_Countdown_29"), value: 29 },
                { name: this.$t("item_Countdown_30"), value: 30 },
            ],
        };
    },
    computed: {
        ...mapState(["appMinVersion"]),
        ...mapGetters(["headerHeight", "statusBarHeight", "canControl"]),
        ...mapState({
            devName: (state) => state.devName,
            commonMode: (state) => state.commonMode.mode,
            SwitchOn: (state) => state.switch.on,
            MainSwitchOn: (state) => state.mainSwitch.on,
            AuxSwitchOn: (state) => state.auxSwitch.on,
            Brightness: (state) => state.brightness.brightness,
            CctColorTemperature: (state) => state.cct.colorTemperature,
            AuxBrightnessBrightness: (state) => state.auxBrightness.brightness,
            AuxCctColorTemperature: (state) => state.auxCct.colorTemperature,
            LightModeMode: (state) => state.lightMode.mode,
            DelayArr: (state) => state.delay.delay,
            timerList: (state) => state.timer.timer,
        }),
        TimerNumTimeText() {
            if (this.timerList && this.timerList.length > 0) {
                const enabledTimer = this.timerList.find(timer => timer.enable === 1);
                if (enabledTimer) {
                    if (enabledTimer.start && enabledTimer.end) {
                        const start = this.$moment(enabledTimer.start, "HHmmssZ").format("HH:mm");
                        const end = this.$moment(enabledTimer.end, "HHmmssZ").format("HH:mm");
                        return `${start}-${end}`;
                    } else if (enabledTimer.end) {
                        return this.$t('timer_end_time').replace(
                            "{time}",this.$moment(enabledTimer.end, "HHmmssZ").format("HH:mm"))
                    } else {
                        return this.$t('timer_start_time').replace(
                            "{time}",this.$moment(enabledTimer.start, "HHmmssZ").format("HH:mm"));
                    }


                }
            }
            return "";
        },
        CountdownTimeText() {
            if (this.DelayArr.length > 0) {
                let obj = this.DelayArr[0];
                const timeStr = obj.start || obj.end;

                // 使用 moment 解析时间
                const target = this.$moment(timeStr, "YYYYMMDDTHHmmssZ");
                const now = this.$moment();
                const diff = Math.max(1, Math.ceil(target.diff(now, "seconds") / 60));

                const hours = Math.floor(diff / 60);
                const minutes = diff % 60;

                return this.$t("Countdown_time_format").replace(
                    "{time}",
                    `${hours.toString().padStart(2, "0")}:${minutes
                        .toString()
                        .padStart(2, "0")}`
                );
            }
            return "";
        },
    },
    activated() {
        console.log('Home页面activated被触发，重新设置goBack');
        if (this.$store.state.type != 'ios') {
                    this.setupGoBack()
        }

    },
    created() {
        this.$store.dispatch("getDevInfoAll");

        this.$store.dispatch("setTitleVisible", false);

        // 注册全局方法供App.vue调用
        if (this.$store.state.type != 'ios') {
            window.homeSetupGoBack = () => {
                console.log('window.homeSetupGoBack被调用，恢复手势返回');
                this.setupGoBack();
            };
            console.log('Home组件created，已注册window.homeSetupGoBack');
        }

    },
    methods: {
        // 重写goBack mixin的setupGoBack方法，首页特殊处理
        setupGoBack() {
            console.log('Home页面setupGoBack被调用，恢复正常手势返回');

            window.goBack = () => {
               this.closeWindow();

            };
        },
        // 首页
        closeWindow() {
            this.$store.dispatch("finishDeviceActivity");
        },
        goBack() {
            //返回上一页
            this.$router.goBack();
        },
        jumpTo() {
            this.$store.dispatch("jumpToDeviceSetting");
        },
        sendCommond(sid, chara, value, curValue) {
            if (!this.canControl || value === curValue) {
                return;
            }
            const data = {
                [sid]: {
                    [chara]: value,
                },
            };
            this.$store.dispatch("setDevInfo", data);
        },
        formatTime(time, format, unit) {
            if (unit == 0) {
                const hours = Math.floor(time / 3600);
                const minutes = Math.floor((time % 3600) / 60);
                const seconds = Math.floor(time % 60);

                console.log("格式化时间:", { time, hours, minutes, seconds });

                return format.replace(
                    "{time}",
                    `${hours.toString().padStart(2, "0")}:${minutes
                        .toString()
                        .padStart(2, "0")}:${seconds
                        .toString()
                        .padStart(2, "0")}`
                );
            } else if (unit == 1) {
                return this.$moment.duration(time, "minutes").format(format);
            } else if (unit == 2) {
                return this.$moment.duration(time, "hours").format(format);
            }
            return time;
        },
        handleCountDownClick() {
            console.log('当前平台类型:', this.$store.state.type);
            console.log('当前isShowCountDownDialog:', this.isShowCountDownDialog);


            // 临时测试：直接使用H5弹窗
            console.log('使用H5弹窗进行测试');
            // this.isShowCountDownDialog = true;
            if (this.$store.state.type == 'ios') {
                window.hilink.timerDiag()
            } else {
                window.goBack = () => {}
                window.hilink.showDelayInfoDialog();
            }


            console.log('设置后isShowCountDownDialog:', this.isShowCountDownDialog);
        },
        handleTimerNumClick() {
                            window.goBack = () => {}
                localStorage.setItem('countDownDialogShowing', '1');
            window.hilink.jumpTo(
                "com.huawei.smarthome.timerPage",
                "Ag.timerPageResult"
            );

            window.Ag.timerPageResult = (res) => {
                console.log(res);
            };
        },
        changeBrightness(val) {
            this.sendCommond("brightness", "brightness", val, this.Brightness);
        },
        changeCctColorTemperature(val) {
            this.sendCommond(
                "cct",
                "colorTemperature",
                val,
                this.CctColorTemperature
            );
        },
        changeAuxBrightnessBrightness(val) {
            this.sendCommond(
                "auxBrightness",
                "brightness",
                val,
                this.AuxBrightnessBrightness
            );
        },
        changeAuxCctColorTemperature(val) {
            this.sendCommond(
                "auxCct",
                "colorTemperature",
                val,
                this.AuxCctColorTemperature
            );
        },
        changeLightModeMode(item) {
            this.sendCommond(
                "lightMode",
                "mode",
                item.value,
                this.LightModeMode
            );
        },
                // iOS倒计时弹窗确认
        handleCountDownConfirm(values) {
            console.log('倒计时确认被调用，选择的时间:', values);
            this.isShowCountDownDialog = false;

            // 计算总分钟数
            const totalMinutes = Number(values[0]) * 60 + Number(values[1]);

            // 生成时间戳 - 当前时间加上选择的分钟数
            const targetTime = this.$moment().add(totalMinutes, 'minutes');
            const timeStr = targetTime.format('YYYYMMDDTHHmmss') + 'Z';

            // 决策理由：根据设备当前开关状态决定使用start还是end参数
            // 设备关机状态(SwitchOn=0)使用start表示倒计时开启
            // 设备开机状态(SwitchOn=1)使用end表示倒计时关闭
            const timeParam = this.SwitchOn === 0 ? 'start' : 'end';
            const paraValue = this.SwitchOn === 0 ? '1' : '0';

            const data = {
                delay: [
                    {
                        'para': 'on',
                        [timeParam]: timeStr,
                        'paraValue': -1,
                        'id': '1',
                        'sid': 'switch'
                    }
                ],
                'num': 1
            };

            console.log('iOS倒计时下发数据:', data);
            this.$store.dispatch("setDevInfo", data);
        },

        // iOS倒计时弹窗取消
        handleCountDownCancel(isCancel) {
            console.log('倒计时取消被调用，isCancel:', isCancel);
            this.isShowCountDownDialog = false;

            // 如果当前有倒计时且点击的是"关闭倒计时"按钮
            if (this.DelayArr.length > 0 && isCancel) {
                const data = {
                    delay: [],
                    'num': 0
                };

                console.log('关闭倒计时:', data);
                this.$store.dispatch("setDevInfo", data);
            }
        },
        // 呼叫售后电话
        callSevicePhone() {
            setTimeout(() => {
                if (
                    this.$store.dispatch(
                        "isLaterAppVersion",
                        this.appMinVersion
                    )
                ) {
                    console.log("this.appMinVersion", this.appMinVersion);
                    window.location.href = `tel:${this.servicePhone}`;
                }
            }, 300);
        },
    },
    watch: {
        Brightness(val) {
            this.BrightnessSliderValue = val;
        },
        CctColorTemperature(val) {
            this.CctColorTemperatureSliderValue = val;
        },
        AuxBrightnessBrightness(val) {
            this.AuxBrightnessBrightnessSliderValue = val;
        },
        AuxCctColorTemperature(val) {
            this.AuxCctColorTemperatureSliderValue = val;
        },
    },
    beforeDestroy() {},
};
</script>
<style lang="less" scoped>
@import url("../style/Home.less");
</style>
