# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@7.0.0-beta.44":
  "integrity" "sha512-cuAuTTIQ9RqcFRJ/Y8PvTh+paepNcaGxwQwjIDRWPXmzzyAeCO4KqS9ikMvq0MCbRk6GlYKwfzStrcP3/jSL8g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@babel/code-frame/-/code-frame-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "@babel/highlight" "7.0.0-beta.44"

"@babel/generator@7.0.0-beta.44":
  "integrity" "sha512-5xVb7hlhjGcdkKpMXgicAVgx8syK5VJz193k0i/0sLP6DzE6lRrU1K3B/rFefgdo9LPGMAOOOAWW4jycj07ShQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@babel/generator/-/generator-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "@babel/types" "7.0.0-beta.44"
    "jsesc" "^2.5.1"
    "lodash" "^4.2.0"
    "source-map" "^0.5.0"
    "trim-right" "^1.0.1"

"@babel/helper-function-name@7.0.0-beta.44":
  "integrity" "sha512-MHRG2qZMKMFaBavX0LWpfZ2e+hLloT++N7rfM3DYOMUOGCD8cVjqZpwiL8a0bOX3IYcQev1ruciT0gdFFRTxzg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@babel/helper-function-name/-/helper-function-name-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "@babel/helper-get-function-arity" "7.0.0-beta.44"
    "@babel/template" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"

"@babel/helper-get-function-arity@7.0.0-beta.44":
  "integrity" "sha512-w0YjWVwrM2HwP6/H3sEgrSQdkCaxppqFeJtAnB23pRiJB5E/O9Yp7JAAeWBl+gGEgmBFinnTyOv2RN7rcSmMiw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "@babel/types" "7.0.0-beta.44"

"@babel/helper-split-export-declaration@7.0.0-beta.44":
  "integrity" "sha512-aQ7QowtkgKKzPGf0j6u77kBMdUFVBKNHw2p/3HX/POt5/oz8ec5cs0GwlgM8Hz7ui5EwJnzyfRmkNF1Nx1N7aA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "@babel/types" "7.0.0-beta.44"

"@babel/highlight@7.0.0-beta.44":
  "integrity" "sha512-Il19yJvy7vMFm8AVAh6OZzaFoAd0hbkeMZiX3P5HGD+z7dyI7RzndHB0dg6Urh/VAFfHtpOIzDUSxmY6coyZWQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@babel/highlight/-/highlight-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "chalk" "^2.0.0"
    "esutils" "^2.0.2"
    "js-tokens" "^3.0.0"

"@babel/template@7.0.0-beta.44":
  "integrity" "sha512-w750Sloq0UNifLx1rUqwfbnC6uSUk0mfwwgGRfdLiaUzfAOiH0tHJE6ILQIUi3KYkjiCDTskoIsnfqZvWLBDng=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@babel/template/-/template-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    "babylon" "7.0.0-beta.44"
    "lodash" "^4.2.0"

"@babel/traverse@7.0.0-beta.44":
  "integrity" "sha512-UHuDz8ukQkJCDASKHf+oDt3FVUzFd+QYfuBIsiNu/4+/ix6pP/C+uQZJ6K1oEfbCMv/IKWbgDEh7fcsnIE5AtA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@babel/traverse/-/traverse-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/generator" "7.0.0-beta.44"
    "@babel/helper-function-name" "7.0.0-beta.44"
    "@babel/helper-split-export-declaration" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    "babylon" "7.0.0-beta.44"
    "debug" "^3.1.0"
    "globals" "^11.1.0"
    "invariant" "^2.2.0"
    "lodash" "^4.2.0"

"@babel/types@7.0.0-beta.44":
  "integrity" "sha512-5eTV4WRmqbaFM3v9gHAIljEQJU4Ssc6fxL61JN+Oe2ga/BwyjzjamwkCVVAQjHGuAX8i0BWo42dshL8eO5KfLQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@babel/types/-/types-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "esutils" "^2.0.2"
    "lodash" "^4.2.0"
    "to-fast-properties" "^2.0.0"

"@better-scroll/core@^2.4.2":
  "integrity" "sha512-IqVZLnh04YpaEAy9wJDxtFK/stxVQjB9A9Wcr3Uwkj7Av1TtFpin+t/TObl53diNDG5ZJ+vck/OAthphpuugLA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/core/-/core-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/shared-utils" "^2.4.2"

"@better-scroll/indicators@^2.4.2":
  "integrity" "sha512-dScI0HIt06L1thLO+1W7w1enqRtegWF1izKYhswiyTXLgagLh2QbAi+oexNiPSOGBuNz8w1mO7hZvVJez/ZFkw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/indicators/-/indicators-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/core" "^2.4.2"

"@better-scroll/infinity@^2.4.2":
  "integrity" "sha512-KGpCWLTyMzcGBT59c3N/JaVauLmi0jG2YZApdnqON+Oz4AJiHnzGkzWc4EJxcxR3Vy+m5Jp91y6xrpsvRmOdIg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/infinity/-/infinity-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/core" "^2.4.2"

"@better-scroll/mouse-wheel@^2.4.2":
  "integrity" "sha512-dLc8vlYkSYN9AQEo4NDPQloPhscCV17TY98JUBcio4I2STDlYYIQqofqm1GQPfByfK9f057DnrcgZ/bPVx7PTA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/mouse-wheel/-/mouse-wheel-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/core" "^2.4.2"

"@better-scroll/movable@^2.4.2":
  "integrity" "sha512-y4FbKx9FTUfAfM6wW+PCKA3CONc4nfjaE4N1ImGsIorzkSWV2eGO+LTD6FVv5+gVINI/o9Xj/gHmEJUaupUeSg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/movable/-/movable-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/core" "^2.4.2"

"@better-scroll/nested-scroll@^2.4.2":
  "integrity" "sha512-JnC2sqtDqVkj9F5J39u52b6qDsMqXbnURG67dHf97tYoYP2dluwjkvhhxjSFrghREoQzT/KWcDbkfcGy47lQwQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/nested-scroll/-/nested-scroll-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/core" "^2.4.2"

"@better-scroll/observe-dom@^2.4.2":
  "integrity" "sha512-eeS77CZs+V72dkya10e5ptndBAbhQVcXoYGvfoIihOJgCPqO5MdYTpzRmlbshYCEE5juUMLe82Kx8FSbOejpAw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/observe-dom/-/observe-dom-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/core" "^2.4.2"

"@better-scroll/observe-image@^2.4.2":
  "integrity" "sha512-ToYVf5vn0cio5B9uaL5NyvFX3JNU6L0Po20Lw7uxa0+FE+kMvSAMJN+9POg6G+cfIqI8GVHhyyR+4Ev8v2Nl5Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/observe-image/-/observe-image-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/core" "^2.4.2"

"@better-scroll/pull-down@^2.4.2":
  "integrity" "sha512-bqMLk33o4oesTXIIOteBKHQgfVKjIVvity39TM/MfzkbY1WDBxD+HFS7ySgoqw7Kl2Wiv/U76Wfqzmm6yDdg7Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/pull-down/-/pull-down-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/core" "^2.4.2"

"@better-scroll/pull-up@^2.4.2":
  "integrity" "sha512-07Cke3oa96lN9/inxJZ0ixh0nBbbqxOi2IKcBGtD6dP+susiMcdhgd/c7zNRjXkwlw0vzTNfXTgExIOEWzLHYg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/pull-up/-/pull-up-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/core" "^2.4.2"

"@better-scroll/scroll-bar@^2.4.2":
  "integrity" "sha512-zZd0+sWfzTCXJeuA001o5bea7AvLYz7BCE7dolxFbPTIPnV5V5UEH8LLdXQ/HIwgxI0Pj9PoY9njSuhooh6lfg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/scroll-bar/-/scroll-bar-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/core" "^2.4.2"

"@better-scroll/shared-utils@^2.4.2":
  "integrity" "sha512-Gy/Jfbpu+hq0u+PcjkTqyXGqAf+0dexTzEZ5IDXEVwJVLmd3cx8A73oTcAZ8QZgk4wSHvlMjXecSaptkhnNPEw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/shared-utils/-/shared-utils-2.4.2.tgz"
  "version" "2.4.2"

"@better-scroll/slide@^2.4.2":
  "integrity" "sha512-VfdFHm/meo4nEyfx0JLn8rUHfQdQCnoBHs/BsV+vmjVivKg+cVgfS+QaytrIulWKqNAWNlfXbAaNtZMv8gNKZg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/slide/-/slide-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/core" "^2.4.2"

"@better-scroll/wheel@^2.4.2":
  "integrity" "sha512-oJw68glWbrYBbRK8RJnKo3Fw9bU7Cd4zDbBHbKZ062/YJAJUZW9wF/3Lout4PnolkjuJIp2TZiEgJNtuRd+Njg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/wheel/-/wheel-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/core" "^2.4.2"

"@better-scroll/zoom@^2.4.2":
  "integrity" "sha512-rN/BTE3lf2sNNvKowGL/tUKZ/BgdZcZa0pjE/rVW0tBL/7x68cBowWQD5HF2Z2Bge8OPXRjkwN/xN1aFYhEtWA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@better-scroll/zoom/-/zoom-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/core" "^2.4.2"

"@types/json5@^0.0.29":
  "integrity" "sha1-7ihweulOEdK4J7y+UnC86n8+ce4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@types/json5/-/json5-0.0.29.tgz"
  "version" "0.0.29"

"@types/q@^1.5.1":
  "integrity" "sha512-L28j2FcJfSZOnL1WBjDYp2vUHCeIFlyYI/53EwD/rKUBQ7MtUUfbQWiyKJGpcnv4/WgrhWsFKrcPstcAt/J0tQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/@types/q/-/q-1.5.5.tgz"
  "version" "1.5.5"

"accepts@~1.3.4", "accepts@~1.3.5", "accepts@~1.3.8":
  "integrity" "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/accepts/-/accepts-1.3.8.tgz"
  "version" "1.3.8"
  dependencies:
    "mime-types" "~2.1.34"
    "negotiator" "0.6.3"

"acorn-dynamic-import@^2.0.0":
  "integrity" "sha1-x1K9IQvvZ5UBtsbLf8hPj0cVjMQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/acorn-dynamic-import/-/acorn-dynamic-import-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "acorn" "^4.0.3"

"acorn-jsx@^3.0.0":
  "integrity" "sha1-r9+UiPsezvyDSPb7IvRk4ypYs2s="
  "resolved" "https://repo.huaweicloud.com/repository/npm/acorn-jsx/-/acorn-jsx-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "acorn" "^3.0.4"

"acorn@^3.0.4":
  "integrity" "sha1-ReN/s56No/JbruP/U2niu18iAXo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/acorn/-/acorn-3.3.0.tgz"
  "version" "3.3.0"

"acorn@^4.0.3":
  "integrity" "sha1-EFSVrlNh1pe9GVyCUZLhrX8lN4c="
  "resolved" "https://repo.huaweicloud.com/repository/npm/acorn/-/acorn-4.0.13.tgz"
  "version" "4.0.13"

"acorn@^5.0.0", "acorn@^5.3.0", "acorn@^5.5.0":
  "integrity" "sha512-1D++VG7BhrtvQpNbBzovKNc1FLGGEE/oGe7b9xJm/RFHMBeUaUGpluV9RLjZa47YFdPcDAenEYuq9pQPcMdLJg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/acorn/-/acorn-5.7.4.tgz"
  "version" "5.7.4"

"ajv-keywords@^2.1.0":
  "integrity" "sha1-YXmX/F9gV2iUxDX5QNgZ4TW4B2I="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ajv-keywords/-/ajv-keywords-2.1.1.tgz"
  "version" "2.1.1"

"ajv-keywords@^3.1.0":
  "integrity" "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  "version" "3.5.2"

"ajv@^5.0.0", "ajv@^5.2.3", "ajv@^5.3.0":
  "integrity" "sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ajv/-/ajv-5.5.2.tgz"
  "version" "5.5.2"
  dependencies:
    "co" "^4.6.0"
    "fast-deep-equal" "^1.0.0"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.3.0"

"ajv@^6.1.0":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"align-text@^0.1.1", "align-text@^0.1.3":
  "integrity" "sha1-DNkKVhCT810KmSVsIrcGlDP60Rc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/align-text/-/align-text-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"
    "longest" "^1.0.1"
    "repeat-string" "^1.5.2"

"alphanum-sort@^1.0.0", "alphanum-sort@^1.0.1", "alphanum-sort@^1.0.2":
  "integrity" "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/alphanum-sort/-/alphanum-sort-1.0.2.tgz"
  "version" "1.0.2"

"ansi-escapes@^3.0.0":
  "integrity" "sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ansi-escapes/-/ansi-escapes-3.2.0.tgz"
  "version" "3.2.0"

"ansi-html@0.0.7":
  "integrity" "sha1-gTWEAhliqenm/QOflA0S9WynhZ4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ansi-html/-/ansi-html-0.0.7.tgz"
  "version" "0.0.7"

"ansi-regex@^2.0.0":
  "integrity" "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ansi-regex/-/ansi-regex-2.1.1.tgz"
  "version" "2.1.1"

"ansi-regex@^3.0.0":
  "integrity" "sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ansi-regex/-/ansi-regex-3.0.1.tgz"
  "version" "3.0.1"

"ansi-styles@^2.2.1":
  "integrity" "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ansi-styles/-/ansi-styles-2.2.1.tgz"
  "version" "2.2.1"

"ansi-styles@^3.2.1":
  "integrity" "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"anymatch@^2.0.0":
  "integrity" "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/anymatch/-/anymatch-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromatch" "^3.1.4"
    "normalize-path" "^2.1.1"

"anymatch@~3.1.2":
  "integrity" "sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/anymatch/-/anymatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"aproba@^1.1.1":
  "integrity" "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/aproba/-/aproba-1.2.0.tgz"
  "version" "1.2.0"

"argparse@^1.0.7":
  "integrity" "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/argparse/-/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"arr-diff@^4.0.0":
  "integrity" "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/arr-diff/-/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.1.0":
  "integrity" "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/arr-flatten/-/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@^3.1.0":
  "integrity" "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/arr-union/-/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-find-index@^1.0.1":
  "integrity" "sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E="
  "resolved" "https://repo.huaweicloud.com/repository/npm/array-find-index/-/array-find-index-1.0.2.tgz"
  "version" "1.0.2"

"array-flatten@^2.1.0":
  "integrity" "sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/array-flatten/-/array-flatten-2.1.2.tgz"
  "version" "2.1.2"

"array-flatten@1.1.1":
  "integrity" "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/array-flatten/-/array-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array-includes@^3.0.3", "array-includes@^3.1.4":
  "integrity" "sha512-ZTNSQkmWumEbiHO2GF4GmWxYVTiQyJy2XOTa15sdQSrvKn7l+180egQMqlrMOUMCyLMD7pmyQe4mMDUT6Behrw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/array-includes/-/array-includes-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"
    "get-intrinsic" "^1.1.1"
    "is-string" "^1.0.7"

"array-union@^1.0.1":
  "integrity" "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/array-union/-/array-union-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "array-uniq" "^1.0.1"

"array-uniq@^1.0.1":
  "integrity" "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/array-uniq/-/array-uniq-1.0.3.tgz"
  "version" "1.0.3"

"array-unique@^0.3.2":
  "integrity" "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/array-unique/-/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"array.prototype.flat@^1.2.5":
  "integrity" "sha512-KaYU+S+ndVqyUnignHftkwc58o3uVU1jzczILJ1tN2YaIZpFIKBiP/x/j97E5MVPsaCloPbqWLB/8qCTVvT2qg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/array.prototype.flat/-/array.prototype.flat-1.2.5.tgz"
  "version" "1.2.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.0"

"asn1.js@^5.2.0":
  "integrity" "sha512-+I//4cYPccV8LdmBLiX8CYvf9Sp3vQsrqu2QNXRcrbiWvcx/UdlFiqUJJzxRQxgsZmvhXhn4cSKeSmoFjVdupA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/asn1.js/-/asn1.js-5.4.1.tgz"
  "version" "5.4.1"
  dependencies:
    "bn.js" "^4.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"
    "safer-buffer" "^2.1.0"

"assert@^1.1.1":
  "integrity" "sha512-EDsgawzwoun2CZkCgtxJbv392v4nbk9XDD06zI+kQYoBM/3RBWLlEyJARDOmhAAosBjWACEkKL6S+lIZtcAubA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/assert/-/assert-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "object-assign" "^4.1.1"
    "util" "0.10.3"

"assign-symbols@^1.0.0":
  "integrity" "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="
  "resolved" "https://repo.huaweicloud.com/repository/npm/assign-symbols/-/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"async-each@^1.0.1":
  "integrity" "sha512-z/WhQ5FPySLdvREByI2vZiTWwCnF0moMJ1hK9YQwDTHKh6I7/uSckMetoRGb5UBZPC1z0jlw+n/XCgjeH7y1AQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/async-each/-/async-each-1.0.3.tgz"
  "version" "1.0.3"

"async-limiter@~1.0.0":
  "integrity" "sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/async-limiter/-/async-limiter-1.0.1.tgz"
  "version" "1.0.1"

"async@^2.1.2", "async@^2.4.1", "async@^2.6.2":
  "integrity" "sha512-zflvls11DCy+dQWzTW2dzuilv8Z5X/pjfmZOWba6TNIVDm+2UDaJmXSOXlasHKfNBs8oo3M0aT50fDEWfKZjXg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/async/-/async-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "lodash" "^4.17.14"

"atob@^2.1.2":
  "integrity" "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/atob/-/atob-2.1.2.tgz"
  "version" "2.1.2"

"autoprefixer@^6.3.1":
  "integrity" "sha1-Hb0cg1ZY41zj+ZhAmdsAWFx4IBQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/autoprefixer/-/autoprefixer-6.7.7.tgz"
  "version" "6.7.7"
  dependencies:
    "browserslist" "^1.7.6"
    "caniuse-db" "^1.0.30000634"
    "normalize-range" "^0.1.2"
    "num2fraction" "^1.2.2"
    "postcss" "^5.2.16"
    "postcss-value-parser" "^3.2.3"

"autoprefixer@^7.1.2":
  "integrity" "sha512-Iq8TRIB+/9eQ8rbGhcP7ct5cYb/3qjNYAR2SnzLCEcwF6rvVOax8+9+fccgXk4bEhQGjOZd5TLhsksmAdsbGqQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/autoprefixer/-/autoprefixer-7.2.6.tgz"
  "version" "7.2.6"
  dependencies:
    "browserslist" "^2.11.3"
    "caniuse-lite" "^1.0.30000805"
    "normalize-range" "^0.1.2"
    "num2fraction" "^1.2.2"
    "postcss" "^6.0.17"
    "postcss-value-parser" "^3.2.3"

"babel-code-frame@^6.22.0", "babel-code-frame@^6.26.0":
  "integrity" "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-code-frame/-/babel-code-frame-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "chalk" "^1.1.3"
    "esutils" "^2.0.2"
    "js-tokens" "^3.0.2"

"babel-core@^6.22.1", "babel-core@^6.26.0":
  "integrity" "sha512-6jyFLuDmeidKmUEb3NM+/yawG0M2bDZ9Z1qbZP59cyHLz8kYGKYwpJP0UwUKKUiTRNvxfLesJnTedqczP7cTDA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-core/-/babel-core-6.26.3.tgz"
  "version" "6.26.3"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "babel-generator" "^6.26.0"
    "babel-helpers" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-register" "^6.26.0"
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "convert-source-map" "^1.5.1"
    "debug" "^2.6.9"
    "json5" "^0.5.1"
    "lodash" "^4.17.4"
    "minimatch" "^3.0.4"
    "path-is-absolute" "^1.0.1"
    "private" "^0.1.8"
    "slash" "^1.0.0"
    "source-map" "^0.5.7"

"babel-eslint@^8.2.1":
  "integrity" "sha512-aCdHjhzcILdP8c9lej7hvXKvQieyRt20SF102SIGyY4cUIiw6UaAtK4j2o3dXX74jEmy0TJ0CEhv4fTIM3SzcA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-eslint/-/babel-eslint-8.2.6.tgz"
  "version" "8.2.6"
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/traverse" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    "babylon" "7.0.0-beta.44"
    "eslint-scope" "3.7.1"
    "eslint-visitor-keys" "^1.0.0"

"babel-generator@^6.26.0":
  "integrity" "sha512-HyfwY6ApZj7BYTcJURpM5tznulaBvyio7/0d4zFOeMPUmfxkCjHocCuoLa2SAGzBI8AREcH3eP3758F672DppA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-generator/-/babel-generator-6.26.1.tgz"
  "version" "6.26.1"
  dependencies:
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "detect-indent" "^4.0.0"
    "jsesc" "^1.3.0"
    "lodash" "^4.17.4"
    "source-map" "^0.5.7"
    "trim-right" "^1.0.1"

"babel-helper-bindify-decorators@^6.24.1":
  "integrity" "sha1-FMGeXxQte0fxmlJDHlKxzLxAozA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helper-bindify-decorators/-/babel-helper-bindify-decorators-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-builder-binary-assignment-operator-visitor@^6.24.1":
  "integrity" "sha1-zORReto1b0IgvK6KAsKzRvmlZmQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-explode-assignable-expression" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-call-delegate@^6.24.1":
  "integrity" "sha1-7Oaqzdx25Bw0YfiL/Fdb0Nqi340="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helper-call-delegate/-/babel-helper-call-delegate-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-hoist-variables" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-define-map@^6.24.1":
  "integrity" "sha1-pfVtq0GiX5fstJjH66ypgZ+Vvl8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helper-define-map/-/babel-helper-define-map-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-helper-explode-assignable-expression@^6.24.1":
  "integrity" "sha1-8luCz33BBDPFX3BZLVdGQArCLKo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-explode-class@^6.24.1":
  "integrity" "sha1-fcKjkQ3uAHBW4eMdZAztPVTqqes="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helper-explode-class/-/babel-helper-explode-class-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-bindify-decorators" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-function-name@^6.24.1":
  "integrity" "sha1-00dbjAPtmCQqJbSDUasYOZ01gKk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helper-function-name/-/babel-helper-function-name-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-get-function-arity" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-get-function-arity@^6.24.1":
  "integrity" "sha1-j3eCqpNAfEHTqlCQj4mwMbG2hT0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helper-get-function-arity/-/babel-helper-get-function-arity-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-hoist-variables@^6.24.1":
  "integrity" "sha1-HssnaJydJVE+rbyZFKc/VAi+enY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helper-hoist-variables/-/babel-helper-hoist-variables-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-optimise-call-expression@^6.24.1":
  "integrity" "sha1-96E0J7qfc/j0+pk8VKl4gtEkQlc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helper-optimise-call-expression/-/babel-helper-optimise-call-expression-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-regex@^6.24.1":
  "integrity" "sha1-MlxZ+QL4LyS3T6zu0DY5VPZJXnI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helper-regex/-/babel-helper-regex-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-helper-remap-async-to-generator@^6.24.1":
  "integrity" "sha1-XsWBgnrXI/7N04HxySg5BnbkVRs="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helper-remap-async-to-generator/-/babel-helper-remap-async-to-generator-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-replace-supers@^6.24.1":
  "integrity" "sha1-v22/5Dk40XNpohPKiov3S2qQqxo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-optimise-call-expression" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-vue-jsx-merge-props@^2.0.3":
  "integrity" "sha512-gsLiKK7Qrb7zYJNgiXKpXblxbV5ffSwR0f5whkPAaBAR4fhi6bwRZxX9wBlIc5M/v8CCkXUbXZL4N/nSE97cqg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-2.0.3.tgz"
  "version" "2.0.3"

"babel-helpers@^6.24.1":
  "integrity" "sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-helpers/-/babel-helpers-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-loader@^7.1.1":
  "integrity" "sha512-iCHfbieL5d1LfOQeeVJEUyD9rTwBcP/fcEbRCfempxTDuqrKpu0AZjLAQHEQa3Yqyj9ORKe2iHfoj4rHLf7xpw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-loader/-/babel-loader-7.1.5.tgz"
  "version" "7.1.5"
  dependencies:
    "find-cache-dir" "^1.0.0"
    "loader-utils" "^1.0.2"
    "mkdirp" "^0.5.1"

"babel-messages@^6.23.0":
  "integrity" "sha1-8830cDhYA1sqKVHG7F7fbGLyYw4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-messages/-/babel-messages-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-check-es2015-constants@^6.22.0":
  "integrity" "sha1-NRV7EBQm/S/9PaP3XH0ekYNbv4o="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-check-es2015-constants/-/babel-plugin-check-es2015-constants-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-syntax-async-functions@^6.8.0":
  "integrity" "sha1-ytnK0RkbWtY0vzCuCHI5HgZHvpU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-syntax-async-functions/-/babel-plugin-syntax-async-functions-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-async-generators@^6.5.0":
  "integrity" "sha1-a8lj67FuzLrmuStZbrfzXDQqi5o="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-syntax-async-generators/-/babel-plugin-syntax-async-generators-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-class-properties@^6.8.0":
  "integrity" "sha1-1+sjt5oxf4VDlixQW4J8fWysJ94="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-syntax-class-properties/-/babel-plugin-syntax-class-properties-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-decorators@^6.13.0":
  "integrity" "sha1-MSVjtNvePMgGzuPkFszurd0RrAs="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-syntax-decorators/-/babel-plugin-syntax-decorators-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-dynamic-import@^6.18.0":
  "integrity" "sha1-jWomIpyDdFqZgqRBBRVyyqF5sdo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-syntax-dynamic-import/-/babel-plugin-syntax-dynamic-import-6.18.0.tgz"
  "version" "6.18.0"

"babel-plugin-syntax-exponentiation-operator@^6.8.0":
  "integrity" "sha1-nufoM3KQ2pUoggGmpX9BcDF4MN4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-syntax-exponentiation-operator/-/babel-plugin-syntax-exponentiation-operator-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-jsx@^6.18.0":
  "integrity" "sha1-CvMqmm4Tyno/1QaeYtew9Y0NiUY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-syntax-jsx/-/babel-plugin-syntax-jsx-6.18.0.tgz"
  "version" "6.18.0"

"babel-plugin-syntax-object-rest-spread@^6.8.0":
  "integrity" "sha1-/WU28rzhODb/o6VFjEkDpZe7O/U="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-syntax-object-rest-spread/-/babel-plugin-syntax-object-rest-spread-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-trailing-function-commas@^6.22.0":
  "integrity" "sha1-ugNgk3+NBuQBgKQ/4NVhb/9TLPM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-syntax-trailing-function-commas/-/babel-plugin-syntax-trailing-function-commas-6.22.0.tgz"
  "version" "6.22.0"

"babel-plugin-transform-async-generator-functions@^6.24.1":
  "integrity" "sha1-8FiQAUX9PpkHpt3yjaWfIVJYpds="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-async-generator-functions/-/babel-plugin-transform-async-generator-functions-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-remap-async-to-generator" "^6.24.1"
    "babel-plugin-syntax-async-generators" "^6.5.0"
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-async-to-generator@^6.22.0", "babel-plugin-transform-async-to-generator@^6.24.1":
  "integrity" "sha1-ZTbjeK/2yx1VF6wOQOs+n8jQh2E="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-async-to-generator/-/babel-plugin-transform-async-to-generator-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-remap-async-to-generator" "^6.24.1"
    "babel-plugin-syntax-async-functions" "^6.8.0"
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-class-properties@^6.24.1":
  "integrity" "sha1-anl2PqYdM9NvN7YRqp3vgagbRqw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-class-properties/-/babel-plugin-transform-class-properties-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-plugin-syntax-class-properties" "^6.8.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-decorators@^6.24.1":
  "integrity" "sha1-eIAT2PjGtSIr33s0Q5Df13Vp4k0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-decorators/-/babel-plugin-transform-decorators-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-explode-class" "^6.24.1"
    "babel-plugin-syntax-decorators" "^6.13.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-arrow-functions@^6.22.0":
  "integrity" "sha1-RSaSy3EdX3ncf4XkQM5BufJE0iE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-arrow-functions/-/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-block-scoped-functions@^6.22.0":
  "integrity" "sha1-u8UbSflk1wy42OC5ToICRs46YUE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-block-scoped-functions/-/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-block-scoping@^6.23.0":
  "integrity" "sha1-1w9SmcEwjQXBL0Y4E7CgnnOxiV8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-block-scoping/-/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-plugin-transform-es2015-classes@^6.23.0":
  "integrity" "sha1-WkxYpQyclGHlZLSyo7+ryXolhNs="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-classes/-/babel-plugin-transform-es2015-classes-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-define-map" "^6.24.1"
    "babel-helper-function-name" "^6.24.1"
    "babel-helper-optimise-call-expression" "^6.24.1"
    "babel-helper-replace-supers" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-computed-properties@^6.22.0":
  "integrity" "sha1-b+Ko0WiV1WNPTNmZttNICjCBWbM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-computed-properties/-/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-destructuring@^6.23.0":
  "integrity" "sha1-mXux8auWf2gtKwh2/jWNYOdlxW0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-destructuring/-/babel-plugin-transform-es2015-destructuring-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-duplicate-keys@^6.22.0":
  "integrity" "sha1-c+s9MQypaePvnskcU3QabxV2Qj4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-duplicate-keys/-/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-for-of@^6.23.0":
  "integrity" "sha1-9HyVsrYT3x0+zC/bdXNiPHUkhpE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-for-of/-/babel-plugin-transform-es2015-for-of-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-function-name@^6.22.0":
  "integrity" "sha1-g0yJhTvDaxrw86TF26qU/Y6sqos="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-function-name/-/babel-plugin-transform-es2015-function-name-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-literals@^6.22.0":
  "integrity" "sha1-T1SgLWzWbPkVKAAZox0xklN3yi4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-literals/-/babel-plugin-transform-es2015-literals-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-modules-amd@^6.22.0", "babel-plugin-transform-es2015-modules-amd@^6.24.1":
  "integrity" "sha1-Oz5UAXI5hC1tGcMBHEvS8AoA0VQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-transform-es2015-modules-commonjs" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-modules-commonjs@^6.23.0", "babel-plugin-transform-es2015-modules-commonjs@^6.24.1":
  "integrity" "sha512-CV9ROOHEdrjcwhIaJNBGMBCodN+1cfkwtM1SbUHmvyy35KGT7fohbpOxkE2uLz1o6odKK2Ck/tz47z+VqQfi9Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz"
  "version" "6.26.2"
  dependencies:
    "babel-plugin-transform-strict-mode" "^6.24.1"
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-types" "^6.26.0"

"babel-plugin-transform-es2015-modules-systemjs@^6.23.0":
  "integrity" "sha1-/4mhQrkRmpBhlfXxBuzzBdlAfSM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-hoist-variables" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-modules-umd@^6.23.0":
  "integrity" "sha1-rJl+YoXNGO1hdq22B9YCNErThGg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-transform-es2015-modules-amd" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-object-super@^6.22.0":
  "integrity" "sha1-JM72muIcuDp/hgPa0CH1cusnj40="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-object-super/-/babel-plugin-transform-es2015-object-super-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-replace-supers" "^6.24.1"
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-parameters@^6.23.0":
  "integrity" "sha1-V6w1GrScrxSpfNE7CfZv3wpiXys="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-parameters/-/babel-plugin-transform-es2015-parameters-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-call-delegate" "^6.24.1"
    "babel-helper-get-function-arity" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-shorthand-properties@^6.22.0":
  "integrity" "sha1-JPh11nIch2YbvZmkYi5R8U3jiqA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-shorthand-properties/-/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-spread@^6.22.0":
  "integrity" "sha1-1taKmfia7cRTbIGlQujdnxdG+NE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-spread/-/babel-plugin-transform-es2015-spread-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-sticky-regex@^6.22.0":
  "integrity" "sha1-AMHNsaynERLN8M9hJsLta0V8zbw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-sticky-regex/-/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-regex" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-template-literals@^6.22.0":
  "integrity" "sha1-qEs0UPfp+PH2g51taH2oS7EjbY0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-template-literals/-/babel-plugin-transform-es2015-template-literals-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-typeof-symbol@^6.23.0":
  "integrity" "sha1-3sCfHN3/lLUqxz1QXITfWdzOs3I="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-typeof-symbol/-/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-unicode-regex@^6.22.0":
  "integrity" "sha1-04sS9C6nMj9yk4fxinxa4frrNek="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-es2015-unicode-regex/-/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-regex" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "regexpu-core" "^2.0.0"

"babel-plugin-transform-exponentiation-operator@^6.22.0", "babel-plugin-transform-exponentiation-operator@^6.24.1":
  "integrity" "sha1-KrDJx/MJj6SJB3cruBP+QejeOg4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-builder-binary-assignment-operator-visitor" "^6.24.1"
    "babel-plugin-syntax-exponentiation-operator" "^6.8.0"
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-object-rest-spread@^6.22.0":
  "integrity" "sha1-DzZpLVD+9rfi1LOsFHgTepY7ewY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-object-rest-spread/-/babel-plugin-transform-object-rest-spread-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-plugin-syntax-object-rest-spread" "^6.8.0"
    "babel-runtime" "^6.26.0"

"babel-plugin-transform-regenerator@^6.22.0":
  "integrity" "sha1-4HA2lvveJ/Cj78rPi03KL3s6jy8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-regenerator/-/babel-plugin-transform-regenerator-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "regenerator-transform" "^0.10.0"

"babel-plugin-transform-runtime@^6.22.0":
  "integrity" "sha1-iEkNRGUC6puOfvsP4J7E2ZR5se4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-runtime/-/babel-plugin-transform-runtime-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-strict-mode@^6.24.1":
  "integrity" "sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-strict-mode/-/babel-plugin-transform-strict-mode-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-vue-jsx@^3.5.0":
  "integrity" "sha512-W39X07/n3oJMQd8tALBO+440NraGSF//Lo1ydd/9Nme3+QiRGFBb1Q39T9iixh0jZPPbfv3so18tNoIgLatymw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-plugin-transform-vue-jsx/-/babel-plugin-transform-vue-jsx-3.7.0.tgz"
  "version" "3.7.0"
  dependencies:
    "esutils" "^2.0.2"

"babel-polyfill@^6.26.0":
  "integrity" "sha1-N5k3q8Z9eJWXCtxiHyhM2WbPIVM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-polyfill/-/babel-polyfill-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "core-js" "^2.5.0"
    "regenerator-runtime" "^0.10.5"

"babel-preset-env@^1.3.2":
  "integrity" "sha512-9OR2afuKDneX2/q2EurSftUYM0xGu4O2D9adAhVfADDhrYDaxXV0rBbevVYoY9n6nyX1PmQW/0jtpJvUNr9CHg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-preset-env/-/babel-preset-env-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "babel-plugin-check-es2015-constants" "^6.22.0"
    "babel-plugin-syntax-trailing-function-commas" "^6.22.0"
    "babel-plugin-transform-async-to-generator" "^6.22.0"
    "babel-plugin-transform-es2015-arrow-functions" "^6.22.0"
    "babel-plugin-transform-es2015-block-scoped-functions" "^6.22.0"
    "babel-plugin-transform-es2015-block-scoping" "^6.23.0"
    "babel-plugin-transform-es2015-classes" "^6.23.0"
    "babel-plugin-transform-es2015-computed-properties" "^6.22.0"
    "babel-plugin-transform-es2015-destructuring" "^6.23.0"
    "babel-plugin-transform-es2015-duplicate-keys" "^6.22.0"
    "babel-plugin-transform-es2015-for-of" "^6.23.0"
    "babel-plugin-transform-es2015-function-name" "^6.22.0"
    "babel-plugin-transform-es2015-literals" "^6.22.0"
    "babel-plugin-transform-es2015-modules-amd" "^6.22.0"
    "babel-plugin-transform-es2015-modules-commonjs" "^6.23.0"
    "babel-plugin-transform-es2015-modules-systemjs" "^6.23.0"
    "babel-plugin-transform-es2015-modules-umd" "^6.23.0"
    "babel-plugin-transform-es2015-object-super" "^6.22.0"
    "babel-plugin-transform-es2015-parameters" "^6.23.0"
    "babel-plugin-transform-es2015-shorthand-properties" "^6.22.0"
    "babel-plugin-transform-es2015-spread" "^6.22.0"
    "babel-plugin-transform-es2015-sticky-regex" "^6.22.0"
    "babel-plugin-transform-es2015-template-literals" "^6.22.0"
    "babel-plugin-transform-es2015-typeof-symbol" "^6.23.0"
    "babel-plugin-transform-es2015-unicode-regex" "^6.22.0"
    "babel-plugin-transform-exponentiation-operator" "^6.22.0"
    "babel-plugin-transform-regenerator" "^6.22.0"
    "browserslist" "^3.2.6"
    "invariant" "^2.2.2"
    "semver" "^5.3.0"

"babel-preset-stage-2@^6.22.0":
  "integrity" "sha1-2eKWD7PXEYfw5k7sYrwHdnIZvcE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-preset-stage-2/-/babel-preset-stage-2-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-syntax-dynamic-import" "^6.18.0"
    "babel-plugin-transform-class-properties" "^6.24.1"
    "babel-plugin-transform-decorators" "^6.24.1"
    "babel-preset-stage-3" "^6.24.1"

"babel-preset-stage-3@^6.24.1":
  "integrity" "sha1-g2raCp56f6N8sTj7kyb4eTSkg5U="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-preset-stage-3/-/babel-preset-stage-3-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-syntax-trailing-function-commas" "^6.22.0"
    "babel-plugin-transform-async-generator-functions" "^6.24.1"
    "babel-plugin-transform-async-to-generator" "^6.24.1"
    "babel-plugin-transform-exponentiation-operator" "^6.24.1"
    "babel-plugin-transform-object-rest-spread" "^6.22.0"

"babel-register@^6.26.0":
  "integrity" "sha1-btAhFz4vy0htestFxgCahW9kcHE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-register/-/babel-register-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-core" "^6.26.0"
    "babel-runtime" "^6.26.0"
    "core-js" "^2.5.0"
    "home-or-tmp" "^2.0.0"
    "lodash" "^4.17.4"
    "mkdirp" "^0.5.1"
    "source-map-support" "^0.4.15"

"babel-runtime@^6.18.0", "babel-runtime@^6.22.0", "babel-runtime@^6.26.0":
  "integrity" "sha1-llxwWGaOgrVde/4E/yM3vItWR/4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-runtime/-/babel-runtime-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "core-js" "^2.4.0"
    "regenerator-runtime" "^0.11.0"

"babel-template@^6.24.1", "babel-template@^6.26.0":
  "integrity" "sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-template/-/babel-template-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "lodash" "^4.17.4"

"babel-traverse@^6.24.1", "babel-traverse@^6.26.0":
  "integrity" "sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-traverse/-/babel-traverse-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "debug" "^2.6.8"
    "globals" "^9.18.0"
    "invariant" "^2.2.2"
    "lodash" "^4.17.4"

"babel-types@^6.19.0", "babel-types@^6.24.1", "babel-types@^6.26.0":
  "integrity" "sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babel-types/-/babel-types-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "esutils" "^2.0.2"
    "lodash" "^4.17.4"
    "to-fast-properties" "^1.0.3"

"babylon@^6.18.0":
  "integrity" "sha512-q/UEjfGJ2Cm3oKV71DJz9d25TPnq5rhBVL2Q4fA5wcC3jcrdn7+SssEybFIxwAvvP+YCsCYNKughoF33GxgycQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babylon/-/babylon-6.18.0.tgz"
  "version" "6.18.0"

"babylon@7.0.0-beta.44":
  "integrity" "sha512-5Hlm13BJVAioCHpImtFqNOF2H3ieTOHd0fmFGMxOJ9jgeFqeAwsv3u5P5cR7CSeFrkgHsT19DgFJkHV0/Mcd8g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/babylon/-/babylon-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"

"balanced-match@^0.4.2":
  "integrity" "sha1-yz8+PHMtwPAe5wtAPzAuYddwmDg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/balanced-match/-/balanced-match-0.4.2.tgz"
  "version" "0.4.2"

"balanced-match@^1.0.0":
  "integrity" "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"base@^0.11.1":
  "integrity" "sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/base/-/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-js@^1.0.2":
  "integrity" "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/base64-js/-/base64-js-1.5.1.tgz"
  "version" "1.5.1"

"batch@0.6.1":
  "integrity" "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/batch/-/batch-0.6.1.tgz"
  "version" "0.6.1"

"better-scroll@^2.4.1":
  "integrity" "sha512-I/JzJNUay2vMAkczBa/4x52QBdUGA3Bs/QtrKrxVjSbBHUMG6yR14srQr4aCAWlIra1FBmuddwGRfI6zRagI9Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/better-scroll/-/better-scroll-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "@better-scroll/core" "^2.4.2"
    "@better-scroll/indicators" "^2.4.2"
    "@better-scroll/infinity" "^2.4.2"
    "@better-scroll/mouse-wheel" "^2.4.2"
    "@better-scroll/movable" "^2.4.2"
    "@better-scroll/nested-scroll" "^2.4.2"
    "@better-scroll/observe-dom" "^2.4.2"
    "@better-scroll/observe-image" "^2.4.2"
    "@better-scroll/pull-down" "^2.4.2"
    "@better-scroll/pull-up" "^2.4.2"
    "@better-scroll/scroll-bar" "^2.4.2"
    "@better-scroll/slide" "^2.4.2"
    "@better-scroll/wheel" "^2.4.2"
    "@better-scroll/zoom" "^2.4.2"

"bfj-node4@^5.2.0":
  "integrity" "sha512-SOmOsowQWfXc7ybFARsK3C4MCOWzERaOMV/Fl3Tgjs+5dJWyzo3oa127jL44eMbQiAN17J7SvAs2TRxEScTUmg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/bfj-node4/-/bfj-node4-5.3.1.tgz"
  "version" "5.3.1"
  dependencies:
    "bluebird" "^3.5.1"
    "check-types" "^7.3.0"
    "tryer" "^1.0.0"

"big.js@^3.1.3":
  "integrity" "sha512-+hN/Zh2D08Mx65pZ/4g5bsmNiZUuChDiQfTUQ7qJr4/kuopCr88xZsAXv6mBoZEsUI4OuGHlX59qE94K2mMW8Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/big.js/-/big.js-3.2.0.tgz"
  "version" "3.2.0"

"big.js@^5.2.2":
  "integrity" "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/big.js/-/big.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@^1.0.0":
  "integrity" "sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/binary-extensions/-/binary-extensions-1.13.1.tgz"
  "version" "1.13.1"

"binary-extensions@^2.0.0":
  "integrity" "sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/binary-extensions/-/binary-extensions-2.2.0.tgz"
  "version" "2.2.0"

"bluebird@^3.1.1", "bluebird@^3.4.7", "bluebird@^3.5.1":
  "integrity" "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/bluebird/-/bluebird-3.7.2.tgz"
  "version" "3.7.2"

"bn.js@^4.0.0":
  "integrity" "sha512-c98Bf3tPniI+scsdk237ku1Dc3ujXQTSgyiPUDEOe7tRkhrqridvh8klBv0HCEso1OLOYcHuCv/cS6DNxKH+ZA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/bn.js/-/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^4.1.0":
  "integrity" "sha512-c98Bf3tPniI+scsdk237ku1Dc3ujXQTSgyiPUDEOe7tRkhrqridvh8klBv0HCEso1OLOYcHuCv/cS6DNxKH+ZA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/bn.js/-/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^4.11.9":
  "integrity" "sha512-c98Bf3tPniI+scsdk237ku1Dc3ujXQTSgyiPUDEOe7tRkhrqridvh8klBv0HCEso1OLOYcHuCv/cS6DNxKH+ZA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/bn.js/-/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^5.0.0", "bn.js@^5.1.1":
  "integrity" "sha512-D7iWRBvnZE8ecXiLj/9wbxH7Tk79fAh8IHaTNq1RWRixsS02W+5qS+iE9yq6RYl0asXx5tw0bLhmT5pIfbSquw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/bn.js/-/bn.js-5.2.0.tgz"
  "version" "5.2.0"

"body-parser@1.19.2":
  "integrity" "sha512-SAAwOxgoCKMGs9uUAUFHygfLAyaniaoun6I8mFY9pRAJL9+Kec34aU+oIjDhTycub1jozEfEwx1W1IuOYxVSFw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/body-parser/-/body-parser-1.19.2.tgz"
  "version" "1.19.2"
  dependencies:
    "bytes" "3.1.2"
    "content-type" "~1.0.4"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "http-errors" "1.8.1"
    "iconv-lite" "0.4.24"
    "on-finished" "~2.3.0"
    "qs" "6.9.7"
    "raw-body" "2.4.3"
    "type-is" "~1.6.18"

"bonjour@^3.5.0":
  "integrity" "sha1-jokKGD2O6aI5OzhExpGkK897yfU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/bonjour/-/bonjour-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "array-flatten" "^2.1.0"
    "deep-equal" "^1.0.1"
    "dns-equal" "^1.0.0"
    "dns-txt" "^2.0.2"
    "multicast-dns" "^6.0.1"
    "multicast-dns-service-types" "^1.1.0"

"boolbase@^1.0.0", "boolbase@~1.0.0":
  "integrity" "sha1-aN/1++YMUes3cl6p4+0xDcwed24="
  "resolved" "https://repo.huaweicloud.com/repository/npm/boolbase/-/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"brace-expansion@^1.1.7":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@^2.3.1":
  "integrity" "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/braces/-/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"braces@^2.3.2":
  "integrity" "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/braces/-/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"braces@~3.0.2":
  "integrity" "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/braces/-/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"brorand@^1.0.1", "brorand@^1.1.0":
  "integrity" "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/brorand/-/brorand-1.1.0.tgz"
  "version" "1.1.0"

"browserify-aes@^1.0.0", "browserify-aes@^1.0.4":
  "integrity" "sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/browserify-aes/-/browserify-aes-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "buffer-xor" "^1.0.3"
    "cipher-base" "^1.0.0"
    "create-hash" "^1.1.0"
    "evp_bytestokey" "^1.0.3"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"browserify-cipher@^1.0.0":
  "integrity" "sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/browserify-cipher/-/browserify-cipher-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "browserify-aes" "^1.0.4"
    "browserify-des" "^1.0.0"
    "evp_bytestokey" "^1.0.0"

"browserify-des@^1.0.0":
  "integrity" "sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/browserify-des/-/browserify-des-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "cipher-base" "^1.0.1"
    "des.js" "^1.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"browserify-rsa@^4.0.0", "browserify-rsa@^4.0.1":
  "integrity" "sha512-AdEER0Hkspgno2aR97SAf6vi0y0k8NuOpGnVH3O99rcA5Q6sh8QxcngtHuJ6uXwnfAXNM4Gn1Gb7/MV1+Ymbog=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/browserify-rsa/-/browserify-rsa-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "bn.js" "^5.0.0"
    "randombytes" "^2.0.1"

"browserify-sign@^4.0.0":
  "integrity" "sha512-/vrA5fguVAKKAVTNJjgSm1tRQDHUU6DbwO9IROu/0WAzC8PKhucDSh18J0RMvVeHAn5puMd+QHC2erPRNf8lmg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/browserify-sign/-/browserify-sign-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "bn.js" "^5.1.1"
    "browserify-rsa" "^4.0.1"
    "create-hash" "^1.2.0"
    "create-hmac" "^1.1.7"
    "elliptic" "^6.5.3"
    "inherits" "^2.0.4"
    "parse-asn1" "^5.1.5"
    "readable-stream" "^3.6.0"
    "safe-buffer" "^5.2.0"

"browserify-zlib@^0.2.0":
  "integrity" "sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/browserify-zlib/-/browserify-zlib-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "pako" "~1.0.5"

"browserslist@^1.3.6":
  "integrity" "sha1-C9dnBCWL6CmyOYu1Dkti0aFmsLk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/browserslist/-/browserslist-1.7.7.tgz"
  "version" "1.7.7"
  dependencies:
    "caniuse-db" "^1.0.30000639"
    "electron-to-chromium" "^1.2.7"

"browserslist@^1.5.2":
  "integrity" "sha1-C9dnBCWL6CmyOYu1Dkti0aFmsLk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/browserslist/-/browserslist-1.7.7.tgz"
  "version" "1.7.7"
  dependencies:
    "caniuse-db" "^1.0.30000639"
    "electron-to-chromium" "^1.2.7"

"browserslist@^1.7.6":
  "integrity" "sha1-C9dnBCWL6CmyOYu1Dkti0aFmsLk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/browserslist/-/browserslist-1.7.7.tgz"
  "version" "1.7.7"
  dependencies:
    "caniuse-db" "^1.0.30000639"
    "electron-to-chromium" "^1.2.7"

"browserslist@^2.11.3":
  "integrity" "sha512-yWu5cXT7Av6mVwzWc8lMsJMHWn4xyjSuGYi4IozbVTLUOEYPSagUB8kiMDUHA1fS3zjr8nkxkn9jdvug4BBRmA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/browserslist/-/browserslist-2.11.3.tgz"
  "version" "2.11.3"
  dependencies:
    "caniuse-lite" "^1.0.30000792"
    "electron-to-chromium" "^1.3.30"

"browserslist@^3.2.6":
  "integrity" "sha512-WHVocJYavUwVgVViC0ORikPHQquXwVh939TaelZ4WDqpWgTX/FsGhl/+P4qBUAGcRvtOgDgC+xftNWWp2RUTAQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/browserslist/-/browserslist-3.2.8.tgz"
  "version" "3.2.8"
  dependencies:
    "caniuse-lite" "^1.0.30000844"
    "electron-to-chromium" "^1.3.47"

"browserslist@^4.0.0":
  "integrity" "sha512-CQOBCqp/9pDvDbx3xfMi+86pr4KXIf2FDkTTdeuYw8OxS9t898LA1Khq57gtufFILXpfgsSx5woNgsBgvGjpsA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/browserslist/-/browserslist-4.20.2.tgz"
  "version" "4.20.2"
  dependencies:
    "caniuse-lite" "^1.0.30001317"
    "electron-to-chromium" "^1.4.84"
    "escalade" "^3.1.1"
    "node-releases" "^2.0.2"
    "picocolors" "^1.0.0"

"buffer-from@^1.0.0":
  "integrity" "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/buffer-from/-/buffer-from-1.1.2.tgz"
  "version" "1.1.2"

"buffer-indexof@^1.0.0":
  "integrity" "sha512-4/rOEg86jivtPTeOUUT61jJO1Ya1TrR/OkqCSZDyq84WJh3LuuiphBYJN+fm5xufIk4XAFcEwte/8WzC8If/1g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/buffer-indexof/-/buffer-indexof-1.1.1.tgz"
  "version" "1.1.1"

"buffer-xor@^1.0.3":
  "integrity" "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/buffer-xor/-/buffer-xor-1.0.3.tgz"
  "version" "1.0.3"

"buffer@^4.3.0":
  "integrity" "sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/buffer/-/buffer-4.9.2.tgz"
  "version" "4.9.2"
  dependencies:
    "base64-js" "^1.0.2"
    "ieee754" "^1.1.4"
    "isarray" "^1.0.0"

"builtin-status-codes@^3.0.0":
  "integrity" "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug="
  "resolved" "https://repo.huaweicloud.com/repository/npm/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.0.0":
  "integrity" "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/bytes/-/bytes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.1.2":
  "integrity" "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/bytes/-/bytes-3.1.2.tgz"
  "version" "3.1.2"

"cacache@^10.0.4":
  "integrity" "sha512-Dph0MzuH+rTQzGPNT9fAnrPmMmjKfST6trxJeK7NQuHRaVw24VzPRWTmg9MpcwOVQZO0E1FBICUlFeNaKPIfHA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cacache/-/cacache-10.0.4.tgz"
  "version" "10.0.4"
  dependencies:
    "bluebird" "^3.5.1"
    "chownr" "^1.0.1"
    "glob" "^7.1.2"
    "graceful-fs" "^4.1.11"
    "lru-cache" "^4.1.1"
    "mississippi" "^2.0.0"
    "mkdirp" "^0.5.1"
    "move-concurrently" "^1.0.1"
    "promise-inflight" "^1.0.1"
    "rimraf" "^2.6.2"
    "ssri" "^5.2.4"
    "unique-filename" "^1.1.0"
    "y18n" "^4.0.0"

"cache-base@^1.0.1":
  "integrity" "sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cache-base/-/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"call-bind@^1.0.0", "call-bind@^1.0.2":
  "integrity" "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/call-bind/-/call-bind-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.0.2"

"caller-callsite@^2.0.0":
  "integrity" "sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/caller-callsite/-/caller-callsite-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "callsites" "^2.0.0"

"caller-path@^0.1.0":
  "integrity" "sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/caller-path/-/caller-path-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "callsites" "^0.2.0"

"caller-path@^2.0.0":
  "integrity" "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/caller-path/-/caller-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-callsite" "^2.0.0"

"callsites@^0.2.0":
  "integrity" "sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/callsites/-/callsites-0.2.0.tgz"
  "version" "0.2.0"

"callsites@^2.0.0":
  "integrity" "sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/callsites/-/callsites-2.0.0.tgz"
  "version" "2.0.0"

"camel-case@3.0.x":
  "integrity" "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M="
  "resolved" "https://repo.huaweicloud.com/repository/npm/camel-case/-/camel-case-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "no-case" "^2.2.0"
    "upper-case" "^1.1.1"

"camelcase-keys@^2.0.0":
  "integrity" "sha1-MIvur/3ygRkFHvodkyITyRuPkuc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/camelcase-keys/-/camelcase-keys-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "camelcase" "^2.0.0"
    "map-obj" "^1.0.0"

"camelcase@^1.0.2":
  "integrity" "sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/camelcase/-/camelcase-1.2.1.tgz"
  "version" "1.2.1"

"camelcase@^2.0.0":
  "integrity" "sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/camelcase/-/camelcase-2.1.1.tgz"
  "version" "2.1.1"

"camelcase@^3.0.0":
  "integrity" "sha1-MvxLn82vhF/N9+c7uXysImHwqwo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/camelcase/-/camelcase-3.0.0.tgz"
  "version" "3.0.0"

"camelcase@^4.1.0":
  "integrity" "sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/camelcase/-/camelcase-4.1.0.tgz"
  "version" "4.1.0"

"caniuse-api@^1.5.2":
  "integrity" "sha1-tTTnxzTE+B7F++isoq0kNUuWLGw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/caniuse-api/-/caniuse-api-1.6.1.tgz"
  "version" "1.6.1"
  dependencies:
    "browserslist" "^1.3.6"
    "caniuse-db" "^1.0.30000529"
    "lodash.memoize" "^4.1.2"
    "lodash.uniq" "^4.5.0"

"caniuse-api@^3.0.0":
  "integrity" "sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/caniuse-api/-/caniuse-api-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-lite" "^1.0.0"
    "lodash.memoize" "^4.1.2"
    "lodash.uniq" "^4.5.0"

"caniuse-db@^1.0.30000529", "caniuse-db@^1.0.30000634", "caniuse-db@^1.0.30000639":
  "integrity" "sha512-k8IP22eSycsYwJdGR7+Yngga7pg7NWE1iQ3PnSJkNkahUkZI6FUOAHSKS6Ewqi5BrketJZSi7PjVph7SUgxcNg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/caniuse-db/-/caniuse-db-1.0.30001320.tgz"
  "version" "1.0.30001320"

"caniuse-lite@^1.0.0", "caniuse-lite@^1.0.30000792", "caniuse-lite@^1.0.30000805", "caniuse-lite@^1.0.30000844", "caniuse-lite@^1.0.30001317":
  "integrity" "sha512-1/Cg4jlD9qjZzhbzkzEaAC2JHsP0WrOc8Rd/3a3LuajGzGWR/hD7TVyvq99VqmTy99eVh8Zkmdq213OgvgXx7w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/caniuse-lite/-/caniuse-lite-1.0.30001327.tgz"
  "version" "1.0.30001327"

"center-align@^0.1.1":
  "integrity" "sha1-qg0yYptu6XIgBBHL1EYckHvCt60="
  "resolved" "https://repo.huaweicloud.com/repository/npm/center-align/-/center-align-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "align-text" "^0.1.3"
    "lazy-cache" "^1.0.3"

"chalk@^1.0.0":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/chalk/-/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^1.1.3":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/chalk/-/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^2.0.0", "chalk@^2.0.1", "chalk@^2.1.0", "chalk@^2.3.0", "chalk@^2.4.1":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chardet@^0.4.0":
  "integrity" "sha1-tUc7M9yXxCTl2Y3IfVXU2KKci/I="
  "resolved" "https://repo.huaweicloud.com/repository/npm/chardet/-/chardet-0.4.2.tgz"
  "version" "0.4.2"

"check-types@^7.3.0":
  "integrity" "sha512-YbulWHdfP99UfZ73NcUDlNJhEIDgm9Doq9GhpyXbF+7Aegi3CVV7qqMCKTTqJxlvEvnQBp9IA+dxsGN6xK/nSg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/check-types/-/check-types-7.4.0.tgz"
  "version" "7.4.0"

"chokidar@^2.1.2":
  "integrity" "sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/chokidar/-/chokidar-2.1.8.tgz"
  "version" "2.1.8"
  dependencies:
    "anymatch" "^2.0.0"
    "async-each" "^1.0.1"
    "braces" "^2.3.2"
    "glob-parent" "^3.1.0"
    "inherits" "^2.0.3"
    "is-binary-path" "^1.0.0"
    "is-glob" "^4.0.0"
    "normalize-path" "^3.0.0"
    "path-is-absolute" "^1.0.0"
    "readdirp" "^2.2.1"
    "upath" "^1.1.1"
  optionalDependencies:
    "fsevents" "^1.2.7"

"chokidar@^2.1.8":
  "integrity" "sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/chokidar/-/chokidar-2.1.8.tgz"
  "version" "2.1.8"
  dependencies:
    "anymatch" "^2.0.0"
    "async-each" "^1.0.1"
    "braces" "^2.3.2"
    "glob-parent" "^3.1.0"
    "inherits" "^2.0.3"
    "is-binary-path" "^1.0.0"
    "is-glob" "^4.0.0"
    "normalize-path" "^3.0.0"
    "path-is-absolute" "^1.0.0"
    "readdirp" "^2.2.1"
    "upath" "^1.1.1"
  optionalDependencies:
    "fsevents" "^1.2.7"

"chokidar@^3.4.1":
  "integrity" "sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/chokidar/-/chokidar-3.5.3.tgz"
  "version" "3.5.3"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"chownr@^1.0.1":
  "integrity" "sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/chownr/-/chownr-1.1.4.tgz"
  "version" "1.1.4"

"cipher-base@^1.0.0", "cipher-base@^1.0.1", "cipher-base@^1.0.3":
  "integrity" "sha512-Kkht5ye6ZGmwv40uUDZztayT2ThLQGfnj/T71N/XzeZeo3nf8foyW7zGTsPYkEya3m5f3cAypH+qe7YOrM1U2Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cipher-base/-/cipher-base-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"circular-json@^0.3.1":
  "integrity" "sha512-UZK3NBx2Mca+b5LsG7bY183pHWt5Y1xts4P3Pz7ENTwGVnJOUWbRb3ocjvX7hx9tq/yTAdclXm9sZ38gNuem4A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/circular-json/-/circular-json-0.3.3.tgz"
  "version" "0.3.3"

"clap@^1.0.9":
  "integrity" "sha512-4CoL/A3hf90V3VIEjeuhSvlGFEHKzOz+Wfc2IVZc+FaUgU0ZQafJTP49fvnULipOPcAfqhyI2duwQyns6xqjYA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/clap/-/clap-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "chalk" "^1.1.3"

"class-utils@^0.3.5":
  "integrity" "sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/class-utils/-/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"clean-css@4.2.x":
  "integrity" "sha512-EJUDT7nDVFDvaQgAo2G/PJvxmp1o/c6iXLbswsBbUFXi1Nr+AjA2cKmfbKDMjMvzEe75g3P6JkaDDAKk96A85A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/clean-css/-/clean-css-4.2.4.tgz"
  "version" "4.2.4"
  dependencies:
    "source-map" "~0.6.0"

"cli-cursor@^2.1.0":
  "integrity" "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cli-cursor/-/cli-cursor-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "restore-cursor" "^2.0.0"

"cli-spinners@^1.0.1":
  "integrity" "sha512-1QL4544moEsDVH9T/l6Cemov/37iv1RtoKf7NJ04A60+4MREXNfx/QvavbH6QoGdsD4N4Mwy49cmaINR/o2mdg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cli-spinners/-/cli-spinners-1.3.1.tgz"
  "version" "1.3.1"

"cli-width@^2.0.0":
  "integrity" "sha512-GRMWDxpOB6Dgk2E5Uo+3eEBvtOOlimMmpbFiKuLFnQzYDavtLFY3K5ona41jgN/WdRZtG7utuVSVTL4HbZHGkw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cli-width/-/cli-width-2.2.1.tgz"
  "version" "2.2.1"

"cliui@^2.1.0":
  "integrity" "sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cliui/-/cliui-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "center-align" "^0.1.1"
    "right-align" "^0.1.1"
    "wordwrap" "0.0.2"

"cliui@^3.2.0":
  "integrity" "sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cliui/-/cliui-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "string-width" "^1.0.1"
    "strip-ansi" "^3.0.1"
    "wrap-ansi" "^2.0.0"

"clone@^1.0.2":
  "integrity" "sha1-2jCcwmPfFZlMaIypAheco8fNfH4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/clone/-/clone-1.0.4.tgz"
  "version" "1.0.4"

"clone@^2.1.1":
  "integrity" "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="
  "resolved" "https://repo.huaweicloud.com/repository/npm/clone/-/clone-2.1.2.tgz"
  "version" "2.1.2"

"co@^4.6.0":
  "integrity" "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/co/-/co-4.6.0.tgz"
  "version" "4.6.0"

"coa@^2.0.2":
  "integrity" "sha512-q5/jG+YQnSy4nRTV4F7lPepBJZ8qBNJJDBuJdoejDyLXgmL7IEo+Le2JDZudFTFt7mrCqIRaSjws4ygRCTCAXA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/coa/-/coa-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "@types/q" "^1.5.1"
    "chalk" "^2.4.1"
    "q" "^1.1.2"

"coa@~1.0.1":
  "integrity" "sha1-qe8VNmDWqGqL3sAomlxoTSF0Mv0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/coa/-/coa-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "q" "^1.1.2"

"coalescy@1.0.0":
  "integrity" "sha1-SwZYRrg2NhrabEtKSr9LwcrDG/E="
  "resolved" "https://repo.huaweicloud.com/repository/npm/coalescy/-/coalescy-1.0.0.tgz"
  "version" "1.0.0"

"code-point-at@^1.0.0":
  "integrity" "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c="
  "resolved" "https://repo.huaweicloud.com/repository/npm/code-point-at/-/code-point-at-1.1.0.tgz"
  "version" "1.1.0"

"collection-visit@^1.0.0":
  "integrity" "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/collection-visit/-/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.3.0", "color-convert@^1.9.0", "color-convert@^1.9.3":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-name@^1.0.0", "color-name@1.1.3":
  "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"color-string@^0.3.0":
  "integrity" "sha1-J9RvtnAlxcL6JZk7+/V55HhBuZE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/color-string/-/color-string-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "color-name" "^1.0.0"

"color-string@^1.6.0":
  "integrity" "sha512-9Mrz2AQLefkH1UvASKj6v6hj/7eWgjnT/cVsR8CumieLoT+g900exWeNogqtweI8dxloXN9BDQTYro1oWu/5CQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/color-string/-/color-string-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "color-name" "^1.0.0"
    "simple-swizzle" "^0.2.2"

"color@^0.11.0":
  "integrity" "sha1-bXtcdPtl6EHNSHkq0e1eB7kE12Q="
  "resolved" "https://repo.huaweicloud.com/repository/npm/color/-/color-0.11.4.tgz"
  "version" "0.11.4"
  dependencies:
    "clone" "^1.0.2"
    "color-convert" "^1.3.0"
    "color-string" "^0.3.0"

"color@^3.0.0":
  "integrity" "sha512-aBl7dZI9ENN6fUGC7mWpMTPNHmWUSNan9tuWN6ahh5ZLNk9baLJOnSMlrQkHcrfFgz2/RigjUVAjdx36VcemKA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/color/-/color-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.3"
    "color-string" "^1.6.0"

"colormin@^1.0.5":
  "integrity" "sha1-6i90IKcrlogaOKrlnsEkpvcpgTM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/colormin/-/colormin-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "color" "^0.11.0"
    "css-color-names" "0.0.4"
    "has" "^1.0.1"

"colors@~1.1.2":
  "integrity" "sha1-FopHAXVran9RoSzgyXv6KMCE7WM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/colors/-/colors-1.1.2.tgz"
  "version" "1.1.2"

"commander@^2.13.0", "commander@2.17.x":
  "integrity" "sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/commander/-/commander-2.17.1.tgz"
  "version" "2.17.1"

"commander@~2.13.0":
  "integrity" "sha512-MVuS359B+YzaWqjCL/c+22gfryv+mCBPHAv3zyVI2GN8EY6IRP8VwtasXn8jyyhvvq84R4ImN1OKRtcbIasjYA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/commander/-/commander-2.13.0.tgz"
  "version" "2.13.0"

"commander@~2.19.0":
  "integrity" "sha512-6tvAOO+D6OENvRAh524Dh9jcfKTYDQAqvqezbCW82xj5X0pSrcpxtvRKHLG0yBY6SD7PSDrJaj+0AiOcKVd1Xg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/commander/-/commander-2.19.0.tgz"
  "version" "2.19.0"

"commondir@^1.0.1":
  "integrity" "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="
  "resolved" "https://repo.huaweicloud.com/repository/npm/commondir/-/commondir-1.0.1.tgz"
  "version" "1.0.1"

"component-emitter@^1.2.1":
  "integrity" "sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/component-emitter/-/component-emitter-1.3.0.tgz"
  "version" "1.3.0"

"compressible@~2.0.16":
  "integrity" "sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/compressible/-/compressible-2.0.18.tgz"
  "version" "2.0.18"
  dependencies:
    "mime-db" ">= 1.43.0 < 2"

"compression@^1.7.3":
  "integrity" "sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/compression/-/compression-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "accepts" "~1.3.5"
    "bytes" "3.0.0"
    "compressible" "~2.0.16"
    "debug" "2.6.9"
    "on-headers" "~1.0.2"
    "safe-buffer" "5.1.2"
    "vary" "~1.1.2"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
  "resolved" "https://repo.huaweicloud.com/repository/npm/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"concat-stream@^1.5.0", "concat-stream@^1.6.0":
  "integrity" "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/concat-stream/-/concat-stream-1.6.2.tgz"
  "version" "1.6.2"
  dependencies:
    "buffer-from" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^2.2.2"
    "typedarray" "^0.0.6"

"connect-history-api-fallback@^1.3.0":
  "integrity" "sha512-e54B99q/OUoH64zYYRf3HBP5z24G38h5D3qXu23JGRoigpX5Ss4r9ZnDk3g0Z8uQC2x2lPaJ+UlWBc1ZWBWdLg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz"
  "version" "1.6.0"

"console-browserify@^1.1.0":
  "integrity" "sha512-ZMkYO/LkF17QvCPqM0gxw8yUzigAOZOSWSHg91FH6orS7vcEj5dVZTidN2fQ14yBSdg97RqhSNwLUXInd52OTA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/console-browserify/-/console-browserify-1.2.0.tgz"
  "version" "1.2.0"

"consolidate@^0.14.0":
  "integrity" "sha1-WiUEe8dvcwcmZ8jLUsmJiI9JTGM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/consolidate/-/consolidate-0.14.5.tgz"
  "version" "0.14.5"
  dependencies:
    "bluebird" "^3.1.1"

"constants-browserify@^1.0.0":
  "integrity" "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U="
  "resolved" "https://repo.huaweicloud.com/repository/npm/constants-browserify/-/constants-browserify-1.0.0.tgz"
  "version" "1.0.0"

"content-disposition@0.5.4":
  "integrity" "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/content-disposition/-/content-disposition-0.5.4.tgz"
  "version" "0.5.4"
  dependencies:
    "safe-buffer" "5.2.1"

"content-type@~1.0.4":
  "integrity" "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/content-type/-/content-type-1.0.4.tgz"
  "version" "1.0.4"

"convert-source-map@^1.5.1":
  "integrity" "sha512-+OQdjP49zViI/6i7nIJpA8rAl4sV/JdPfU9nZs3VqOwGIgizICvuN2ru6fMd+4llL0tar18UYJXfZ/TWtmhUjA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/convert-source-map/-/convert-source-map-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "safe-buffer" "~5.1.1"

"cookie-signature@1.0.6":
  "integrity" "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cookie-signature/-/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie@0.4.2":
  "integrity" "sha512-aSWTXFzaKWkvHO1Ny/s+ePFpvKsPnjc551iI41v3ny/ow6tBG5Vd+FuqGNhh1LxOmVzOlGUriIlOaokOvhaStA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cookie/-/cookie-0.4.2.tgz"
  "version" "0.4.2"

"copy-anything@^2.0.1":
  "integrity" "sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/copy-anything/-/copy-anything-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "is-what" "^3.14.1"

"copy-concurrently@^1.0.0":
  "integrity" "sha512-f2domd9fsVDFtaFcbaRZuYXwtdmnzqbADSwhSWYxYB/Q8zsdUUFMXVRwXGDMWmbEzAn1kdRrtI1T/KTFOL4X2A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/copy-concurrently/-/copy-concurrently-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "aproba" "^1.1.1"
    "fs-write-stream-atomic" "^1.0.8"
    "iferr" "^0.1.5"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.0"

"copy-descriptor@^0.1.0":
  "integrity" "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="
  "resolved" "https://repo.huaweicloud.com/repository/npm/copy-descriptor/-/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"copy-webpack-plugin@^4.0.1":
  "integrity" "sha512-Y+SQCF+0NoWQryez2zXn5J5knmr9z/9qSQt7fbL78u83rxmigOy8X5+BFn8CFSuX+nKT8gpYwJX68ekqtQt6ZA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/copy-webpack-plugin/-/copy-webpack-plugin-4.6.0.tgz"
  "version" "4.6.0"
  dependencies:
    "cacache" "^10.0.4"
    "find-cache-dir" "^1.0.0"
    "globby" "^7.1.1"
    "is-glob" "^4.0.0"
    "loader-utils" "^1.1.0"
    "minimatch" "^3.0.4"
    "p-limit" "^1.0.0"
    "serialize-javascript" "^1.4.0"

"core-js@^2.4.0", "core-js@^2.5.0":
  "integrity" "sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/core-js/-/core-js-2.6.12.tgz"
  "version" "2.6.12"

"core-util-is@~1.0.0":
  "integrity" "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/core-util-is/-/core-util-is-1.0.3.tgz"
  "version" "1.0.3"

"cosmiconfig@^2.1.0":
  "integrity" "sha512-GiNXLwAFPYHy25XmTPpafYvn3CLAkJ8FLsscq78MQd1Kh0OU6Yzhn4eV2MVF4G9WEQZoWEGltatdR+ntGPMl5A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cosmiconfig/-/cosmiconfig-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "is-directory" "^0.3.1"
    "js-yaml" "^3.4.3"
    "minimist" "^1.2.0"
    "object-assign" "^4.1.0"
    "os-homedir" "^1.0.1"
    "parse-json" "^2.2.0"
    "require-from-string" "^1.1.0"

"cosmiconfig@^2.1.1":
  "integrity" "sha512-GiNXLwAFPYHy25XmTPpafYvn3CLAkJ8FLsscq78MQd1Kh0OU6Yzhn4eV2MVF4G9WEQZoWEGltatdR+ntGPMl5A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cosmiconfig/-/cosmiconfig-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "is-directory" "^0.3.1"
    "js-yaml" "^3.4.3"
    "minimist" "^1.2.0"
    "object-assign" "^4.1.0"
    "os-homedir" "^1.0.1"
    "parse-json" "^2.2.0"
    "require-from-string" "^1.1.0"

"cosmiconfig@^5.0.0":
  "integrity" "sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cosmiconfig/-/cosmiconfig-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "import-fresh" "^2.0.0"
    "is-directory" "^0.3.1"
    "js-yaml" "^3.13.1"
    "parse-json" "^4.0.0"

"create-ecdh@^4.0.0":
  "integrity" "sha512-mf+TCx8wWc9VpuxfP2ht0iSISLZnt0JgWlrOKZiNqyUZWnjIaCIVNQArMHnCZKfEYRg6IM7A+NeJoN8gf/Ws0A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/create-ecdh/-/create-ecdh-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "bn.js" "^4.1.0"
    "elliptic" "^6.5.3"

"create-hash@^1.1.0", "create-hash@^1.1.2", "create-hash@^1.2.0":
  "integrity" "sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/create-hash/-/create-hash-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cipher-base" "^1.0.1"
    "inherits" "^2.0.1"
    "md5.js" "^1.3.4"
    "ripemd160" "^2.0.1"
    "sha.js" "^2.4.0"

"create-hmac@^1.1.0", "create-hmac@^1.1.4", "create-hmac@^1.1.7":
  "integrity" "sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/create-hmac/-/create-hmac-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "cipher-base" "^1.0.3"
    "create-hash" "^1.1.0"
    "inherits" "^2.0.1"
    "ripemd160" "^2.0.0"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"cross-spawn@^5.0.1", "cross-spawn@^5.1.0":
  "integrity" "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cross-spawn/-/cross-spawn-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "lru-cache" "^4.0.1"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"crypto-browserify@^3.11.0":
  "integrity" "sha512-fz4spIh+znjO2VjL+IdhEpRJ3YN6sMzITSBijk6FK2UvTqruSQW+/cCZTSNsMiZNvUeq0CqurF+dAbyiGOY6Wg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/crypto-browserify/-/crypto-browserify-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "browserify-cipher" "^1.0.0"
    "browserify-sign" "^4.0.0"
    "create-ecdh" "^4.0.0"
    "create-hash" "^1.1.0"
    "create-hmac" "^1.1.0"
    "diffie-hellman" "^5.0.0"
    "inherits" "^2.0.1"
    "pbkdf2" "^3.0.3"
    "public-encrypt" "^4.0.0"
    "randombytes" "^2.0.0"
    "randomfill" "^1.0.3"

"css-color-names@^0.0.4", "css-color-names@0.0.4":
  "integrity" "sha1-gIrcLnnPhHOAabZGyyDsJ762KeA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/css-color-names/-/css-color-names-0.0.4.tgz"
  "version" "0.0.4"

"css-declaration-sorter@^4.0.1":
  "integrity" "sha512-BcxQSKTSEEQUftYpBVnsH4SF05NTuBokb19/sBt6asXGKZ/6VP7PLG1CBCkFDYOnhXhPh0jMhO6xZ71oYHXHBA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/css-declaration-sorter/-/css-declaration-sorter-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.1"
    "timsort" "^0.3.0"

"css-loader@^0.28.0":
  "integrity" "sha512-wovHgjAx8ZIMGSL8pTys7edA1ClmzxHeY6n/d97gg5odgsxEgKjULPR0viqyC+FWMCL9sfqoC/QCUBo62tLvPg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/css-loader/-/css-loader-0.28.11.tgz"
  "version" "0.28.11"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "css-selector-tokenizer" "^0.7.0"
    "cssnano" "^3.10.0"
    "icss-utils" "^2.1.0"
    "loader-utils" "^1.0.2"
    "lodash.camelcase" "^4.3.0"
    "object-assign" "^4.1.1"
    "postcss" "^5.0.6"
    "postcss-modules-extract-imports" "^1.2.0"
    "postcss-modules-local-by-default" "^1.2.0"
    "postcss-modules-scope" "^1.1.0"
    "postcss-modules-values" "^1.3.0"
    "postcss-value-parser" "^3.3.0"
    "source-list-map" "^2.0.0"

"css-parse@~2.0.0":
  "integrity" "sha1-pGjuZnwW2BzPBcWMONKpfHgNv9Q="
  "resolved" "https://repo.huaweicloud.com/repository/npm/css-parse/-/css-parse-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "css" "^2.0.0"

"css-select-base-adapter@^0.1.1":
  "integrity" "sha512-jQVeeRG70QI08vSTwf1jHxp74JoZsr2XSgETae8/xC8ovSnL2WF87GTLO86Sbwdt2lK4Umg4HnnwMO4YF3Ce7w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/css-select-base-adapter/-/css-select-base-adapter-0.1.1.tgz"
  "version" "0.1.1"

"css-select@^2.0.0":
  "integrity" "sha512-Dqk7LQKpwLoH3VovzZnkzegqNSuAziQyNZUcrdDM401iY+R5NkGBXGmtO05/yaXQziALuPogeG0b7UAgjnTJTQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/css-select/-/css-select-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^3.2.1"
    "domutils" "^1.7.0"
    "nth-check" "^1.0.2"

"css-select@^4.1.3":
  "integrity" "sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/css-select/-/css-select-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^6.0.1"
    "domhandler" "^4.3.1"
    "domutils" "^2.8.0"
    "nth-check" "^2.0.1"

"css-selector-tokenizer@^0.7.0":
  "integrity" "sha512-jWQv3oCEL5kMErj4wRnK/OPoBi0D+P1FR2cDCKYPaMeD2eW3/mttav8HT4hT1CKopiJI/psEULjkClhvJo4Lvg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/css-selector-tokenizer/-/css-selector-tokenizer-0.7.3.tgz"
  "version" "0.7.3"
  dependencies:
    "cssesc" "^3.0.0"
    "fastparse" "^1.1.2"

"css-tree@^1.1.2":
  "integrity" "sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/css-tree/-/css-tree-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "mdn-data" "2.0.14"
    "source-map" "^0.6.1"

"css-tree@1.0.0-alpha.37":
  "integrity" "sha512-DMxWJg0rnz7UgxKT0Q1HU/L9BeJI0M6ksor0OgqOnF+aRCDWg/N2641HmVyU9KVIu0OVVWOb2IpC9A+BJRnejg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/css-tree/-/css-tree-1.0.0-alpha.37.tgz"
  "version" "1.0.0-alpha.37"
  dependencies:
    "mdn-data" "2.0.4"
    "source-map" "^0.6.1"

"css-what@^3.2.1":
  "integrity" "sha512-ACUm3L0/jiZTqfzRM3Hi9Q8eZqd6IK37mMWPLz9PJxkLWllYeRf+EHUSHYEtFop2Eqytaq1FizFVh7XfBnXCDQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/css-what/-/css-what-3.4.2.tgz"
  "version" "3.4.2"

"css-what@^6.0.1":
  "integrity" "sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/css-what/-/css-what-6.1.0.tgz"
  "version" "6.1.0"

"css@^2.0.0":
  "integrity" "sha512-oUnjmWpy0niI3x/mPL8dVEI1l7MnG3+HHyRPHf+YFSbK+svOhXpmSOcDURUh2aOCgl2grzrOPt1nHLuCVFULLw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/css/-/css-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "inherits" "^2.0.3"
    "source-map" "^0.6.1"
    "source-map-resolve" "^0.5.2"
    "urix" "^0.1.0"

"cssesc@^3.0.0":
  "integrity" "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cssesc/-/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"cssnano-preset-default@^4.0.8":
  "integrity" "sha512-LdAyHuq+VRyeVREFmuxUZR1TXjQm8QQU/ktoo/x7bz+SdOge1YKc5eMN6pRW7YWBmyq59CqYba1dJ5cUukEjLQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cssnano-preset-default/-/cssnano-preset-default-4.0.8.tgz"
  "version" "4.0.8"
  dependencies:
    "css-declaration-sorter" "^4.0.1"
    "cssnano-util-raw-cache" "^4.0.1"
    "postcss" "^7.0.0"
    "postcss-calc" "^7.0.1"
    "postcss-colormin" "^4.0.3"
    "postcss-convert-values" "^4.0.1"
    "postcss-discard-comments" "^4.0.2"
    "postcss-discard-duplicates" "^4.0.2"
    "postcss-discard-empty" "^4.0.1"
    "postcss-discard-overridden" "^4.0.1"
    "postcss-merge-longhand" "^4.0.11"
    "postcss-merge-rules" "^4.0.3"
    "postcss-minify-font-values" "^4.0.2"
    "postcss-minify-gradients" "^4.0.2"
    "postcss-minify-params" "^4.0.2"
    "postcss-minify-selectors" "^4.0.2"
    "postcss-normalize-charset" "^4.0.1"
    "postcss-normalize-display-values" "^4.0.2"
    "postcss-normalize-positions" "^4.0.2"
    "postcss-normalize-repeat-style" "^4.0.2"
    "postcss-normalize-string" "^4.0.2"
    "postcss-normalize-timing-functions" "^4.0.2"
    "postcss-normalize-unicode" "^4.0.1"
    "postcss-normalize-url" "^4.0.1"
    "postcss-normalize-whitespace" "^4.0.2"
    "postcss-ordered-values" "^4.1.2"
    "postcss-reduce-initial" "^4.0.3"
    "postcss-reduce-transforms" "^4.0.2"
    "postcss-svgo" "^4.0.3"
    "postcss-unique-selectors" "^4.0.1"

"cssnano-util-get-arguments@^4.0.0":
  "integrity" "sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cssnano-util-get-arguments/-/cssnano-util-get-arguments-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-get-match@^4.0.0":
  "integrity" "sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cssnano-util-get-match/-/cssnano-util-get-match-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-raw-cache@^4.0.1":
  "integrity" "sha512-qLuYtWK2b2Dy55I8ZX3ky1Z16WYsx544Q0UWViebptpwn/xDBmog2TLg4f+DBMg1rJ6JDWtn96WHbOKDWt1WQA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cssnano-util-raw-cache/-/cssnano-util-raw-cache-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"cssnano-util-same-parent@^4.0.0":
  "integrity" "sha512-WcKx5OY+KoSIAxBW6UBBRay1U6vkYheCdjyVNDm85zt5K9mHoGOfsOsqIszfAqrQQFIIKgjh2+FDgIj/zsl21Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cssnano-util-same-parent/-/cssnano-util-same-parent-4.0.1.tgz"
  "version" "4.0.1"

"cssnano@^3.10.0":
  "integrity" "sha1-Tzj2zqK5sX+gFJDyPx3GjqZcHDg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cssnano/-/cssnano-3.10.0.tgz"
  "version" "3.10.0"
  dependencies:
    "autoprefixer" "^6.3.1"
    "decamelize" "^1.1.2"
    "defined" "^1.0.0"
    "has" "^1.0.1"
    "object-assign" "^4.0.1"
    "postcss" "^5.0.14"
    "postcss-calc" "^5.2.0"
    "postcss-colormin" "^2.1.8"
    "postcss-convert-values" "^2.3.4"
    "postcss-discard-comments" "^2.0.4"
    "postcss-discard-duplicates" "^2.0.1"
    "postcss-discard-empty" "^2.0.1"
    "postcss-discard-overridden" "^0.1.1"
    "postcss-discard-unused" "^2.2.1"
    "postcss-filter-plugins" "^2.0.0"
    "postcss-merge-idents" "^2.1.5"
    "postcss-merge-longhand" "^2.0.1"
    "postcss-merge-rules" "^2.0.3"
    "postcss-minify-font-values" "^1.0.2"
    "postcss-minify-gradients" "^1.0.1"
    "postcss-minify-params" "^1.0.4"
    "postcss-minify-selectors" "^2.0.4"
    "postcss-normalize-charset" "^1.1.0"
    "postcss-normalize-url" "^3.0.7"
    "postcss-ordered-values" "^2.1.0"
    "postcss-reduce-idents" "^2.2.2"
    "postcss-reduce-initial" "^1.0.0"
    "postcss-reduce-transforms" "^1.0.3"
    "postcss-svgo" "^2.1.1"
    "postcss-unique-selectors" "^2.0.2"
    "postcss-value-parser" "^3.2.3"
    "postcss-zindex" "^2.0.1"

"cssnano@^4.1.10":
  "integrity" "sha512-6gZm2htn7xIPJOHY824ERgj8cNPgPxyCSnkXc4v7YvNW+TdVfzgngHcEhy/8D11kUWRUMbke+tC+AUcUsnMz2g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cssnano/-/cssnano-4.1.11.tgz"
  "version" "4.1.11"
  dependencies:
    "cosmiconfig" "^5.0.0"
    "cssnano-preset-default" "^4.0.8"
    "is-resolvable" "^1.0.0"
    "postcss" "^7.0.0"

"csso@^4.0.2":
  "integrity" "sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/csso/-/csso-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "css-tree" "^1.1.2"

"csso@~2.3.1":
  "integrity" "sha1-3dUsWHAz9J6Utx/FVWnyUuj/X4U="
  "resolved" "https://repo.huaweicloud.com/repository/npm/csso/-/csso-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "clap" "^1.0.9"
    "source-map" "^0.5.3"

"cuint@^0.2.2":
  "integrity" "sha1-QICG1AlVDCYxFVYZ6fp7ytw7mRs="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cuint/-/cuint-0.2.2.tgz"
  "version" "0.2.2"

"currently-unhandled@^0.4.1":
  "integrity" "sha1-mI3zP+qxke95mmE2nddsF635V+o="
  "resolved" "https://repo.huaweicloud.com/repository/npm/currently-unhandled/-/currently-unhandled-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "array-find-index" "^1.0.1"

"cyclist@^1.0.1":
  "integrity" "sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/cyclist/-/cyclist-1.0.1.tgz"
  "version" "1.0.1"

"d@^1.0.1", "d@1":
  "integrity" "sha512-m62ShEObQ39CfralilEQRjH6oAMtNCV1xJyEx5LpRYUVN+EviphDgUc/F3hnYbADmkiNs67Y+3ylmlG7Lnu+FA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/d/-/d-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "es5-ext" "^0.10.50"
    "type" "^1.0.1"

"de-indent@^1.0.2":
  "integrity" "sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/de-indent/-/de-indent-1.0.2.tgz"
  "version" "1.0.2"

"debug@^2.2.0", "debug@^2.3.3", "debug@^2.6.6", "debug@^2.6.8", "debug@^2.6.9", "debug@2.6.9":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^3.1.0":
  "integrity" "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/debug/-/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^3.1.1":
  "integrity" "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/debug/-/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^3.2.6":
  "integrity" "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/debug/-/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^3.2.7":
  "integrity" "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/debug/-/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.1.0":
  "integrity" "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/debug/-/debug-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "ms" "2.1.2"

"debug@~3.1.0":
  "integrity" "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/debug/-/debug-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "ms" "2.0.0"

"decamelize@^1.0.0", "decamelize@^1.1.1", "decamelize@^1.1.2":
  "integrity" "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/decamelize/-/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decode-uri-component@^0.2.0":
  "integrity" "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/decode-uri-component/-/decode-uri-component-0.2.0.tgz"
  "version" "0.2.0"

"deep-equal@^1.0.1":
  "integrity" "sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/deep-equal/-/deep-equal-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "is-arguments" "^1.0.4"
    "is-date-object" "^1.0.1"
    "is-regex" "^1.0.4"
    "object-is" "^1.0.1"
    "object-keys" "^1.1.1"
    "regexp.prototype.flags" "^1.2.0"

"deep-is@~0.1.3":
  "integrity" "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/deep-is/-/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"define-properties@^1.1.3":
  "integrity" "sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/define-properties/-/define-properties-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "object-keys" "^1.0.12"

"define-property@^0.2.5":
  "integrity" "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/define-property/-/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "integrity" "sha1-dp66rz9KY6rTr56NMEybvnm/sOY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/define-property/-/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "integrity" "sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/define-property/-/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"defined@^1.0.0":
  "integrity" "sha1-yY2bzvdWdBiOEQlpFRGZ45sfppM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/defined/-/defined-1.0.0.tgz"
  "version" "1.0.0"

"del@^3.0.0":
  "integrity" "sha1-U+z2mf/LyzljdpGrE7rxYIGXZuU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/del/-/del-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "globby" "^6.1.0"
    "is-path-cwd" "^1.0.0"
    "is-path-in-cwd" "^1.0.0"
    "p-map" "^1.1.1"
    "pify" "^3.0.0"
    "rimraf" "^2.2.8"

"depd@~1.1.2":
  "integrity" "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="
  "resolved" "https://repo.huaweicloud.com/repository/npm/depd/-/depd-1.1.2.tgz"
  "version" "1.1.2"

"des.js@^1.0.0":
  "integrity" "sha512-Q0I4pfFrv2VPd34/vfLrFOoRmlYj3OV50i7fskps1jZWK1kApMWWT9G6RRUeYedLcBDIhnSDaUvJMb3AhUlaEA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/des.js/-/des.js-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"

"destroy@~1.0.4":
  "integrity" "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/destroy/-/destroy-1.0.4.tgz"
  "version" "1.0.4"

"detect-indent@^4.0.0":
  "integrity" "sha1-920GQ1LN9Docts5hnE7jqUdd4gg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/detect-indent/-/detect-indent-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "repeating" "^2.0.0"

"detect-node@^2.0.4":
  "integrity" "sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/detect-node/-/detect-node-2.1.0.tgz"
  "version" "2.1.0"

"diffie-hellman@^5.0.0":
  "integrity" "sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/diffie-hellman/-/diffie-hellman-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "miller-rabin" "^4.0.0"
    "randombytes" "^2.0.0"

"dir-glob@^2.0.0":
  "integrity" "sha512-f9LBi5QWzIW3I6e//uxZoLBlUt9kcp66qo0sSCxL6YZKc75R1c4MFCoe/LaZiBGmgujvQdxc5Bn3QhfyvK5Hsw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/dir-glob/-/dir-glob-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "path-type" "^3.0.0"

"dns-equal@^1.0.0":
  "integrity" "sha1-s55/HabrCnW6nBcySzR1PEfgZU0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/dns-equal/-/dns-equal-1.0.0.tgz"
  "version" "1.0.0"

"dns-packet@^1.3.1":
  "integrity" "sha512-BQ6F4vycLXBvdrJZ6S3gZewt6rcrks9KBgM9vrhW+knGRqc8uEdT7fuCwloc7nny5xNoMJ17HGH0R/6fpo8ECA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/dns-packet/-/dns-packet-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "ip" "^1.1.0"
    "safe-buffer" "^5.0.1"

"dns-txt@^2.0.2":
  "integrity" "sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/dns-txt/-/dns-txt-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "buffer-indexof" "^1.0.0"

"doctrine@^2.1.0":
  "integrity" "sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/doctrine/-/doctrine-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esutils" "^2.0.2"

"dom-converter@^0.2.0":
  "integrity" "sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/dom-converter/-/dom-converter-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "utila" "~0.4"

"dom-serializer@^1.0.1":
  "integrity" "sha512-5c54Bk5Dw4qAxNOI1pFEizPSjVsx5+bpJKmL2kPn8JhBUq2q09tTCa3mjijun2NfK78NMouDYNMBkOrPZiS+ig=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/dom-serializer/-/dom-serializer-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.2.0"
    "entities" "^2.0.0"

"dom-serializer@0":
  "integrity" "sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/dom-serializer/-/dom-serializer-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "entities" "^2.0.0"

"domain-browser@^1.1.1":
  "integrity" "sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/domain-browser/-/domain-browser-1.2.0.tgz"
  "version" "1.2.0"

"domelementtype@^1.3.1", "domelementtype@1":
  "integrity" "sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/domelementtype/-/domelementtype-1.3.1.tgz"
  "version" "1.3.1"

"domelementtype@^2.0.1", "domelementtype@^2.2.0":
  "integrity" "sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/domelementtype/-/domelementtype-2.3.0.tgz"
  "version" "2.3.0"

"domhandler@^2.3.0":
  "integrity" "sha512-JiK04h0Ht5u/80fdLMCEmV4zkNh2BcoMFBmZ/91WtYZ8qVXSKjiw7fXMgFPnHcSZgOo3XdinHvmnDUeMf5R4wA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/domhandler/-/domhandler-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "domelementtype" "1"

"domhandler@^4.0.0", "domhandler@^4.2.0", "domhandler@^4.3.1":
  "integrity" "sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/domhandler/-/domhandler-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "domelementtype" "^2.2.0"

"domutils@^1.5.1", "domutils@^1.7.0":
  "integrity" "sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/domutils/-/domutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"domutils@^2.5.2":
  "integrity" "sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/domutils/-/domutils-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "dom-serializer" "^1.0.1"
    "domelementtype" "^2.2.0"
    "domhandler" "^4.2.0"

"domutils@^2.8.0":
  "integrity" "sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/domutils/-/domutils-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "dom-serializer" "^1.0.1"
    "domelementtype" "^2.2.0"
    "domhandler" "^4.2.0"

"dot-prop@^5.2.0":
  "integrity" "sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/dot-prop/-/dot-prop-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "is-obj" "^2.0.0"

"duplexer@^0.1.1":
  "integrity" "sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/duplexer/-/duplexer-0.1.2.tgz"
  "version" "0.1.2"

"duplexify@^3.4.2", "duplexify@^3.6.0":
  "integrity" "sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/duplexify/-/duplexify-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "end-of-stream" "^1.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"
    "stream-shift" "^1.0.0"

"echarts@4.7.0":
  "integrity" "sha512-NlOTdUcAsIyCCG+N4uh0ZEvXtrPW2jvcuqf03RyqYeCKzyPbiOQ4I3MdKXMhxG3lBdqQNdNXVT71SB4KTQjN0A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/echarts/-/echarts-4.7.0.tgz"
  "version" "4.7.0"
  dependencies:
    "zrender" "4.3.0"

"ee-first@1.1.1":
  "integrity" "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ee-first/-/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"ejs@^2.5.7":
  "integrity" "sha512-7vmuyh5+kuUyJKePhQfRQBhXV5Ce+RnaeeQArKu1EAMpL3WbgMt5WG6uQZpEVvYSSsxMXRKOewtDk9RaTKXRlA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ejs/-/ejs-2.7.4.tgz"
  "version" "2.7.4"

"electron-to-chromium@^1.2.7", "electron-to-chromium@^1.3.30", "electron-to-chromium@^1.3.47", "electron-to-chromium@^1.4.84":
  "integrity" "sha512-ZYfpVLULm67K7CaaGP7DmjyeMY4naxsbTy+syVVxT6QHI1Ww8XbJjmr9fDckrhq44WzCrcC5kH3zGpdusxwwqg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/electron-to-chromium/-/electron-to-chromium-1.4.106.tgz"
  "version" "1.4.106"

"elliptic@^6.5.3":
  "integrity" "sha512-iLhC6ULemrljPZb+QutR5TQGB+pdW6KGD5RSegS+8sorOZT+rdQFbsQFJgvN3eRqNALqJer4oQ16YvJHlU8hzQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/elliptic/-/elliptic-6.5.4.tgz"
  "version" "6.5.4"
  dependencies:
    "bn.js" "^4.11.9"
    "brorand" "^1.1.0"
    "hash.js" "^1.0.0"
    "hmac-drbg" "^1.0.1"
    "inherits" "^2.0.4"
    "minimalistic-assert" "^1.0.1"
    "minimalistic-crypto-utils" "^1.0.1"

"emojis-list@^2.0.0":
  "integrity" "sha1-TapNnbAPmBmIDHn6RXrlsJof04k="
  "resolved" "https://repo.huaweicloud.com/repository/npm/emojis-list/-/emojis-list-2.1.0.tgz"
  "version" "2.1.0"

"emojis-list@^3.0.0":
  "integrity" "sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/emojis-list/-/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"encodeurl@~1.0.2":
  "integrity" "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="
  "resolved" "https://repo.huaweicloud.com/repository/npm/encodeurl/-/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"end-of-stream@^1.0.0", "end-of-stream@^1.1.0":
  "integrity" "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/end-of-stream/-/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"enhanced-resolve@^3.4.0":
  "integrity" "sha1-BCHjOf1xQZs9oT0Smzl5BAIwR24="
  "resolved" "https://repo.huaweicloud.com/repository/npm/enhanced-resolve/-/enhanced-resolve-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "graceful-fs" "^4.1.2"
    "memory-fs" "^0.4.0"
    "object-assign" "^4.0.1"
    "tapable" "^0.2.7"

"entities@^1.1.1":
  "integrity" "sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/entities/-/entities-1.1.2.tgz"
  "version" "1.1.2"

"entities@^2.0.0":
  "integrity" "sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/entities/-/entities-2.2.0.tgz"
  "version" "2.2.0"

"errno@^0.1.1", "errno@^0.1.3", "errno@~0.1.7":
  "integrity" "sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/errno/-/errno-0.1.8.tgz"
  "version" "0.1.8"
  dependencies:
    "prr" "~1.0.1"

"error-ex@^1.2.0", "error-ex@^1.3.1":
  "integrity" "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/error-ex/-/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^2.0.0":
  "integrity" "sha512-chLOW0ZGRf4s8raLrDxa5sdkvPec5YdvwbFnqJme4rk0rFajP8mPtrDL1+I+CwrQDCjswDA5sREX7jYQDQs9vA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/error-stack-parser/-/error-stack-parser-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "stackframe" "^1.1.1"

"es-abstract@^1.17.2", "es-abstract@^1.19.0", "es-abstract@^1.19.1":
  "integrity" "sha512-gfSBJoZdlL2xRiOCy0g8gLMryhoe1TlimjzU99L/31Z8QEGIhVQI+EWwt5lT+AuU9SnorVupXFqqOGqGfsyO6w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/es-abstract/-/es-abstract-1.19.2.tgz"
  "version" "1.19.2"
  dependencies:
    "call-bind" "^1.0.2"
    "es-to-primitive" "^1.2.1"
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.1.1"
    "get-symbol-description" "^1.0.0"
    "has" "^1.0.3"
    "has-symbols" "^1.0.3"
    "internal-slot" "^1.0.3"
    "is-callable" "^1.2.4"
    "is-negative-zero" "^2.0.2"
    "is-regex" "^1.1.4"
    "is-shared-array-buffer" "^1.0.1"
    "is-string" "^1.0.7"
    "is-weakref" "^1.0.2"
    "object-inspect" "^1.12.0"
    "object-keys" "^1.1.1"
    "object.assign" "^4.1.2"
    "string.prototype.trimend" "^1.0.4"
    "string.prototype.trimstart" "^1.0.4"
    "unbox-primitive" "^1.0.1"

"es-to-primitive@^1.2.1":
  "integrity" "sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "is-callable" "^1.1.4"
    "is-date-object" "^1.0.1"
    "is-symbol" "^1.0.2"

"es5-ext@^0.10.35", "es5-ext@^0.10.46", "es5-ext@^0.10.50", "es5-ext@~0.10.14":
  "integrity" "sha512-jpKNXIt60htYG59/9FGf2PYT3pwMpnEbNKysU+k/4FGwyGtMotOvcZOuW+EmXXYASRqYSXQfGL5cVIthOTgbkg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/es5-ext/-/es5-ext-0.10.60.tgz"
  "version" "0.10.60"
  dependencies:
    "es6-iterator" "^2.0.3"
    "es6-symbol" "^3.1.3"
    "next-tick" "^1.1.0"

"es6-iterator@^2.0.3", "es6-iterator@~2.0.1":
  "integrity" "sha1-p96IkUGgWpSwhUQDstCg+/qY87c="
  "resolved" "https://repo.huaweicloud.com/repository/npm/es6-iterator/-/es6-iterator-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "d" "1"
    "es5-ext" "^0.10.35"
    "es6-symbol" "^3.1.1"

"es6-map@^0.1.3":
  "integrity" "sha1-kTbgUD3MBqMBaQ8LsU/042TpSfA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/es6-map/-/es6-map-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "d" "1"
    "es5-ext" "~0.10.14"
    "es6-iterator" "~2.0.1"
    "es6-set" "~0.1.5"
    "es6-symbol" "~3.1.1"
    "event-emitter" "~0.3.5"

"es6-set@~0.1.5":
  "integrity" "sha1-0rPsXU2ADO2BjbU40ol02wpzzLE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/es6-set/-/es6-set-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "d" "1"
    "es5-ext" "~0.10.14"
    "es6-iterator" "~2.0.1"
    "es6-symbol" "3.1.1"
    "event-emitter" "~0.3.5"

"es6-symbol@^3.1.1", "es6-symbol@^3.1.3", "es6-symbol@~3.1.1":
  "integrity" "sha512-NJ6Yn3FuDinBaBRWl/q5X/s4koRHBrgKAu+yGI6JCBeiu3qrcbJhwT2GeR/EXVfylRk8dpQVJoLEFhK+Mu31NA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/es6-symbol/-/es6-symbol-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "d" "^1.0.1"
    "ext" "^1.1.2"

"es6-symbol@3.1.1":
  "integrity" "sha1-vwDvT9q2uhtG7Le2KbTH7VcVzHc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/es6-symbol/-/es6-symbol-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "d" "1"
    "es5-ext" "~0.10.14"

"es6-weak-map@^2.0.1":
  "integrity" "sha512-p5um32HOTO1kP+w7PRnB+5lQ43Z6muuMuIMffvDN8ZB4GcnjLBV6zGStpbASIMk4DCAvEaamhe2zhyCb/QXXsA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/es6-weak-map/-/es6-weak-map-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "d" "1"
    "es5-ext" "^0.10.46"
    "es6-iterator" "^2.0.3"
    "es6-symbol" "^3.1.1"

"escalade@^3.1.1":
  "integrity" "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/escalade/-/escalade-3.1.1.tgz"
  "version" "3.1.1"

"escape-html@~1.0.3":
  "integrity" "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/escape-html/-/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.2", "escape-string-regexp@^1.0.5":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escope@^3.6.0":
  "integrity" "sha1-4Bl16BJ4GhY6ba392AOY3GTIicM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/escope/-/escope-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "es6-map" "^0.1.3"
    "es6-weak-map" "^2.0.1"
    "esrecurse" "^4.1.0"
    "estraverse" "^4.1.1"

"eslint-config-standard@^10.2.1":
  "integrity" "sha1-wGHk0GbzedwXzVYsZOgZtN1FRZE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eslint-config-standard/-/eslint-config-standard-10.2.1.tgz"
  "version" "10.2.1"

"eslint-friendly-formatter@^3.0.0":
  "integrity" "sha1-J4h0Q1psRuwdlPoLH/SU4w7wQpA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eslint-friendly-formatter/-/eslint-friendly-formatter-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "chalk" "^1.0.0"
    "coalescy" "1.0.0"
    "extend" "^3.0.0"
    "minimist" "^1.2.0"
    "text-table" "^0.2.0"

"eslint-import-resolver-node@^0.3.6":
  "integrity" "sha512-0En0w03NRVMn9Uiyn8YRPDKvWjxCWkslUEhGNTdGx15RvPJYQ+lbOlqrlNI2vEAs4pDYK4f/HN2TbDmk5TP0iw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "debug" "^3.2.7"
    "resolve" "^1.20.0"

"eslint-loader@^1.7.1":
  "integrity" "sha512-40aN976qSNPyb9ejTqjEthZITpls1SVKtwguahmH1dzGCwQU/vySE+xX33VZmD8csU0ahVNCtFlsPgKqRBiqgg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eslint-loader/-/eslint-loader-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "loader-fs-cache" "^1.0.0"
    "loader-utils" "^1.0.2"
    "object-assign" "^4.0.1"
    "object-hash" "^1.1.4"
    "rimraf" "^2.6.1"

"eslint-module-utils@^2.7.2":
  "integrity" "sha512-088JEC7O3lDZM9xGe0RerkOMd0EjFl+Yvd1jPWIkMT5u3H9+HC34mWWPnqPrN13gieT9pBOO+Qt07Nb/6TresQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eslint-module-utils/-/eslint-module-utils-2.7.3.tgz"
  "version" "2.7.3"
  dependencies:
    "debug" "^3.2.7"
    "find-up" "^2.1.0"

"eslint-plugin-html@5.0.3":
  "integrity" "sha512-46ruAnp3jVQP/5Bi5eEIOooscjUTPFU3vxCxHe/OG6ORdM7Xv5c25/Nz9fAbHklzCpiXuIiH4/mV/XBkm7MINw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eslint-plugin-html/-/eslint-plugin-html-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "htmlparser2" "^3.10.0"

"eslint-plugin-import@^2.7.0":
  "integrity" "sha512-/KJBASVFxpu0xg1kIBn9AUa8hQVnszpwgE7Ld0lKAlx7Ie87yzEzCgSkekt+le/YVhiaosO4Y14GDAOc41nfxA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eslint-plugin-import/-/eslint-plugin-import-2.25.4.tgz"
  "version" "2.25.4"
  dependencies:
    "array-includes" "^3.1.4"
    "array.prototype.flat" "^1.2.5"
    "debug" "^2.6.9"
    "doctrine" "^2.1.0"
    "eslint-import-resolver-node" "^0.3.6"
    "eslint-module-utils" "^2.7.2"
    "has" "^1.0.3"
    "is-core-module" "^2.8.0"
    "is-glob" "^4.0.3"
    "minimatch" "^3.0.4"
    "object.values" "^1.1.5"
    "resolve" "^1.20.0"
    "tsconfig-paths" "^3.12.0"

"eslint-plugin-node@^5.2.0":
  "integrity" "sha512-xhPXrh0Vl/b7870uEbaumb2Q+LxaEcOQ3kS1jtIXanBAwpMre1l5q/l2l/hESYJGEFKuI78bp6Uw50hlpr7B+g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eslint-plugin-node/-/eslint-plugin-node-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "ignore" "^3.3.6"
    "minimatch" "^3.0.4"
    "resolve" "^1.3.3"
    "semver" "5.3.0"

"eslint-plugin-promise@^3.4.0":
  "integrity" "sha512-JiFL9UFR15NKpHyGii1ZcvmtIqa3UTwiDAGb8atSffe43qJ3+1czVGN6UtkklpcJ2DVnqvTMzEKRaJdBkAL2aQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eslint-plugin-promise/-/eslint-plugin-promise-3.8.0.tgz"
  "version" "3.8.0"

"eslint-plugin-standard@^3.0.1":
  "integrity" "sha512-fVcdyuKRr0EZ4fjWl3c+gp1BANFJD1+RaWa2UPYfMZ6jCtp5RG00kSaXnK/dE5sYzt4kaWJ9qdxqUfc0d9kX0w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eslint-plugin-standard/-/eslint-plugin-standard-3.1.0.tgz"
  "version" "3.1.0"

"eslint-plugin-vue@^4.0.0":
  "integrity" "sha512-esETKhVMI7Vdli70Wt4bvAwnZBJeM0pxVX9Yb0wWKxdCJc2EADalVYK/q2FzMw8oKN0wPMdqVCKS8kmR89recA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eslint-plugin-vue/-/eslint-plugin-vue-4.7.1.tgz"
  "version" "4.7.1"
  dependencies:
    "vue-eslint-parser" "^2.0.3"

"eslint-scope@^3.7.1", "eslint-scope@3.7.1":
  "integrity" "sha1-PWPD7f2gLgbgGkUq2IyqzHzctug="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eslint-scope/-/eslint-scope-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "esrecurse" "^4.1.0"
    "estraverse" "^4.1.1"

"eslint-visitor-keys@^1.0.0":
  "integrity" "sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz"
  "version" "1.3.0"

"eslint@^4.15.0":
  "integrity" "sha512-bT3/1x1EbZB7phzYu7vCr1v3ONuzDtX8WjuM9c0iYxe+cq+pwcKEoQjl7zd3RpC6YOLgnSy3cTN58M2jcoPDIQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eslint/-/eslint-4.19.1.tgz"
  "version" "4.19.1"
  dependencies:
    "ajv" "^5.3.0"
    "babel-code-frame" "^6.22.0"
    "chalk" "^2.1.0"
    "concat-stream" "^1.6.0"
    "cross-spawn" "^5.1.0"
    "debug" "^3.1.0"
    "doctrine" "^2.1.0"
    "eslint-scope" "^3.7.1"
    "eslint-visitor-keys" "^1.0.0"
    "espree" "^3.5.4"
    "esquery" "^1.0.0"
    "esutils" "^2.0.2"
    "file-entry-cache" "^2.0.0"
    "functional-red-black-tree" "^1.0.1"
    "glob" "^7.1.2"
    "globals" "^11.0.1"
    "ignore" "^3.3.3"
    "imurmurhash" "^0.1.4"
    "inquirer" "^3.0.6"
    "is-resolvable" "^1.0.0"
    "js-yaml" "^3.9.1"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.3.0"
    "lodash" "^4.17.4"
    "minimatch" "^3.0.2"
    "mkdirp" "^0.5.1"
    "natural-compare" "^1.4.0"
    "optionator" "^0.8.2"
    "path-is-inside" "^1.0.2"
    "pluralize" "^7.0.0"
    "progress" "^2.0.0"
    "regexpp" "^1.0.1"
    "require-uncached" "^1.0.3"
    "semver" "^5.3.0"
    "strip-ansi" "^4.0.0"
    "strip-json-comments" "~2.0.1"
    "table" "4.0.2"
    "text-table" "~0.2.0"

"espree@^3.5.2", "espree@^3.5.4":
  "integrity" "sha512-yAcIQxtmMiB/jL32dzEp2enBeidsB7xWPLNiw3IIkpVds1P+h7qF9YwJq1yUNzp2OKXgAprs4F61ih66UsoD1A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/espree/-/espree-3.5.4.tgz"
  "version" "3.5.4"
  dependencies:
    "acorn" "^5.5.0"
    "acorn-jsx" "^3.0.0"

"esprima@^2.6.0":
  "integrity" "sha1-luO3DVd59q1JzQMmc9HDEnZ7pYE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/esprima/-/esprima-2.7.3.tgz"
  "version" "2.7.3"

"esprima@^4.0.0":
  "integrity" "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/esprima/-/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.0.0":
  "integrity" "sha512-cCDispWt5vHHtwMY2YrAQ4ibFkAL8RbH5YGBnZBc90MolvvfkkQcJro/aZiAQUlQ3qgrYS6D6v8Gc5G5CQsc9w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/esquery/-/esquery-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.1.0":
  "integrity" "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/esrecurse/-/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/estraverse/-/estraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.1.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"estraverse@^5.2.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"esutils@^2.0.2":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@~1.8.1":
  "integrity" "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/etag/-/etag-1.8.1.tgz"
  "version" "1.8.1"

"event-emitter@~0.3.5":
  "integrity" "sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/event-emitter/-/event-emitter-0.3.5.tgz"
  "version" "0.3.5"
  dependencies:
    "d" "1"
    "es5-ext" "~0.10.14"

"eventemitter3@^4.0.0":
  "integrity" "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eventemitter3/-/eventemitter3-4.0.7.tgz"
  "version" "4.0.7"

"events@^3.0.0":
  "integrity" "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/events/-/events-3.3.0.tgz"
  "version" "3.3.0"

"eventsource@0.1.6":
  "integrity" "sha1-Cs7ehJ7X3RzMMsgRuxG5RNTykjI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/eventsource/-/eventsource-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "original" ">=0.0.5"

"evp_bytestokey@^1.0.0", "evp_bytestokey@^1.0.3":
  "integrity" "sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "md5.js" "^1.3.4"
    "safe-buffer" "^5.1.1"

"execa@^0.7.0":
  "integrity" "sha1-lEvs00zEHuMqY6n68nrVpl/Fl3c="
  "resolved" "https://repo.huaweicloud.com/repository/npm/execa/-/execa-0.7.0.tgz"
  "version" "0.7.0"
  dependencies:
    "cross-spawn" "^5.0.1"
    "get-stream" "^3.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"expand-brackets@^2.1.4":
  "integrity" "sha1-t3c14xXOMPa27/D4OwQVGiJEliI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/expand-brackets/-/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"express@^4.16.2":
  "integrity" "sha512-yuSQpz5I+Ch7gFrPCk4/c+dIBKlQUxtgwqzph132bsT6qhuzss6I8cLJQz7B3rFblzd6wtcI0ZbGltH/C4LjUg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/express/-/express-4.17.3.tgz"
  "version" "4.17.3"
  dependencies:
    "accepts" "~1.3.8"
    "array-flatten" "1.1.1"
    "body-parser" "1.19.2"
    "content-disposition" "0.5.4"
    "content-type" "~1.0.4"
    "cookie" "0.4.2"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "~1.1.2"
    "fresh" "0.5.2"
    "merge-descriptors" "1.0.1"
    "methods" "~1.1.2"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "path-to-regexp" "0.1.7"
    "proxy-addr" "~2.0.7"
    "qs" "6.9.7"
    "range-parser" "~1.2.1"
    "safe-buffer" "5.2.1"
    "send" "0.17.2"
    "serve-static" "1.14.2"
    "setprototypeof" "1.2.0"
    "statuses" "~1.5.0"
    "type-is" "~1.6.18"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"ext@^1.1.2":
  "integrity" "sha512-sdBImtzkq2HpkdRLtlLWDa6w4DX22ijZLKx8BMPUuKe1c5lbN6xwQDQCxSfxBQnHZ13ls/FH0MQZx/q/gr6FQg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ext/-/ext-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "type" "^2.5.0"

"extend-shallow@^2.0.1":
  "integrity" "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/extend-shallow/-/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0", "extend-shallow@^3.0.2":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/extend-shallow/-/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend@^3.0.0":
  "integrity" "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/extend/-/extend-3.0.2.tgz"
  "version" "3.0.2"

"external-editor@^2.0.4":
  "integrity" "sha512-bSn6gvGxKt+b7+6TKEv1ZycHleA7aHhRHyAqJyp5pbUFuYYNIzpZnQDk7AsYckyWdEnTeAnay0aCy2aV6iTk9A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/external-editor/-/external-editor-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "chardet" "^0.4.0"
    "iconv-lite" "^0.4.17"
    "tmp" "^0.0.33"

"extglob@^2.0.4":
  "integrity" "sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/extglob/-/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"extract-text-webpack-plugin@^3.0.0":
  "integrity" "sha512-bt/LZ4m5Rqt/Crl2HiKuAl/oqg0psx1tsTLkvWbJen1CtD+fftkZhMaQ9HOtY2gWsl2Wq+sABmMVi9z3DhKWQQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/extract-text-webpack-plugin/-/extract-text-webpack-plugin-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "async" "^2.4.1"
    "loader-utils" "^1.1.0"
    "schema-utils" "^0.3.0"
    "webpack-sources" "^1.0.1"

"fast-deep-equal@^1.0.0":
  "integrity" "sha1-wFNHeBfIa1HaqFPIHgWbcz0CNhQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/fast-deep-equal/-/fast-deep-equal-1.1.0.tgz"
  "version" "1.1.0"

"fast-deep-equal@^3.1.1":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@~2.0.6":
  "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastparse@^1.1.2":
  "integrity" "sha512-483XLLxTVIwWK3QTrMGRqUfUpoOs/0hbQrl2oz4J0pAcm3A3bu84wxTFqGqkJzewCLdME38xJLJAxBABfQT8sQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/fastparse/-/fastparse-1.1.2.tgz"
  "version" "1.1.2"

"faye-websocket@^0.10.0":
  "integrity" "sha1-TkkvjQTftviQA1B/btvy1QHnxvQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/faye-websocket/-/faye-websocket-0.10.0.tgz"
  "version" "0.10.0"
  dependencies:
    "websocket-driver" ">=0.5.1"

"faye-websocket@~0.11.0":
  "integrity" "sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/faye-websocket/-/faye-websocket-0.11.4.tgz"
  "version" "0.11.4"
  dependencies:
    "websocket-driver" ">=0.5.1"

"figures@^2.0.0":
  "integrity" "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/figures/-/figures-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"file-entry-cache@^2.0.0":
  "integrity" "sha1-w5KZDD5oR4PYOLjISkXYoEhFg2E="
  "resolved" "https://repo.huaweicloud.com/repository/npm/file-entry-cache/-/file-entry-cache-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "flat-cache" "^1.2.1"
    "object-assign" "^4.0.1"

"file-loader@^1.1.4":
  "integrity" "sha512-TGR4HU7HUsGg6GCOPJnFk06RhWgEWFLAGWiT6rcD+GRC2keU3s9RGJ+b3Z6/U73jwwNb2gKLJ7YCrp+jvU4ALg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/file-loader/-/file-loader-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "loader-utils" "^1.0.2"
    "schema-utils" "^0.4.5"

"file-uri-to-path@1.0.0":
  "integrity" "sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw=="
  "resolved" "https://registry.npmjs.org/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz"
  "version" "1.0.0"

"filesize@^3.5.11":
  "integrity" "sha512-7KjR1vv6qnicaPMi1iiTcI85CyYwRO/PSFCu6SvqL8jN2Wjt/NIYQTFtFs7fSDCYOstUkEWIQGFUg5YZQfjlcg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/filesize/-/filesize-3.6.1.tgz"
  "version" "3.6.1"

"fill-range@^4.0.0":
  "integrity" "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/fill-range/-/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"
    "to-regex-range" "^2.1.0"

"fill-range@^7.0.1":
  "integrity" "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/fill-range/-/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"finalhandler@~1.1.2":
  "integrity" "sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/finalhandler/-/finalhandler-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "statuses" "~1.5.0"
    "unpipe" "~1.0.0"

"find-cache-dir@^0.1.1":
  "integrity" "sha1-yN765XyKUqinhPnjHFfHQumToLk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/find-cache-dir/-/find-cache-dir-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "commondir" "^1.0.1"
    "mkdirp" "^0.5.1"
    "pkg-dir" "^1.0.0"

"find-cache-dir@^1.0.0":
  "integrity" "sha1-kojj6ePMN0hxfTnq3hfPcfww7m8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/find-cache-dir/-/find-cache-dir-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^1.0.0"
    "pkg-dir" "^2.0.0"

"find-up@^1.0.0":
  "integrity" "sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/find-up/-/find-up-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "path-exists" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"find-up@^2.0.0", "find-up@^2.1.0":
  "integrity" "sha1-RdG35QbHF93UgndaK3eSCjwMV6c="
  "resolved" "https://repo.huaweicloud.com/repository/npm/find-up/-/find-up-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "locate-path" "^2.0.0"

"flat-cache@^1.2.1":
  "integrity" "sha512-VwyB3Lkgacfik2vhqR4uv2rvebqmDvFu4jlN/C1RzWoJEo8I7z4Q404oiqYCkq41mni8EzQnm95emU9seckwtg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/flat-cache/-/flat-cache-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "circular-json" "^0.3.1"
    "graceful-fs" "^4.1.2"
    "rimraf" "~2.6.2"
    "write" "^0.2.1"

"flatten@^1.0.2":
  "integrity" "sha512-dVsPA/UwQ8+2uoFe5GHtiBMu48dWLTdsuEd7CKGlZlD78r1TTWBvDuFaFGKCo/ZfEr95Uk56vZoX86OsHkUeIg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/flatten/-/flatten-1.0.3.tgz"
  "version" "1.0.3"

"flush-write-stream@^1.0.0":
  "integrity" "sha512-3Z4XhFZ3992uIq0XOqb9AreonueSYphE6oYbpt5+3u06JWklbsPkNv3ZKkP9Bz/r+1MWCaMoSQ28P85+1Yc77w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/flush-write-stream/-/flush-write-stream-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "inherits" "^2.0.3"
    "readable-stream" "^2.3.6"

"follow-redirects@^1.0.0":
  "integrity" "sha512-MQDfihBQYMcyy5dhRDJUHcw7lb2Pv/TuE6xP1vyraLukNDHKbDxDNaOE3NbCAdKQApno+GPRyo1YAp89yCjK4w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/follow-redirects/-/follow-redirects-1.14.9.tgz"
  "version" "1.14.9"

"for-in@^1.0.2":
  "integrity" "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/for-in/-/for-in-1.0.2.tgz"
  "version" "1.0.2"

"forwarded@0.2.0":
  "integrity" "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/forwarded/-/forwarded-0.2.0.tgz"
  "version" "0.2.0"

"fragment-cache@^0.2.1":
  "integrity" "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/fragment-cache/-/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fresh@0.5.2":
  "integrity" "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="
  "resolved" "https://repo.huaweicloud.com/repository/npm/fresh/-/fresh-0.5.2.tgz"
  "version" "0.5.2"

"friendly-errors-webpack-plugin@^1.6.1":
  "integrity" "sha512-K27M3VK30wVoOarP651zDmb93R9zF28usW4ocaK3mfQeIEI5BPht/EzZs5E8QLLwbLRJQMwscAjDxYPb1FuNiw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/friendly-errors-webpack-plugin/-/friendly-errors-webpack-plugin-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "chalk" "^1.1.3"
    "error-stack-parser" "^2.0.0"
    "string-width" "^2.0.0"

"from2@^2.1.0":
  "integrity" "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/from2/-/from2-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"

"fs-write-stream-atomic@^1.0.8":
  "integrity" "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "graceful-fs" "^4.1.2"
    "iferr" "^0.1.5"
    "imurmurhash" "^0.1.4"
    "readable-stream" "1 || 2"

"fs.realpath@^1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"function-bind@^1.1.1":
  "integrity" "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/function-bind/-/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"functional-red-black-tree@^1.0.1":
  "integrity" "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
  "version" "1.0.1"

"get-caller-file@^1.0.1":
  "integrity" "sha512-3t6rVToeoZfYSGd8YoLFR2DJkiQrIiUrGcjvFX2mDw3bn6k2OtwHN0TNCLbBO+w8qTvimhDkv+LSscbJY1vE6w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/get-caller-file/-/get-caller-file-1.0.3.tgz"
  "version" "1.0.3"

"get-intrinsic@^1.0.2", "get-intrinsic@^1.1.0", "get-intrinsic@^1.1.1":
  "integrity" "sha512-kWZrnVM42QCiEA2Ig1bG8zjoIMOgxWwYCEeNdwY6Tv/cOSeGpcoX4pXHfKUxNKVoArnrEr2e9srnAxxGIraS9Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/get-intrinsic/-/get-intrinsic-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "function-bind" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.1"

"get-stdin@^4.0.1":
  "integrity" "sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/get-stdin/-/get-stdin-4.0.1.tgz"
  "version" "4.0.1"

"get-stream@^3.0.0":
  "integrity" "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/get-stream/-/get-stream-3.0.0.tgz"
  "version" "3.0.0"

"get-symbol-description@^1.0.0":
  "integrity" "sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/get-symbol-description/-/get-symbol-description-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "call-bind" "^1.0.2"
    "get-intrinsic" "^1.1.1"

"get-value@^2.0.3", "get-value@^2.0.6":
  "integrity" "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/get-value/-/get-value-2.0.6.tgz"
  "version" "2.0.6"

"glob-parent@^3.1.0":
  "integrity" "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/glob-parent/-/glob-parent-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-glob" "^3.1.0"
    "path-dirname" "^1.0.0"

"glob-parent@~5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob@^7.0.0", "glob@^7.0.3", "glob@^7.1.2", "glob@^7.1.3", "glob@^7.1.6":
  "integrity" "sha512-lmLf6gtyrPq8tTjSmrO94wBeQbFR3HbLHbuyD69wuyQkImp2hWqMGB47OX65FBkPffO641IP9jWa1z4ivqG26Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/glob/-/glob-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"globals@^11.0.1":
  "integrity" "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/globals/-/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^11.1.0":
  "integrity" "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/globals/-/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^9.18.0":
  "integrity" "sha512-S0nG3CLEQiY/ILxqtztTWH/3iRRdyBLw6KMDxnKMchrtbj2OFmehVh0WUCfW3DUrIgx/qFrJPICrq4Z4sTR9UQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/globals/-/globals-9.18.0.tgz"
  "version" "9.18.0"

"globby@^6.1.0":
  "integrity" "sha1-9abXDoOV4hyFj7BInWTfAkJNUGw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/globby/-/globby-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "array-union" "^1.0.1"
    "glob" "^7.0.3"
    "object-assign" "^4.0.1"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"globby@^7.1.1":
  "integrity" "sha1-+yzP+UAfhgCUXfral0QMypcrhoA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/globby/-/globby-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "array-union" "^1.0.1"
    "dir-glob" "^2.0.0"
    "glob" "^7.1.2"
    "ignore" "^3.3.5"
    "pify" "^3.0.0"
    "slash" "^1.0.0"

"graceful-fs@^4.1.11", "graceful-fs@^4.1.2":
  "integrity" "sha512-NtNxqUcXgpW2iMrfqSfR73Glt39K+BLwWsPs94yR63v45T0Wbej7eRmL5cWfwEgqXnmjQp3zaJTshdRW/qC2ZQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/graceful-fs/-/graceful-fs-4.2.9.tgz"
  "version" "4.2.9"

"growly@^1.3.0":
  "integrity" "sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/growly/-/growly-1.3.0.tgz"
  "version" "1.3.0"

"gzip-size@^4.1.0":
  "integrity" "sha1-iuCWJX6r59acRb4rZ8RIEk/7UXw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/gzip-size/-/gzip-size-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "duplexer" "^0.1.1"
    "pify" "^3.0.0"

"handle-thing@^2.0.0":
  "integrity" "sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/handle-thing/-/handle-thing-2.0.1.tgz"
  "version" "2.0.1"

"has-ansi@^2.0.0":
  "integrity" "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/has-ansi/-/has-ansi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-regex" "^2.0.0"

"has-bigints@^1.0.1":
  "integrity" "sha512-LSBS2LjbNBTf6287JEbEzvJgftkF5qFkmCo9hDRpAzKhUOlJ+hx8dd4USs00SgsUNwc4617J9ki5YtEClM2ffA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/has-bigints/-/has-bigints-1.0.1.tgz"
  "version" "1.0.1"

"has-flag@^1.0.0":
  "integrity" "sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/has-flag/-/has-flag-1.0.0.tgz"
  "version" "1.0.0"

"has-flag@^2.0.0":
  "integrity" "sha1-6CB68cx7MNRGzHC3NLXovhj4jVE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/has-flag/-/has-flag-2.0.0.tgz"
  "version" "2.0.0"

"has-flag@^3.0.0":
  "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-symbols@^1.0.1", "has-symbols@^1.0.2", "has-symbols@^1.0.3":
  "integrity" "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/has-symbols/-/has-symbols-1.0.3.tgz"
  "version" "1.0.3"

"has-tostringtag@^1.0.0":
  "integrity" "sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-symbols" "^1.0.2"

"has-value@^0.3.1":
  "integrity" "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/has-value/-/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "integrity" "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/has-value/-/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "integrity" "sha1-bWHeldkd/Km5oCCJrThL/49it3E="
  "resolved" "https://repo.huaweicloud.com/repository/npm/has-values/-/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@^1.0.0":
  "integrity" "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/has-values/-/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"has@^1.0.0", "has@^1.0.1", "has@^1.0.3":
  "integrity" "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/has/-/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hash-base@^3.0.0":
  "integrity" "sha512-1nmYp/rhMDiE7AYkDw+lLwlAzz0AntGIe51F3RfFfEqyQ3feY2eI/NcwC6umIQVOASPMsWJLJScWKSSvzL9IVA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/hash-base/-/hash-base-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "inherits" "^2.0.4"
    "readable-stream" "^3.6.0"
    "safe-buffer" "^5.2.0"

"hash-sum@^1.0.2":
  "integrity" "sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/hash-sum/-/hash-sum-1.0.2.tgz"
  "version" "1.0.2"

"hash.js@^1.0.0", "hash.js@^1.0.3":
  "integrity" "sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/hash.js/-/hash.js-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "inherits" "^2.0.3"
    "minimalistic-assert" "^1.0.1"

"he@^1.1.0", "he@1.2.x":
  "integrity" "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/he/-/he-1.2.0.tgz"
  "version" "1.2.0"

"hex-color-regex@^1.1.0":
  "integrity" "sha512-l9sfDFsuqtOqKDsQdqrMRk0U85RZc0RtOR9yPI7mRVOa4FsR/BVnZ0shmQRM96Ji99kYZP/7hn1cedc1+ApsTQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/hex-color-regex/-/hex-color-regex-1.1.0.tgz"
  "version" "1.1.0"

"hmac-drbg@^1.0.1":
  "integrity" "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/hmac-drbg/-/hmac-drbg-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "hash.js" "^1.0.3"
    "minimalistic-assert" "^1.0.0"
    "minimalistic-crypto-utils" "^1.0.1"

"home-or-tmp@^2.0.0":
  "integrity" "sha1-42w/LSyufXRqhX440Y1fMqeILbg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/home-or-tmp/-/home-or-tmp-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "os-homedir" "^1.0.0"
    "os-tmpdir" "^1.0.1"

"hosted-git-info@^2.1.4":
  "integrity" "sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/hosted-git-info/-/hosted-git-info-2.8.9.tgz"
  "version" "2.8.9"

"hpack.js@^2.1.6":
  "integrity" "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/hpack.js/-/hpack.js-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "inherits" "^2.0.1"
    "obuf" "^1.0.0"
    "readable-stream" "^2.0.1"
    "wbuf" "^1.1.0"

"hsl-regex@^1.0.0":
  "integrity" "sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/hsl-regex/-/hsl-regex-1.0.0.tgz"
  "version" "1.0.0"

"hsla-regex@^1.0.0":
  "integrity" "sha1-wc56MWjIxmFAM6S194d/OyJfnDg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/hsla-regex/-/hsla-regex-1.0.0.tgz"
  "version" "1.0.0"

"html-comment-regex@^1.1.0":
  "integrity" "sha512-P+M65QY2JQ5Y0G9KKdlDpo0zK+/OHptU5AaBwUfAIDJZk1MYf32Frm84EcOytfJE0t5JvkAnKlmjsXDnWzCJmQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/html-comment-regex/-/html-comment-regex-1.1.2.tgz"
  "version" "1.1.2"

"html-entities@^1.2.0":
  "integrity" "sha512-8nxjcBcd8wovbeKx7h3wTji4e6+rhaVuPNpMqwWgnHh+N9ToqsCs6XztWRBPQ+UtzsoMAdKZtUENoVzU/EMtZA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/html-entities/-/html-entities-1.4.0.tgz"
  "version" "1.4.0"

"html-minifier@^3.2.3":
  "integrity" "sha512-LKUKwuJDhxNa3uf/LPR/KVjm/l3rBqtYeCOAekvG8F1vItxMUpueGd94i/asDDr8/1u7InxzFA5EeGjhhG5mMA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/html-minifier/-/html-minifier-3.5.21.tgz"
  "version" "3.5.21"
  dependencies:
    "camel-case" "3.0.x"
    "clean-css" "4.2.x"
    "commander" "2.17.x"
    "he" "1.2.x"
    "param-case" "2.1.x"
    "relateurl" "0.2.x"
    "uglify-js" "3.4.x"

"html-webpack-plugin@^2.30.1":
  "integrity" "sha1-f5xCG36pHsRg9WUn1430hO51N9U="
  "resolved" "https://repo.huaweicloud.com/repository/npm/html-webpack-plugin/-/html-webpack-plugin-2.30.1.tgz"
  "version" "2.30.1"
  dependencies:
    "bluebird" "^3.4.7"
    "html-minifier" "^3.2.3"
    "loader-utils" "^0.2.16"
    "lodash" "^4.17.3"
    "pretty-error" "^2.0.2"
    "toposort" "^1.0.0"

"htmlparser2@^3.10.0":
  "integrity" "sha512-IgieNijUMbkDovyoKObU1DUhm1iwNYE/fuifEoEHfd1oZKZDaONBSkal7Y01shxsM49R4XaMdGez3WnF9UfiCQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/htmlparser2/-/htmlparser2-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "domelementtype" "^1.3.1"
    "domhandler" "^2.3.0"
    "domutils" "^1.5.1"
    "entities" "^1.1.1"
    "inherits" "^2.0.1"
    "readable-stream" "^3.1.1"

"htmlparser2@^6.1.0":
  "integrity" "sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/htmlparser2/-/htmlparser2-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.0.0"
    "domutils" "^2.5.2"
    "entities" "^2.0.0"

"http-deceiver@^1.2.7":
  "integrity" "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/http-deceiver/-/http-deceiver-1.2.7.tgz"
  "version" "1.2.7"

"http-errors@~1.6.2":
  "integrity" "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/http-errors/-/http-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" ">= 1.4.0 < 2"

"http-errors@1.8.1":
  "integrity" "sha512-Kpk9Sm7NmI+RHhnj6OIWDI1d6fIoFAtFt9RLaTMRlg/8w49juAStsrBgp0Dp4OdxdVbRIeKhtCUvoi/RuAhO4g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/http-errors/-/http-errors-1.8.1.tgz"
  "version" "1.8.1"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.4"
    "setprototypeof" "1.2.0"
    "statuses" ">= 1.5.0 < 2"
    "toidentifier" "1.0.1"

"http-parser-js@>=0.5.1":
  "integrity" "sha512-vDlkRPDJn93swjcjqMSaGSPABbIarsr1TLAui/gLDXzV5VsJNdXNzMYDyNBLQkjWQCJ1uizu8T2oDMhmGt0PRA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/http-parser-js/-/http-parser-js-0.5.6.tgz"
  "version" "0.5.6"

"http-proxy-middleware@^0.19.1":
  "integrity" "sha512-aYk1rTKqLTus23X3L96LGNCGNgWpG4cG0XoZIT1GUPhhulEHX/QalnO6Vbo+WmKWi4AL2IidjuC0wZtbpg0yhQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/http-proxy-middleware/-/http-proxy-middleware-0.19.2.tgz"
  "version" "0.19.2"
  dependencies:
    "http-proxy" "^1.18.1"
    "is-glob" "^4.0.0"
    "lodash" "^4.17.11"
    "micromatch" "^3.1.10"

"http-proxy@^1.18.1":
  "integrity" "sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/http-proxy/-/http-proxy-1.18.1.tgz"
  "version" "1.18.1"
  dependencies:
    "eventemitter3" "^4.0.0"
    "follow-redirects" "^1.0.0"
    "requires-port" "^1.0.0"

"https-browserify@^1.0.0":
  "integrity" "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/https-browserify/-/https-browserify-1.0.0.tgz"
  "version" "1.0.0"

"iconv-lite@^0.4.17", "iconv-lite@^0.4.4", "iconv-lite@0.4.24":
  "integrity" "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/iconv-lite/-/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"icss-replace-symbols@^1.1.0":
  "integrity" "sha1-Bupvg2ead0njhs/h/oEq5dsiPe0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/icss-replace-symbols/-/icss-replace-symbols-1.1.0.tgz"
  "version" "1.1.0"

"icss-utils@^2.1.0":
  "integrity" "sha1-g/Cg7DeL8yRheLbCrZE28TWxyWI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/icss-utils/-/icss-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "postcss" "^6.0.1"

"ieee754@^1.1.4":
  "integrity" "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ieee754/-/ieee754-1.2.1.tgz"
  "version" "1.2.1"

"iferr@^0.1.5":
  "integrity" "sha1-xg7taebY/bazEEofy8ocGS3FtQE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/iferr/-/iferr-0.1.5.tgz"
  "version" "0.1.5"

"ignore@^3.3.3", "ignore@^3.3.5", "ignore@^3.3.6":
  "integrity" "sha512-Pgs951kaMm5GXP7MOvxERINe3gsaVjUWFm+UZPSq9xYriQAksyhg0csnS0KXSNRD5NmNdapXEpjxG49+AKh/ug=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ignore/-/ignore-3.3.10.tgz"
  "version" "3.3.10"

"image-size@~0.5.0":
  "integrity" "sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w="
  "resolved" "https://repo.huaweicloud.com/repository/npm/image-size/-/image-size-0.5.5.tgz"
  "version" "0.5.5"

"import-cwd@^2.0.0":
  "integrity" "sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/import-cwd/-/import-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "import-from" "^2.1.0"

"import-fresh@^2.0.0":
  "integrity" "sha1-2BNVwVYS04bGH53dOSLUMEgipUY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/import-fresh/-/import-fresh-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-path" "^2.0.0"
    "resolve-from" "^3.0.0"

"import-from@^2.1.0":
  "integrity" "sha1-M1238qev/VOqpHHUuAId7ja387E="
  "resolved" "https://repo.huaweicloud.com/repository/npm/import-from/-/import-from-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "resolve-from" "^3.0.0"

"import-local@^1.0.0":
  "integrity" "sha512-vAaZHieK9qjGo58agRBg+bhHX3hoTZU/Oa3GESWLz7t1U62fk63aHuDJJEteXoDeTCcPmUT+z38gkHPZkkmpmQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/import-local/-/import-local-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "pkg-dir" "^2.0.0"
    "resolve-cwd" "^2.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o="
  "resolved" "https://repo.huaweicloud.com/repository/npm/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^2.1.0":
  "integrity" "sha1-ji1INIdCEhtKghi3oTfppSBJ3IA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/indent-string/-/indent-string-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "repeating" "^2.0.0"

"indexes-of@^1.0.1":
  "integrity" "sha1-8w9xbI4r00bHtn0985FVZqfAVgc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/indexes-of/-/indexes-of-1.0.1.tgz"
  "version" "1.0.1"

"inflight@^1.0.4":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.1", "inherits@~2.0.3", "inherits@2", "inherits@2.0.4":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inherits@2.0.1":
  "integrity" "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/inherits/-/inherits-2.0.1.tgz"
  "version" "2.0.1"

"inherits@2.0.3":
  "integrity" "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/inherits/-/inherits-2.0.3.tgz"
  "version" "2.0.3"

"inquirer@^3.0.6":
  "integrity" "sha512-h+xtnyk4EwKvFWHrUYsWErEVR+igKtLdchu+o0Z1RL7VU/jVMFbYir2bp6bAj8efFNxWqHX0dIss6fJQ+/+qeQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/inquirer/-/inquirer-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "ansi-escapes" "^3.0.0"
    "chalk" "^2.0.0"
    "cli-cursor" "^2.1.0"
    "cli-width" "^2.0.0"
    "external-editor" "^2.0.4"
    "figures" "^2.0.0"
    "lodash" "^4.3.0"
    "mute-stream" "0.0.7"
    "run-async" "^2.2.0"
    "rx-lite" "^4.0.8"
    "rx-lite-aggregates" "^4.0.8"
    "string-width" "^2.1.0"
    "strip-ansi" "^4.0.0"
    "through" "^2.3.6"

"internal-ip@1.2.0":
  "integrity" "sha1-rp+/k7mEh4eF1QqN4bNWlWBYz1w="
  "resolved" "https://repo.huaweicloud.com/repository/npm/internal-ip/-/internal-ip-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "meow" "^3.3.0"

"internal-slot@^1.0.3":
  "integrity" "sha512-O0DB1JC/sPyZl7cIo78n5dR7eUSwwpYPiXRhTzNxZVAMUuB8vlnRFyLxdrVToks6XPLVnFfbzaVd5WLjhgg+vA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/internal-slot/-/internal-slot-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "get-intrinsic" "^1.1.0"
    "has" "^1.0.3"
    "side-channel" "^1.0.4"

"interpret@^1.0.0":
  "integrity" "sha512-agE4QfB2Lkp9uICn7BAqoscw4SZP9kTE2hxiFI3jBPmXJfdqiahTbUuKGsMoN2GtqL9AxhYioAcVvgsb1HvRbA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/interpret/-/interpret-1.4.0.tgz"
  "version" "1.4.0"

"invariant@^2.2.0", "invariant@^2.2.2":
  "integrity" "sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/invariant/-/invariant-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "loose-envify" "^1.0.0"

"invert-kv@^1.0.0":
  "integrity" "sha1-EEqOSqym09jNFXqO+L+rLXo//bY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/invert-kv/-/invert-kv-1.0.0.tgz"
  "version" "1.0.0"

"ip@^1.1.0", "ip@^1.1.5":
  "integrity" "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ip/-/ip-1.1.5.tgz"
  "version" "1.1.5"

"ipaddr.js@1.9.1":
  "integrity" "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  "version" "1.9.1"

"is-absolute-url@^2.0.0":
  "integrity" "sha1-UFMN+4T8yap9vnhS6Do3uTufKqY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-absolute-url/-/is-absolute-url-2.1.0.tgz"
  "version" "2.1.0"

"is-accessor-descriptor@^0.1.6":
  "integrity" "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "kind-of" "^3.0.2"

"is-accessor-descriptor@^1.0.0":
  "integrity" "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-arguments@^1.0.4":
  "integrity" "sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-arguments/-/is-arguments-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-arrayish/-/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-arrayish@^0.3.1":
  "integrity" "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-arrayish/-/is-arrayish-0.3.2.tgz"
  "version" "0.3.2"

"is-bigint@^1.0.1":
  "integrity" "sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-bigint/-/is-bigint-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-bigints" "^1.0.1"

"is-binary-path@^1.0.0":
  "integrity" "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-binary-path/-/is-binary-path-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "binary-extensions" "^1.0.0"

"is-binary-path@~2.1.0":
  "integrity" "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-binary-path/-/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-boolean-object@^1.1.0":
  "integrity" "sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-buffer@^1.1.5":
  "integrity" "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-buffer/-/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-callable@^1.1.4", "is-callable@^1.2.4":
  "integrity" "sha512-nsuwtxZfMX67Oryl9LCQ+upnC0Z0BgpwntpS89m1H/TLF0zNfzfLMV/9Wa/6MZsj0acpEjAO0KF1xT6ZdLl95w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-callable/-/is-callable-1.2.4.tgz"
  "version" "1.2.4"

"is-color-stop@^1.0.0":
  "integrity" "sha1-z/9HGu5N1cnhWFmPvhKWe1za00U="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-color-stop/-/is-color-stop-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "css-color-names" "^0.0.4"
    "hex-color-regex" "^1.1.0"
    "hsl-regex" "^1.0.0"
    "hsla-regex" "^1.0.0"
    "rgb-regex" "^1.0.1"
    "rgba-regex" "^1.0.0"

"is-core-module@^2.8.0", "is-core-module@^2.8.1":
  "integrity" "sha512-SdNCUs284hr40hFTFP6l0IfZ/RSrMXF3qgoRHd3/79unUTvrFO/JoXwkGm+5J/Oe3E/b5GsnG330uUNgRpu1PA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-core-module/-/is-core-module-2.8.1.tgz"
  "version" "2.8.1"
  dependencies:
    "has" "^1.0.3"

"is-data-descriptor@^0.1.4":
  "integrity" "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"

"is-data-descriptor@^1.0.0":
  "integrity" "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-date-object@^1.0.1":
  "integrity" "sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-date-object/-/is-date-object-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-descriptor@^0.1.0":
  "integrity" "sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-descriptor/-/is-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "^0.1.6"
    "is-data-descriptor" "^0.1.4"
    "kind-of" "^5.0.0"

"is-descriptor@^1.0.0":
  "integrity" "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-descriptor/-/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-descriptor@^1.0.2":
  "integrity" "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-descriptor/-/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-directory@^0.3.1":
  "integrity" "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-directory/-/is-directory-0.3.1.tgz"
  "version" "0.3.1"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "integrity" "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-extendable/-/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-extendable/-/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^2.1.0", "is-extglob@^2.1.1":
  "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-finite@^1.0.0":
  "integrity" "sha512-cdyMtqX/BOqqNBBiKlIVkytNHm49MtMlYyn1zxzvJKWmFMlGzm+ry5BBfYyeY9YmNKbRSo/o7OX9w9ale0wg3w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-finite/-/is-finite-1.1.0.tgz"
  "version" "1.1.0"

"is-fullwidth-code-point@^1.0.0":
  "integrity" "sha1-754xOG8DGn8NZDr4L95QxFfvAMs="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "number-is-nan" "^1.0.0"

"is-fullwidth-code-point@^2.0.0":
  "integrity" "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz"
  "version" "2.0.0"

"is-glob@^3.1.0":
  "integrity" "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-glob/-/is-glob-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-extglob" "^2.1.0"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@^4.0.3", "is-glob@~4.0.1":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-negative-zero@^2.0.2":
  "integrity" "sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-negative-zero/-/is-negative-zero-2.0.2.tgz"
  "version" "2.0.2"

"is-number-object@^1.0.4":
  "integrity" "sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-number-object/-/is-number-object-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-number@^3.0.0":
  "integrity" "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-number/-/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-obj@^2.0.0":
  "integrity" "sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-obj/-/is-obj-2.0.0.tgz"
  "version" "2.0.0"

"is-path-cwd@^1.0.0":
  "integrity" "sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-path-cwd/-/is-path-cwd-1.0.0.tgz"
  "version" "1.0.0"

"is-path-in-cwd@^1.0.0":
  "integrity" "sha512-FjV1RTW48E7CWM7eE/J2NJvAEEVektecDBVBE5Hh3nM1Jd0kvhHtX68Pr3xsDf857xt3Y4AkwVULK1Vku62aaQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-path-in-cwd/-/is-path-in-cwd-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-path-inside" "^1.0.0"

"is-path-inside@^1.0.0":
  "integrity" "sha1-jvW33lBDej/cprToZe96pVy0gDY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-path-inside/-/is-path-inside-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "path-is-inside" "^1.0.1"

"is-plain-obj@^1.0.0":
  "integrity" "sha1-caUMhCnfync8kqOQpKA7OfzVHT4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-plain-obj/-/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
  "integrity" "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-plain-object/-/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-regex@^1.0.4", "is-regex@^1.1.4":
  "integrity" "sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-regex/-/is-regex-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-resolvable@^1.0.0":
  "integrity" "sha512-qgDYXFSR5WvEfuS5dMj6oTMEbrrSaM0CrFk2Yiq/gXnBvD9pMa2jGXxyhGLfvhZpuMZe18CJpFxAt3CRs42NMg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-resolvable/-/is-resolvable-1.1.0.tgz"
  "version" "1.1.0"

"is-shared-array-buffer@^1.0.1":
  "integrity" "sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.2"

"is-stream@^1.1.0":
  "integrity" "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-stream/-/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-string@^1.0.5", "is-string@^1.0.7":
  "integrity" "sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-string/-/is-string-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-svg@^2.0.0":
  "integrity" "sha1-z2EJDaDZ77yrhyLeum8DIgjbsOk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-svg/-/is-svg-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "html-comment-regex" "^1.1.0"

"is-symbol@^1.0.2", "is-symbol@^1.0.3":
  "integrity" "sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-symbol/-/is-symbol-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-symbols" "^1.0.2"

"is-utf8@^0.2.0":
  "integrity" "sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-utf8/-/is-utf8-0.2.1.tgz"
  "version" "0.2.1"

"is-weakref@^1.0.2":
  "integrity" "sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-weakref/-/is-weakref-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.2"

"is-what@^3.14.1":
  "integrity" "sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-what/-/is-what-3.14.1.tgz"
  "version" "3.14.1"

"is-windows@^1.0.2":
  "integrity" "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-windows/-/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"is-wsl@^1.1.0":
  "integrity" "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/is-wsl/-/is-wsl-1.1.0.tgz"
  "version" "1.1.0"

"isarray@^1.0.0", "isarray@~1.0.0", "isarray@1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isexe@^2.0.0":
  "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^2.0.0":
  "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/isobject/-/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0", "isobject@^3.0.1":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/isobject/-/isobject-3.0.1.tgz"
  "version" "3.0.1"

"js-base64@^2.1.9":
  "integrity" "sha512-pZe//GGmwJndub7ZghVHz7vjb2LgC1m8B07Au3eYqeqv9emhESByMXxaEgkUkEqJe87oBbSniGYoQNIBklc7IQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/js-base64/-/js-base64-2.6.4.tgz"
  "version" "2.6.4"

"js-tokens@^3.0.0", "js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^3.0.2":
  "integrity" "sha1-mGbfOVECEw449/mWvOtlRDIJwls="
  "resolved" "https://repo.huaweicloud.com/repository/npm/js-tokens/-/js-tokens-3.0.2.tgz"
  "version" "3.0.2"

"js-yaml@^3.13.1":
  "integrity" "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/js-yaml/-/js-yaml-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"js-yaml@^3.4.3", "js-yaml@~3.7.0":
  "integrity" "sha1-XJZ93YN6m/3KXy3oQlOr6KHAO4A="
  "resolved" "https://repo.huaweicloud.com/repository/npm/js-yaml/-/js-yaml-3.7.0.tgz"
  "version" "3.7.0"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^2.6.0"

"js-yaml@^3.9.1":
  "integrity" "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/js-yaml/-/js-yaml-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"jsesc@^1.3.0":
  "integrity" "sha1-RsP+yMGJKxKwgz25vHYiF226s0s="
  "resolved" "https://repo.huaweicloud.com/repository/npm/jsesc/-/jsesc-1.3.0.tgz"
  "version" "1.3.0"

"jsesc@^2.5.1":
  "integrity" "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/jsesc/-/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"jsesc@~0.5.0":
  "integrity" "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/jsesc/-/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-loader@^0.5.4":
  "integrity" "sha512-QLPs8Dj7lnf3e3QYS1zkCo+4ZwqOiF9d/nZnYozTISxXWCfNs9yuky5rJw4/W34s7POaNlbZmQGaB5NiXCbP4w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/json-loader/-/json-loader-0.5.7.tgz"
  "version" "0.5.7"

"json-parse-better-errors@^1.0.1":
  "integrity" "sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-schema-traverse@^0.3.0":
  "integrity" "sha1-NJptRMU6Ud6JtAgFxdXlm0F9M0A="
  "resolved" "https://repo.huaweicloud.com/repository/npm/json-schema-traverse/-/json-schema-traverse-0.3.1.tgz"
  "version" "0.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json3@^3.3.2":
  "integrity" "sha512-c7/8mbUsKigAbLkD5B010BK4D9LZm7A1pNItkEwiUZRpIN66exu/e7YQWysGun+TRKaJp8MhemM+VkfWv42aCA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/json3/-/json3-3.3.3.tgz"
  "version" "3.3.3"

"json5@^0.5.0", "json5@^0.5.1":
  "integrity" "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/json5/-/json5-0.5.1.tgz"
  "version" "0.5.1"

"json5@^1.0.1":
  "integrity" "sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/json5/-/json5-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "minimist" "^1.2.0"

"killable@^1.0.0":
  "integrity" "sha512-LzqtLKlUwirEUyl/nicirVmNiPvYs7l5n8wOPP7fyJVpUPkvCnW/vuiXGpylGUlnPDnB7311rARzAt3Mhswpjg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/killable/-/killable-1.0.1.tgz"
  "version" "1.0.1"

"kind-of@^3.0.2", "kind-of@^3.0.3", "kind-of@^3.2.0":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "integrity" "sha1-IIE989cSkosgc3hpGkUGb65y3Vc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/kind-of/-/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.0":
  "integrity" "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/kind-of/-/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^6.0.0", "kind-of@^6.0.2":
  "integrity" "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/kind-of/-/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"last-call-webpack-plugin@^2.1.2":
  "integrity" "sha512-CZc+m2xZm51J8qSwdODeiiNeqh8CYkKEq6Rw8IkE4i/4yqf2cJhjQPsA6BtAV970ePRNhwEOXhy2U5xc5Jwh9Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/last-call-webpack-plugin/-/last-call-webpack-plugin-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "lodash" "^4.17.4"
    "webpack-sources" "^1.0.1"

"lazy-cache@^1.0.3":
  "integrity" "sha1-odePw6UEdMuAhF07O24dpJpEbo4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/lazy-cache/-/lazy-cache-1.0.4.tgz"
  "version" "1.0.4"

"lcid@^1.0.0":
  "integrity" "sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/lcid/-/lcid-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "invert-kv" "^1.0.0"

"less-loader@^4.0.5":
  "integrity" "sha512-KNTsgCE9tMOM70+ddxp9yyt9iHqgmSs0yTZc5XH5Wo+g80RWRIYNqE58QJKm/yMud5wZEvz50ugRDuzVIkyahg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/less-loader/-/less-loader-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "clone" "^2.1.1"
    "loader-utils" "^1.1.0"
    "pify" "^3.0.0"

"less@4.1.1":
  "integrity" "sha512-w09o8tZFPThBscl5d0Ggp3RcrKIouBoQscnOMgFH3n5V3kN/CXGHNfCkRPtxJk6nKryDXaV9aHLK55RXuH4sAw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/less/-/less-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "copy-anything" "^2.0.1"
    "parse-node-version" "^1.0.1"
    "tslib" "^1.10.0"
  optionalDependencies:
    "errno" "^0.1.1"
    "graceful-fs" "^4.1.2"
    "image-size" "~0.5.0"
    "make-dir" "^2.1.0"
    "mime" "^1.4.1"
    "needle" "^2.5.2"
    "source-map" "~0.6.0"

"levn@^0.3.0", "levn@~0.3.0":
  "integrity" "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/levn/-/levn-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"

"load-json-file@^1.0.0":
  "integrity" "sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/load-json-file/-/load-json-file-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "parse-json" "^2.2.0"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"
    "strip-bom" "^2.0.0"

"load-json-file@^2.0.0":
  "integrity" "sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/load-json-file/-/load-json-file-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "parse-json" "^2.2.0"
    "pify" "^2.0.0"
    "strip-bom" "^3.0.0"

"loader-fs-cache@^1.0.0":
  "integrity" "sha512-ldcgZpjNJj71n+2Mf6yetz+c9bM4xpKtNds4LbqXzU/PTdeAX0g3ytnU1AJMEcTk2Lex4Smpe3Q/eCTsvUBxbA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/loader-fs-cache/-/loader-fs-cache-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "find-cache-dir" "^0.1.1"
    "mkdirp" "^0.5.1"

"loader-runner@^2.3.0":
  "integrity" "sha512-Jsmr89RcXGIwivFY21FcRrisYZfvLMTWx5kOLc+JTxtpBOG6xML0vzbc6SEQG2FO9/4Fc3wW4LVcB5DmGflaRw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/loader-runner/-/loader-runner-2.4.0.tgz"
  "version" "2.4.0"

"loader-utils@^0.2.16":
  "integrity" "sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g="
  "resolved" "https://repo.huaweicloud.com/repository/npm/loader-utils/-/loader-utils-0.2.17.tgz"
  "version" "0.2.17"
  dependencies:
    "big.js" "^3.1.3"
    "emojis-list" "^2.0.0"
    "json5" "^0.5.0"
    "object-assign" "^4.0.1"

"loader-utils@^1.0.2", "loader-utils@^1.1.0":
  "integrity" "sha512-qH0WSMBtn/oHuwjy/NucEgbx5dbxxnxup9s4PVXJUDHZBQY+s0NWA9rJf53RBnQZxfch7euUui7hpoAPvALZdA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/loader-utils/-/loader-utils-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^1.0.1"

"locate-path@^2.0.0":
  "integrity" "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/locate-path/-/locate-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-locate" "^2.0.0"
    "path-exists" "^3.0.0"

"lodash.camelcase@^4.3.0":
  "integrity" "sha1-soqmKIorn8ZRA1x3EfZathkDMaY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz"
  "version" "4.3.0"

"lodash.clonedeep@^4.5.0":
  "integrity" "sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz"
  "version" "4.5.0"

"lodash.memoize@^4.1.2":
  "integrity" "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  "version" "4.1.2"

"lodash.uniq@^4.5.0":
  "integrity" "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="
  "resolved" "https://repo.huaweicloud.com/repository/npm/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash@^4.17.11", "lodash@^4.17.14", "lodash@^4.17.15", "lodash@^4.17.20", "lodash@^4.17.21", "lodash@^4.17.3", "lodash@^4.17.4", "lodash@^4.2.0", "lodash@^4.3.0":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"log-symbols@^2.1.0":
  "integrity" "sha512-VeIAFslyIerEJLXHziedo2basKbMKtTw3vfn5IzG0XTjhAVEJyNHnL2p7vc+wBDSdQuUpNw3M2u6xb9QsAY5Eg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/log-symbols/-/log-symbols-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "chalk" "^2.0.1"

"loglevel@^1.4.1":
  "integrity" "sha512-G6A/nJLRgWOuuwdNuA6koovfEV1YpqqAG4pRUlFaz3jj2QNZ8M4vBqnVA+HBTmU/AMNUtlOsMmSpF6NyOjztbA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/loglevel/-/loglevel-1.8.0.tgz"
  "version" "1.8.0"

"longest@^1.0.1":
  "integrity" "sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/longest/-/longest-1.0.1.tgz"
  "version" "1.0.1"

"loose-envify@^1.0.0":
  "integrity" "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/loose-envify/-/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lottie-web@5.7.1":
  "integrity" "sha512-f7kO4Qcurldc9SWfGUuwgs96/SCT+hwATMCdMK6I/sd6EYK3Y0Ciz40yAvq0x9uXzrk6KKcnQrqijZM8Ir4Drg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/lottie-web/-/lottie-web-5.7.1.tgz"
  "version" "5.7.1"

"loud-rejection@^1.0.0":
  "integrity" "sha1-W0b4AUft7leIcPCG0Eghz5mOVR8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/loud-rejection/-/loud-rejection-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "currently-unhandled" "^0.4.1"
    "signal-exit" "^3.0.0"

"lower-case@^1.1.1":
  "integrity" "sha1-miyr0bno4K6ZOkv31YdcOcQujqw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/lower-case/-/lower-case-1.1.4.tgz"
  "version" "1.1.4"

"lru-cache@^4.0.1", "lru-cache@^4.1.1":
  "integrity" "sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/lru-cache/-/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"make-dir@^1.0.0":
  "integrity" "sha512-2w31R7SJtieJJnQtGc7RVL2StM2vGYVfqUOvUDxH6bC6aJTxPxTF0GnIgCyu7tjockiUWAYQRbxa7vKn34s5sQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/make-dir/-/make-dir-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "pify" "^3.0.0"

"make-dir@^2.1.0":
  "integrity" "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/make-dir/-/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"map-cache@^0.2.2":
  "integrity" "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/map-cache/-/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-obj@^1.0.0", "map-obj@^1.0.1":
  "integrity" "sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/map-obj/-/map-obj-1.0.1.tgz"
  "version" "1.0.1"

"map-visit@^1.0.0":
  "integrity" "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48="
  "resolved" "https://repo.huaweicloud.com/repository/npm/map-visit/-/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"math-expression-evaluator@^1.2.14":
  "integrity" "sha512-M6AMrvq9bO8uL42KvQHPA2/SbAobA0R7gviUmPrcTcGfdwpaLitz4q2Euzx2lP9Oy88vxK3HOrsISgSwKsYS4A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/math-expression-evaluator/-/math-expression-evaluator-1.3.14.tgz"
  "version" "1.3.14"

"md5.js@^1.3.4":
  "integrity" "sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/md5.js/-/md5.js-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"mdn-data@2.0.14":
  "integrity" "sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/mdn-data/-/mdn-data-2.0.14.tgz"
  "version" "2.0.14"

"mdn-data@2.0.4":
  "integrity" "sha512-iV3XNKw06j5Q7mi6h+9vbx23Tv7JkjEVgKHW4pimwyDGWm0OIQntJJ+u1C6mg6mK1EaTv42XQ7w76yuzH7M2cA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/mdn-data/-/mdn-data-2.0.4.tgz"
  "version" "2.0.4"

"media-typer@0.3.0":
  "integrity" "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="
  "resolved" "https://repo.huaweicloud.com/repository/npm/media-typer/-/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"mem@^1.1.0":
  "integrity" "sha1-Xt1StIXKHZAP5kiVUFOZoN+kX3Y="
  "resolved" "https://repo.huaweicloud.com/repository/npm/mem/-/mem-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "mimic-fn" "^1.0.0"

"memory-fs@^0.4.0", "memory-fs@~0.4.1":
  "integrity" "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/memory-fs/-/memory-fs-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"meow@^3.3.0":
  "integrity" "sha1-cstmi0JSKCkKu/qFaJJYcwioAfs="
  "resolved" "https://repo.huaweicloud.com/repository/npm/meow/-/meow-3.7.0.tgz"
  "version" "3.7.0"
  dependencies:
    "camelcase-keys" "^2.0.0"
    "decamelize" "^1.1.2"
    "loud-rejection" "^1.0.0"
    "map-obj" "^1.0.1"
    "minimist" "^1.1.3"
    "normalize-package-data" "^2.3.4"
    "object-assign" "^4.0.1"
    "read-pkg-up" "^1.0.1"
    "redent" "^1.0.0"
    "trim-newlines" "^1.0.0"

"merge-descriptors@1.0.1":
  "integrity" "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="
  "resolved" "https://repo.huaweicloud.com/repository/npm/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  "version" "1.0.1"

"methods@~1.1.2":
  "integrity" "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/methods/-/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^3.1.10", "micromatch@^3.1.4":
  "integrity" "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/micromatch/-/micromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"miller-rabin@^4.0.0":
  "integrity" "sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/miller-rabin/-/miller-rabin-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "^4.0.0"
    "brorand" "^1.0.1"

"mime-db@>= 1.43.0 < 2", "mime-db@1.52.0":
  "integrity" "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-types@~2.1.17", "mime-types@~2.1.24", "mime-types@~2.1.34":
  "integrity" "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"mime@^1.4.1", "mime@^1.5.0", "mime@1.6.0":
  "integrity" "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/mime/-/mime-1.6.0.tgz"
  "version" "1.6.0"

"mime@1.3.x":
  "integrity" "sha1-WR2E02U6awtKO5343lqoEI5y5eA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/mime/-/mime-1.3.6.tgz"
  "version" "1.3.6"

"mimic-fn@^1.0.0":
  "integrity" "sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/mimic-fn/-/mimic-fn-1.2.0.tgz"
  "version" "1.2.0"

"minimalistic-assert@^1.0.0", "minimalistic-assert@^1.0.1":
  "integrity" "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimalistic-crypto-utils@^1.0.1":
  "integrity" "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.2", "minimatch@^3.0.4":
  "integrity" "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/minimatch/-/minimatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimist@^1.1.3", "minimist@^1.2.0", "minimist@^1.2.6":
  "integrity" "sha512-Jsjnk4bw3YJqYzbdyBiNsPWHPfO++UGG749Cxs6peCu5Xg4nrena6OVxOYxrQTqww0Jmwt+Ref8rggumkTLz9Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/minimist/-/minimist-1.2.6.tgz"
  "version" "1.2.6"

"mississippi@^2.0.0":
  "integrity" "sha512-zHo8v+otD1J10j/tC+VNoGK9keCuByhKovAvdn74dmxJl9+mWHnx6EMsDN4lgRoMI/eYo2nchAxniIbUPb5onw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/mississippi/-/mississippi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "concat-stream" "^1.5.0"
    "duplexify" "^3.4.2"
    "end-of-stream" "^1.1.0"
    "flush-write-stream" "^1.0.0"
    "from2" "^2.1.0"
    "parallel-transform" "^1.1.0"
    "pump" "^2.0.1"
    "pumpify" "^1.3.3"
    "stream-each" "^1.1.0"
    "through2" "^2.0.0"

"mixin-deep@^1.2.0":
  "integrity" "sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/mixin-deep/-/mixin-deep-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mkdirp@^0.5.0", "mkdirp@^0.5.1", "mkdirp@^0.5.5", "mkdirp@~0.5.0", "mkdirp@~0.5.1":
  "integrity" "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/mkdirp/-/mkdirp-0.5.6.tgz"
  "version" "0.5.6"
  dependencies:
    "minimist" "^1.2.6"

"mkdirp@~1.0.4":
  "integrity" "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/mkdirp/-/mkdirp-1.0.4.tgz"
  "version" "1.0.4"

"moment-duration-format@^2.3.2":
  "integrity" "sha512-cBMXjSW+fjOb4tyaVHuaVE/A5TqkukDWiOfxxAjY+PEqmmBQlLwn+8OzwPiG3brouXKY5Un4pBjAeB6UToXHaQ=="
  "resolved" "https://registry.npmmirror.com/moment-duration-format/-/moment-duration-format-2.3.2.tgz"
  "version" "2.3.2"

"moment@^2.29.4":
  "integrity" "sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how=="
  "resolved" "https://registry.npmmirror.com/moment/-/moment-2.30.1.tgz"
  "version" "2.30.1"

"move-concurrently@^1.0.1":
  "integrity" "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I="
  "resolved" "https://repo.huaweicloud.com/repository/npm/move-concurrently/-/move-concurrently-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "aproba" "^1.1.1"
    "copy-concurrently" "^1.0.0"
    "fs-write-stream-atomic" "^1.0.8"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.3"

"ms@^2.1.1":
  "integrity" "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"ms@2.0.0":
  "integrity" "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ms/-/ms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.2":
  "integrity" "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ms/-/ms-2.1.2.tgz"
  "version" "2.1.2"

"ms@2.1.3":
  "integrity" "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"multicast-dns-service-types@^1.1.0":
  "integrity" "sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz"
  "version" "1.1.0"

"multicast-dns@^6.0.1":
  "integrity" "sha512-ji6J5enbMyGRHIAkAOu3WdV8nggqviKCEKtXcOqfphZZtQrmHKycfynJ2V7eVPUA4NhJ6V7Wf4TmGbTwKE9B6g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/multicast-dns/-/multicast-dns-6.2.3.tgz"
  "version" "6.2.3"
  dependencies:
    "dns-packet" "^1.3.1"
    "thunky" "^1.0.2"

"mute-stream@0.0.7":
  "integrity" "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s="
  "resolved" "https://repo.huaweicloud.com/repository/npm/mute-stream/-/mute-stream-0.0.7.tgz"
  "version" "0.0.7"

"nanomatch@^1.2.9":
  "integrity" "sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/nanomatch/-/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"natural-compare@^1.4.0":
  "integrity" "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"needle@^2.5.2":
  "integrity" "sha512-6R9fqJ5Zcmf+uYaFgdIHmLwNldn5HbK8L5ybn7Uz+ylX/rnOsSp1AHcvQSrCaFN+qNM1wpymHqD7mVasEOlHGQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/needle/-/needle-2.9.1.tgz"
  "version" "2.9.1"
  dependencies:
    "debug" "^3.2.6"
    "iconv-lite" "^0.4.4"
    "sax" "^1.2.4"

"negotiator@0.6.3":
  "integrity" "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/negotiator/-/negotiator-0.6.3.tgz"
  "version" "0.6.3"

"neo-async@^2.5.0":
  "integrity" "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/neo-async/-/neo-async-2.6.2.tgz"
  "version" "2.6.2"

"next-tick@^1.1.0":
  "integrity" "sha512-CXdUiJembsNjuToQvxayPZF9Vqht7hewsvy2sOWafLvi2awflj9mOC6bHIg50orX8IJvWKY9wYQ/zB2kogPslQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/next-tick/-/next-tick-1.1.0.tgz"
  "version" "1.1.0"

"no-case@^2.2.0":
  "integrity" "sha512-rmTZ9kz+f3rCvK2TD1Ue/oZlns7OGoIWP4fc3llxxRXlOkHKoWPPWJOfFYpITabSow43QJbRIoHQXtt10VldyQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/no-case/-/no-case-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "lower-case" "^1.1.1"

"node-forge@^0.10.0":
  "integrity" "sha512-PPmu8eEeG9saEUvI97fm4OYxXVB6bFvyNTyiUOBichBpFG8A1Ljw3bY62+5oOjDEMHRnd0Y7HQ+x7uzxOzC6JA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/node-forge/-/node-forge-0.10.0.tgz"
  "version" "0.10.0"

"node-libs-browser@^2.0.0":
  "integrity" "sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/node-libs-browser/-/node-libs-browser-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "assert" "^1.1.1"
    "browserify-zlib" "^0.2.0"
    "buffer" "^4.3.0"
    "console-browserify" "^1.1.0"
    "constants-browserify" "^1.0.0"
    "crypto-browserify" "^3.11.0"
    "domain-browser" "^1.1.1"
    "events" "^3.0.0"
    "https-browserify" "^1.0.0"
    "os-browserify" "^0.3.0"
    "path-browserify" "0.0.1"
    "process" "^0.11.10"
    "punycode" "^1.2.4"
    "querystring-es3" "^0.2.0"
    "readable-stream" "^2.3.3"
    "stream-browserify" "^2.0.1"
    "stream-http" "^2.7.2"
    "string_decoder" "^1.0.0"
    "timers-browserify" "^2.0.4"
    "tty-browserify" "0.0.0"
    "url" "^0.11.0"
    "util" "^0.11.0"
    "vm-browserify" "^1.0.1"

"node-notifier@^5.1.2":
  "integrity" "sha512-tVbHs7DyTLtzOiN78izLA85zRqB9NvEXkAf014Vx3jtSvn/xBl6bR8ZYifj+dFcFrKI21huSQgJZ6ZtL3B4HfQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/node-notifier/-/node-notifier-5.4.5.tgz"
  "version" "5.4.5"
  dependencies:
    "growly" "^1.3.0"
    "is-wsl" "^1.1.0"
    "semver" "^5.5.0"
    "shellwords" "^0.1.1"
    "which" "^1.3.0"

"node-releases@^2.0.2":
  "integrity" "sha512-XxYDdcQ6eKqp/YjI+tb2C5WM2LgjnZrfYg4vgQt49EK268b6gYCHsBLrK2qvJo4FmCtqmKezb0WZFK4fkrZNsg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/node-releases/-/node-releases-2.0.2.tgz"
  "version" "2.0.2"

"normalize-package-data@^2.3.2", "normalize-package-data@^2.3.4":
  "integrity" "sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^2.1.1":
  "integrity" "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/normalize-path/-/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/normalize-range/-/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"normalize-url@^1.4.0":
  "integrity" "sha1-LMDWazHqIwNkWENuNiDYWVTGbDw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/normalize-url/-/normalize-url-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "object-assign" "^4.0.1"
    "prepend-http" "^1.0.0"
    "query-string" "^4.1.0"
    "sort-keys" "^1.0.0"

"normalize-url@^3.0.0":
  "integrity" "sha512-U+JJi7duF1o+u2pynbp2zXDW2/PADgC30f0GsHZtRh+HOcXHnw137TrNlyxxRvWW5fjKd3bcLHPxofWuCjaeZg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/normalize-url/-/normalize-url-3.3.0.tgz"
  "version" "3.3.0"

"npm-run-path@^2.0.0":
  "integrity" "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/npm-run-path/-/npm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "^2.0.0"

"nth-check@^1.0.2":
  "integrity" "sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/nth-check/-/nth-check-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "boolbase" "~1.0.0"

"nth-check@^2.0.1":
  "integrity" "sha512-it1vE95zF6dTT9lBsYbxvqh0Soy4SPowchj0UBGj/V6cTPnXXtQOPUbhZ6CmGzAD/rW22LQK6E96pcdJXk4A4w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/nth-check/-/nth-check-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "boolbase" "^1.0.0"

"num2fraction@^1.2.2":
  "integrity" "sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/num2fraction/-/num2fraction-1.2.2.tgz"
  "version" "1.2.2"

"number-is-nan@^1.0.0":
  "integrity" "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/number-is-nan/-/number-is-nan-1.0.1.tgz"
  "version" "1.0.1"

"object-assign@^4.0.1", "object-assign@^4.1.0", "object-assign@^4.1.1":
  "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-copy@^0.1.0":
  "integrity" "sha1-fn2Fi3gb18mRpBupde04EnVOmYw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/object-copy/-/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-hash@^1.1.4":
  "integrity" "sha512-OSuu/pU4ENM9kmREg0BdNrUDIl1heYa4mBZacJc+vVWz4GtAwu7jO8s4AIt2aGRUTqxykpWzI3Oqnsm13tTMDA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/object-hash/-/object-hash-1.3.1.tgz"
  "version" "1.3.1"

"object-inspect@^1.12.0", "object-inspect@^1.9.0":
  "integrity" "sha512-Ho2z80bVIvJloH+YzRmpZVQe87+qASmBUKZDWgx9cu+KDrX2ZDH/3tMy+gXbZETVGs2M8YdxObOh7XAtim9Y0g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/object-inspect/-/object-inspect-1.12.0.tgz"
  "version" "1.12.0"

"object-is@^1.0.1":
  "integrity" "sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/object-is/-/object-is-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"object-keys@^1.0.12", "object-keys@^1.1.1":
  "integrity" "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/object-keys/-/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object-visit@^1.0.0":
  "integrity" "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs="
  "resolved" "https://repo.huaweicloud.com/repository/npm/object-visit/-/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.assign@^4.1.2":
  "integrity" "sha512-ixT2L5THXsApyiUPYKmW+2EHpXXe5Ii3M+f4e+aJFAHao5amFRW6J0OO6c/LU8Be47utCx2GL89hxGB6XSmKuQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/object.assign/-/object.assign-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "call-bind" "^1.0.0"
    "define-properties" "^1.1.3"
    "has-symbols" "^1.0.1"
    "object-keys" "^1.1.1"

"object.getownpropertydescriptors@^2.1.0":
  "integrity" "sha512-VdDoCwvJI4QdC6ndjpqFmoL3/+HxffFBbcJzKi5hwLLqqx3mdbedRpfZDdK0SrOSauj8X4GzBvnDZl4vTN7dOw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"

"object.pick@^1.3.0":
  "integrity" "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c="
  "resolved" "https://repo.huaweicloud.com/repository/npm/object.pick/-/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"object.values@^1.1.0", "object.values@^1.1.5":
  "integrity" "sha512-QUZRW0ilQ3PnPpbNtgdNV1PDbEqLIiSFB3l+EnGtBQ/8SUTLj1PZwtQHABZtLgwpJZTSZhuGLOGk57Drx2IvYg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/object.values/-/object.values-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"

"obuf@^1.0.0", "obuf@^1.1.2":
  "integrity" "sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/obuf/-/obuf-1.1.2.tgz"
  "version" "1.1.2"

"on-finished@~2.3.0":
  "integrity" "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/on-finished/-/on-finished-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.0.2":
  "integrity" "sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/on-headers/-/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
  "resolved" "https://repo.huaweicloud.com/repository/npm/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^2.0.0":
  "integrity" "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/onetime/-/onetime-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "mimic-fn" "^1.0.0"

"opener@^1.4.3":
  "integrity" "sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/opener/-/opener-1.5.2.tgz"
  "version" "1.5.2"

"opn@^5.1.0":
  "integrity" "sha512-PqHpggC9bLV0VeWcdKhkpxY+3JTzetLSqTCWL/z/tFIbI6G8JCjondXklT1JinczLz2Xib62sSp0T/gKT4KksA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/opn/-/opn-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "is-wsl" "^1.1.0"

"optimize-css-assets-webpack-plugin@^3.2.0":
  "integrity" "sha512-FSoF15xKSEM2qCE3/y2gH92PysJSBY58Wx/hmSdIzVSOd0vg+FRS28NWZADId1wh6PDlbVt0lfPduV0IBufItQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/optimize-css-assets-webpack-plugin/-/optimize-css-assets-webpack-plugin-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "cssnano" "^4.1.10"
    "last-call-webpack-plugin" "^2.1.2"

"optionator@^0.8.2":
  "integrity" "sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/optionator/-/optionator-0.8.3.tgz"
  "version" "0.8.3"
  dependencies:
    "deep-is" "~0.1.3"
    "fast-levenshtein" "~2.0.6"
    "levn" "~0.3.0"
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"
    "word-wrap" "~1.2.3"

"ora@^1.2.0":
  "integrity" "sha512-iMK1DOQxzzh2MBlVsU42G80mnrvUhqsMh74phHtDlrcTZPK0pH6o7l7DRshK+0YsxDyEuaOkziVdvM3T0QTzpw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ora/-/ora-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "chalk" "^2.1.0"
    "cli-cursor" "^2.1.0"
    "cli-spinners" "^1.0.1"
    "log-symbols" "^2.1.0"

"original@>=0.0.5":
  "integrity" "sha512-hyBVl6iqqUOJ8FqRe+l/gS8H+kKYjrEndd5Pm1MfBtsEKA038HkkdbAl/72EAXGyonD/PFsvmVG+EvcIpliMBg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/original/-/original-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "url-parse" "^1.4.3"

"os-browserify@^0.3.0":
  "integrity" "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/os-browserify/-/os-browserify-0.3.0.tgz"
  "version" "0.3.0"

"os-homedir@^1.0.0", "os-homedir@^1.0.1":
  "integrity" "sha1-/7xJiDNuDoM94MFox+8VISGqf7M="
  "resolved" "https://repo.huaweicloud.com/repository/npm/os-homedir/-/os-homedir-1.0.2.tgz"
  "version" "1.0.2"

"os-locale@^1.4.0":
  "integrity" "sha1-IPnxeuKe00XoveWDsT0gCYA8FNk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/os-locale/-/os-locale-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "lcid" "^1.0.0"

"os-locale@^2.0.0":
  "integrity" "sha512-3sslG3zJbEYcaC4YVAvDorjGxc7tv6KVATnLPZONiljsUncvihe9BQoVCEs0RZ1kmf4Hk9OBqlZfJZWI4GanKA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/os-locale/-/os-locale-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "execa" "^0.7.0"
    "lcid" "^1.0.0"
    "mem" "^1.1.0"

"os-tmpdir@^1.0.1", "os-tmpdir@~1.0.2":
  "integrity" "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"p-finally@^1.0.0":
  "integrity" "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/p-finally/-/p-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-limit@^1.0.0", "p-limit@^1.1.0":
  "integrity" "sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/p-limit/-/p-limit-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "p-try" "^1.0.0"

"p-locate@^2.0.0":
  "integrity" "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/p-locate/-/p-locate-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-limit" "^1.1.0"

"p-map@^1.1.1":
  "integrity" "sha512-r6zKACMNhjPJMTl8KcFH4li//gkrXWfbD6feV8l6doRHlzljFWGJ2AP6iKaCJXyZmAUMOPtvbW7EXkbWO/pLEA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/p-map/-/p-map-1.2.0.tgz"
  "version" "1.2.0"

"p-try@^1.0.0":
  "integrity" "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M="
  "resolved" "https://repo.huaweicloud.com/repository/npm/p-try/-/p-try-1.0.0.tgz"
  "version" "1.0.0"

"pako@~1.0.5":
  "integrity" "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pako/-/pako-1.0.11.tgz"
  "version" "1.0.11"

"parallel-transform@^1.1.0":
  "integrity" "sha512-P2vSmIu38uIlvdcU7fDkyrxj33gTUy/ABO5ZUbGowxNCopBq/OoD42bP4UmMrJoPyk4Uqf0mu3mtWBhHCZD8yg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/parallel-transform/-/parallel-transform-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cyclist" "^1.0.1"
    "inherits" "^2.0.3"
    "readable-stream" "^2.1.5"

"param-case@2.1.x":
  "integrity" "sha1-35T9jPZTHs915r75oIWPvHK+Ikc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/param-case/-/param-case-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "no-case" "^2.2.0"

"parse-asn1@^5.0.0", "parse-asn1@^5.1.5":
  "integrity" "sha512-RnZRo1EPU6JBnra2vGHj0yhp6ebyjBZpmUCLHWiFhxlzvBCCpAuZ7elsBp1PVAbQN0/04VD/19rfzlBSwLstMw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/parse-asn1/-/parse-asn1-5.1.6.tgz"
  "version" "5.1.6"
  dependencies:
    "asn1.js" "^5.2.0"
    "browserify-aes" "^1.0.0"
    "evp_bytestokey" "^1.0.0"
    "pbkdf2" "^3.0.3"
    "safe-buffer" "^5.1.1"

"parse-json@^2.2.0":
  "integrity" "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck="
  "resolved" "https://repo.huaweicloud.com/repository/npm/parse-json/-/parse-json-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "error-ex" "^1.2.0"

"parse-json@^4.0.0":
  "integrity" "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/parse-json/-/parse-json-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "error-ex" "^1.3.1"
    "json-parse-better-errors" "^1.0.1"

"parse-node-version@^1.0.1":
  "integrity" "sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/parse-node-version/-/parse-node-version-1.0.1.tgz"
  "version" "1.0.1"

"parseurl@~1.3.2", "parseurl@~1.3.3":
  "integrity" "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/parseurl/-/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"pascalcase@^0.1.1":
  "integrity" "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pascalcase/-/pascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-browserify@0.0.1":
  "integrity" "sha512-BapA40NHICOS+USX9SN4tyhq+A2RrN/Ws5F0Z5aMHDp98Fl86lX8Oti8B7uN93L4Ifv4fHOEA+pQw87gmMO/lQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/path-browserify/-/path-browserify-0.0.1.tgz"
  "version" "0.0.1"

"path-dirname@^1.0.0":
  "integrity" "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/path-dirname/-/path-dirname-1.0.2.tgz"
  "version" "1.0.2"

"path-exists@^2.0.0":
  "integrity" "sha1-D+tsZPD8UY2adU3V77YscCJ2H0s="
  "resolved" "https://repo.huaweicloud.com/repository/npm/path-exists/-/path-exists-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pinkie-promise" "^2.0.0"

"path-exists@^3.0.0":
  "integrity" "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/path-exists/-/path-exists-3.0.0.tgz"
  "version" "3.0.0"

"path-is-absolute@^1.0.0", "path-is-absolute@^1.0.1":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
  "resolved" "https://repo.huaweicloud.com/repository/npm/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-is-inside@^1.0.1", "path-is-inside@^1.0.2":
  "integrity" "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/path-is-inside/-/path-is-inside-1.0.2.tgz"
  "version" "1.0.2"

"path-key@^2.0.0":
  "integrity" "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="
  "resolved" "https://repo.huaweicloud.com/repository/npm/path-key/-/path-key-2.0.1.tgz"
  "version" "2.0.1"

"path-parse@^1.0.7":
  "integrity" "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/path-parse/-/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-to-regexp@0.1.7":
  "integrity" "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="
  "resolved" "https://repo.huaweicloud.com/repository/npm/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  "version" "0.1.7"

"path-type@^1.0.0":
  "integrity" "sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/path-type/-/path-type-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"path-type@^2.0.0":
  "integrity" "sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/path-type/-/path-type-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "pify" "^2.0.0"

"path-type@^3.0.0":
  "integrity" "sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/path-type/-/path-type-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "pify" "^3.0.0"

"pbkdf2@^3.0.3":
  "integrity" "sha512-iuh7L6jA7JEGu2WxDwtQP1ddOpaJNC4KlDEFfdQajSGgGPNi4OyDc2R7QnbY2bR9QjBVGwgvTdNJZoE7RaxUMA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pbkdf2/-/pbkdf2-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "create-hash" "^1.1.2"
    "create-hmac" "^1.1.4"
    "ripemd160" "^2.0.1"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"picocolors@^0.2.1":
  "integrity" "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/picocolors/-/picocolors-0.2.1.tgz"
  "version" "0.2.1"

"picocolors@^1.0.0":
  "integrity" "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/picocolors/-/picocolors-1.0.0.tgz"
  "version" "1.0.0"

"picomatch@^2.0.4", "picomatch@^2.2.1":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"pify@^2.0.0":
  "integrity" "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pify/-/pify-2.3.0.tgz"
  "version" "2.3.0"

"pify@^2.3.0":
  "integrity" "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pify/-/pify-2.3.0.tgz"
  "version" "2.3.0"

"pify@^3.0.0":
  "integrity" "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pify/-/pify-3.0.0.tgz"
  "version" "3.0.0"

"pify@^4.0.1":
  "integrity" "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pify/-/pify-4.0.1.tgz"
  "version" "4.0.1"

"pinkie-promise@^2.0.0":
  "integrity" "sha1-ITXW36ejWMBprJsXh3YogihFD/o="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pinkie-promise/-/pinkie-promise-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "pinkie" "^2.0.0"

"pinkie@^2.0.0":
  "integrity" "sha1-clVrgM+g1IqXToDnckjoDtT3+HA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pinkie/-/pinkie-2.0.4.tgz"
  "version" "2.0.4"

"pkg-dir@^1.0.0":
  "integrity" "sha1-ektQio1bstYp1EcFb/TpyTFM89Q="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pkg-dir/-/pkg-dir-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "find-up" "^1.0.0"

"pkg-dir@^2.0.0":
  "integrity" "sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pkg-dir/-/pkg-dir-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "^2.1.0"

"pluralize@^7.0.0":
  "integrity" "sha512-ARhBOdzS3e41FbkW/XWrTEtukqqLoK5+Z/4UeDaLuSW+39JPeFgs4gCGqsrJHVZX0fUrx//4OF0K1CUGwlIFow=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pluralize/-/pluralize-7.0.0.tgz"
  "version" "7.0.0"

"portfinder@^1.0.13", "portfinder@^1.0.9":
  "integrity" "sha512-Se+2isanIcEqf2XMHjyUKskczxbPH7dQnlMjXX6+dybayyHvAf/TCgyMRlzf/B6QDhAEFOGes0pzRo3by4AbMA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/portfinder/-/portfinder-1.0.28.tgz"
  "version" "1.0.28"
  dependencies:
    "async" "^2.6.2"
    "debug" "^3.1.1"
    "mkdirp" "^0.5.5"

"posix-character-classes@^0.1.0":
  "integrity" "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="
  "resolved" "https://repo.huaweicloud.com/repository/npm/posix-character-classes/-/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"postcss-calc@^5.2.0":
  "integrity" "sha1-d7rnypKK2FcW4v2kLyYb98HWW14="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-calc/-/postcss-calc-5.3.1.tgz"
  "version" "5.3.1"
  dependencies:
    "postcss" "^5.0.2"
    "postcss-message-helpers" "^2.0.0"
    "reduce-css-calc" "^1.2.6"

"postcss-calc@^7.0.1":
  "integrity" "sha512-1tKHutbGtLtEZF6PT4JSihCHfIVldU72mZ8SdZHIYriIZ9fh9k9aWSppaT8rHsyI3dX+KSR+W+Ix9BMY3AODrg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-calc/-/postcss-calc-7.0.5.tgz"
  "version" "7.0.5"
  dependencies:
    "postcss" "^7.0.27"
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.0.2"

"postcss-colormin@^2.1.8":
  "integrity" "sha1-ZjFBfV8OkJo9fsJrJMio0eT5bks="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-colormin/-/postcss-colormin-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "colormin" "^1.0.5"
    "postcss" "^5.0.13"
    "postcss-value-parser" "^3.2.3"

"postcss-colormin@^4.0.3":
  "integrity" "sha512-WyQFAdDZpExQh32j0U0feWisZ0dmOtPl44qYmJKkq9xFWY3p+4qnRzCHeNrkeRhwPHz9bQ3mo0/yVkaply0MNw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-colormin/-/postcss-colormin-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "color" "^3.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-convert-values@^2.3.4":
  "integrity" "sha1-u9hZPFwf0uPRwyK7kl3K6Nrk1i0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-convert-values/-/postcss-convert-values-2.6.1.tgz"
  "version" "2.6.1"
  dependencies:
    "postcss" "^5.0.11"
    "postcss-value-parser" "^3.1.2"

"postcss-convert-values@^4.0.1":
  "integrity" "sha512-Kisdo1y77KUC0Jmn0OXU/COOJbzM8cImvw1ZFsBgBgMgb1iL23Zs/LXRe3r+EZqM3vGYKdQ2YJVQ5VkJI+zEJQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-convert-values/-/postcss-convert-values-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-discard-comments@^2.0.4":
  "integrity" "sha1-vv6J+v1bPazlzM5Rt2uBUUvgDj0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-discard-comments/-/postcss-discard-comments-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "postcss" "^5.0.14"

"postcss-discard-comments@^4.0.2":
  "integrity" "sha512-RJutN259iuRf3IW7GZyLM5Sw4GLTOH8FmsXBnv8Ab/Tc2k4SR4qbV4DNbyyY4+Sjo362SyDmW2DQ7lBSChrpkg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-discard-comments/-/postcss-discard-comments-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-duplicates@^2.0.1":
  "integrity" "sha1-uavye4isGIFYpesSq8riAmO5GTI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-discard-duplicates/-/postcss-discard-duplicates-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "postcss" "^5.0.4"

"postcss-discard-duplicates@^4.0.2":
  "integrity" "sha512-ZNQfR1gPNAiXZhgENFfEglF93pciw0WxMkJeVmw8eF+JZBbMD7jp6C67GqJAXVZP2BWbOztKfbsdmMp/k8c6oQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-discard-duplicates/-/postcss-discard-duplicates-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-empty@^2.0.1":
  "integrity" "sha1-0rS9nVztXr2Nyt52QMfXzX9PkrU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-discard-empty/-/postcss-discard-empty-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "postcss" "^5.0.14"

"postcss-discard-empty@^4.0.1":
  "integrity" "sha512-B9miTzbznhDjTfjvipfHoqbWKwd0Mj+/fL5s1QOz06wufguil+Xheo4XpOnc4NqKYBCNqqEzgPv2aPBIJLox0w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-discard-empty/-/postcss-discard-empty-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-overridden@^0.1.1":
  "integrity" "sha1-ix6vVU9ob7KIzYdMVWZ7CqNmjVg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-discard-overridden/-/postcss-discard-overridden-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "postcss" "^5.0.16"

"postcss-discard-overridden@^4.0.1":
  "integrity" "sha512-IYY2bEDD7g1XM1IDEsUT4//iEYCxAmP5oDSFMVU/JVvT7gh+l4fmjciLqGgwjdWpQIdb0Che2VX00QObS5+cTg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-discard-overridden/-/postcss-discard-overridden-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-unused@^2.2.1":
  "integrity" "sha1-vOMLLMWR/8Y0Mitfs0ZLbZNPRDM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-discard-unused/-/postcss-discard-unused-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "postcss" "^5.0.14"
    "uniqs" "^2.0.0"

"postcss-filter-plugins@^2.0.0":
  "integrity" "sha512-T53GVFsdinJhgwm7rg1BzbeBRomOg9y5MBVhGcsV0CxurUdVj1UlPdKtn7aqYA/c/QVkzKMjq2bSV5dKG5+AwQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-filter-plugins/-/postcss-filter-plugins-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "postcss" "^5.0.4"

"postcss-import@^11.0.0":
  "integrity" "sha512-5l327iI75POonjxkXgdRCUS+AlzAdBx4pOvMEhTKTCjb1p8IEeVR9yx3cPbmN7LIWJLbfnIXxAhoB4jpD0c/Cw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-import/-/postcss-import-11.1.0.tgz"
  "version" "11.1.0"
  dependencies:
    "postcss" "^6.0.1"
    "postcss-value-parser" "^3.2.3"
    "read-cache" "^1.0.0"
    "resolve" "^1.1.7"

"postcss-load-config@^1.1.0":
  "integrity" "sha1-U56a/J3chiASHr+djDZz4M5Q0oo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-load-config/-/postcss-load-config-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cosmiconfig" "^2.1.0"
    "object-assign" "^4.1.0"
    "postcss-load-options" "^1.2.0"
    "postcss-load-plugins" "^2.3.0"

"postcss-load-config@^2.0.0":
  "integrity" "sha512-/rDeGV6vMUo3mwJZmeHfEDvwnTKKqQ0S7OHUi/kJvvtx3aWtyWG2/0ZWnzCt2keEclwN6Tf0DST2v9kITdOKYw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-load-config/-/postcss-load-config-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "cosmiconfig" "^5.0.0"
    "import-cwd" "^2.0.0"

"postcss-load-options@^1.2.0":
  "integrity" "sha1-sJixVZ3awt8EvAuzdfmaXP4rbYw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-load-options/-/postcss-load-options-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cosmiconfig" "^2.1.0"
    "object-assign" "^4.1.0"

"postcss-load-plugins@^2.3.0":
  "integrity" "sha1-dFdoEWWZrKLwCfrUJrABdQSdjZI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-load-plugins/-/postcss-load-plugins-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "cosmiconfig" "^2.1.1"
    "object-assign" "^4.1.0"

"postcss-loader@^2.0.8":
  "integrity" "sha512-hgiWSc13xVQAq25cVw80CH0l49ZKlAnU1hKPOdRrNj89bokRr/bZF2nT+hebPPF9c9xs8c3gw3Fr2nxtmXYnNg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-loader/-/postcss-loader-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "loader-utils" "^1.1.0"
    "postcss" "^6.0.0"
    "postcss-load-config" "^2.0.0"
    "schema-utils" "^0.4.0"

"postcss-merge-idents@^2.1.5":
  "integrity" "sha1-TFUwMTwI4dWzu/PSu8dH4njuonA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-merge-idents/-/postcss-merge-idents-2.1.7.tgz"
  "version" "2.1.7"
  dependencies:
    "has" "^1.0.1"
    "postcss" "^5.0.10"
    "postcss-value-parser" "^3.1.1"

"postcss-merge-longhand@^2.0.1":
  "integrity" "sha1-I9kM0Sewp3mUkVMyc5A0oaTz1lg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-merge-longhand/-/postcss-merge-longhand-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "postcss" "^5.0.4"

"postcss-merge-longhand@^4.0.11":
  "integrity" "sha512-alx/zmoeXvJjp7L4mxEMjh8lxVlDFX1gqWHzaaQewwMZiVhLo42TEClKaeHbRf6J7j82ZOdTJ808RtN0ZOZwvw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-merge-longhand/-/postcss-merge-longhand-4.0.11.tgz"
  "version" "4.0.11"
  dependencies:
    "css-color-names" "0.0.4"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "stylehacks" "^4.0.0"

"postcss-merge-rules@^2.0.3":
  "integrity" "sha1-0d9d+qexrMO+VT8OnhDofGG19yE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-merge-rules/-/postcss-merge-rules-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "browserslist" "^1.5.2"
    "caniuse-api" "^1.5.2"
    "postcss" "^5.0.4"
    "postcss-selector-parser" "^2.2.2"
    "vendors" "^1.0.0"

"postcss-merge-rules@^4.0.3":
  "integrity" "sha512-U7e3r1SbvYzO0Jr3UT/zKBVgYYyhAz0aitvGIYOYK5CPmkNih+WDSsS5tvPrJ8YMQYlEMvsZIiqmn7HdFUaeEQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-merge-rules/-/postcss-merge-rules-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-api" "^3.0.0"
    "cssnano-util-same-parent" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"
    "vendors" "^1.0.0"

"postcss-message-helpers@^2.0.0":
  "integrity" "sha1-pPL0+rbk/gAvCu0ABHjN9S+bpg4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-message-helpers/-/postcss-message-helpers-2.0.0.tgz"
  "version" "2.0.0"

"postcss-minify-font-values@^1.0.2":
  "integrity" "sha1-S1jttWZB66fIR0qzUmyv17vey2k="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-minify-font-values/-/postcss-minify-font-values-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "object-assign" "^4.0.1"
    "postcss" "^5.0.4"
    "postcss-value-parser" "^3.0.2"

"postcss-minify-font-values@^4.0.2":
  "integrity" "sha512-j85oO6OnRU9zPf04+PZv1LYIYOprWm6IA6zkXkrJXyRveDEuQggG6tvoy8ir8ZwjLxLuGfNkCZEQG7zan+Hbtg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-minify-font-values/-/postcss-minify-font-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-minify-gradients@^1.0.1":
  "integrity" "sha1-Xb2hE3NwP4PPtKPqOIHY11/15uE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-minify-gradients/-/postcss-minify-gradients-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "postcss" "^5.0.12"
    "postcss-value-parser" "^3.3.0"

"postcss-minify-gradients@^4.0.2":
  "integrity" "sha512-qKPfwlONdcf/AndP1U8SJ/uzIJtowHlMaSioKzebAXSG4iJthlWC9iSWznQcX4f66gIWX44RSA841HTHj3wK+Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-minify-gradients/-/postcss-minify-gradients-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "is-color-stop" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-minify-params@^1.0.4":
  "integrity" "sha1-rSzgcTc7lDs9kwo/pZo1jCjW8fM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-minify-params/-/postcss-minify-params-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "alphanum-sort" "^1.0.1"
    "postcss" "^5.0.2"
    "postcss-value-parser" "^3.0.2"
    "uniqs" "^2.0.0"

"postcss-minify-params@^4.0.2":
  "integrity" "sha512-G7eWyzEx0xL4/wiBBJxJOz48zAKV2WG3iZOqVhPet/9geefm/Px5uo1fzlHu+DOjT+m0Mmiz3jkQzVHe6wxAWg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-minify-params/-/postcss-minify-params-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "browserslist" "^4.0.0"
    "cssnano-util-get-arguments" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "uniqs" "^2.0.0"

"postcss-minify-selectors@^2.0.4":
  "integrity" "sha1-ssapjAByz5G5MtGkllCBFDEXNb8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-minify-selectors/-/postcss-minify-selectors-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "alphanum-sort" "^1.0.2"
    "has" "^1.0.1"
    "postcss" "^5.0.14"
    "postcss-selector-parser" "^2.0.0"

"postcss-minify-selectors@^4.0.2":
  "integrity" "sha512-D5S1iViljXBj9kflQo4YutWnJmwm8VvIsU1GeXJGiG9j8CIg9zs4voPMdQDUmIxetUOh60VilsNzCiAFTOqu3g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-minify-selectors/-/postcss-minify-selectors-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"

"postcss-modules-extract-imports@^1.2.0":
  "integrity" "sha512-6jt9XZwUhwmRUhb/CkyJY020PYaPJsCyt3UjbaWo6XEbH/94Hmv6MP7fG2C5NDU/BcHzyGYxNtHvM+LTf9HrYw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-modules-extract-imports/-/postcss-modules-extract-imports-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "postcss" "^6.0.1"

"postcss-modules-local-by-default@^1.2.0":
  "integrity" "sha1-99gMOYxaOT+nlkRmvRlQCn1hwGk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-modules-local-by-default/-/postcss-modules-local-by-default-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "css-selector-tokenizer" "^0.7.0"
    "postcss" "^6.0.1"

"postcss-modules-scope@^1.1.0":
  "integrity" "sha1-1upkmUx5+XtipytCb75gVqGUu5A="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-modules-scope/-/postcss-modules-scope-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "css-selector-tokenizer" "^0.7.0"
    "postcss" "^6.0.1"

"postcss-modules-values@^1.3.0":
  "integrity" "sha1-7P+p1+GSUYOJ9CrQ6D9yrsRW6iA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-modules-values/-/postcss-modules-values-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "icss-replace-symbols" "^1.1.0"
    "postcss" "^6.0.1"

"postcss-normalize-charset@^1.1.0":
  "integrity" "sha1-757nEhLX/nWceO0WL2HtYrXLk/E="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-normalize-charset/-/postcss-normalize-charset-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "postcss" "^5.0.5"

"postcss-normalize-charset@^4.0.1":
  "integrity" "sha512-gMXCrrlWh6G27U0hF3vNvR3w8I1s2wOBILvA87iNXaPvSNo5uZAMYsZG7XjCUf1eVxuPfyL4TJ7++SGZLc9A3g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-normalize-charset/-/postcss-normalize-charset-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-normalize-display-values@^4.0.2":
  "integrity" "sha512-3F2jcsaMW7+VtRMAqf/3m4cPFhPD3EFRgNs18u+k3lTJJlVe7d0YPO+bnwqo2xg8YiRpDXJI2u8A0wqJxMsQuQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-normalize-display-values/-/postcss-normalize-display-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-positions@^4.0.2":
  "integrity" "sha512-Dlf3/9AxpxE+NF1fJxYDeggi5WwV35MXGFnnoccP/9qDtFrTArZ0D0R+iKcg5WsUd8nUYMIl8yXDCtcrT8JrdA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-normalize-positions/-/postcss-normalize-positions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-repeat-style@^4.0.2":
  "integrity" "sha512-qvigdYYMpSuoFs3Is/f5nHdRLJN/ITA7huIoCyqqENJe9PvPmLhNLMu7QTjPdtnVf6OcYYO5SHonx4+fbJE1+Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-string@^4.0.2":
  "integrity" "sha512-RrERod97Dnwqq49WNz8qo66ps0swYZDSb6rM57kN2J+aoyEAJfZ6bMx0sx/F9TIEX0xthPGCmeyiam/jXif0eA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-normalize-string/-/postcss-normalize-string-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-timing-functions@^4.0.2":
  "integrity" "sha512-acwJY95edP762e++00Ehq9L4sZCEcOPyaHwoaFOhIwWCDfik6YvqsYNxckee65JHLKzuNSSmAdxwD2Cud1Z54A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-unicode@^4.0.1":
  "integrity" "sha512-od18Uq2wCYn+vZ/qCOeutvHjB5jm57ToxRaMeNuf0nWVHaP9Hua56QyMF6fs/4FSUnVIw0CBPsU0K4LnBPwYwg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-normalize-unicode/-/postcss-normalize-unicode-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "browserslist" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-url@^3.0.7":
  "integrity" "sha1-EI90s/L82viRov+j6kWSJ5/HgiI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-normalize-url/-/postcss-normalize-url-3.0.8.tgz"
  "version" "3.0.8"
  dependencies:
    "is-absolute-url" "^2.0.0"
    "normalize-url" "^1.4.0"
    "postcss" "^5.0.14"
    "postcss-value-parser" "^3.2.3"

"postcss-normalize-url@^4.0.1":
  "integrity" "sha512-p5oVaF4+IHwu7VpMan/SSpmpYxcJMtkGppYf0VbdH5B6hN8YNmVyJLuY9FmLQTzY3fag5ESUUHDqM+heid0UVA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-normalize-url/-/postcss-normalize-url-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-absolute-url" "^2.0.0"
    "normalize-url" "^3.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-whitespace@^4.0.2":
  "integrity" "sha512-tO8QIgrsI3p95r8fyqKV+ufKlSHh9hMJqACqbv2XknufqEDhDvbguXGBBqxw9nsQoXWf0qOqppziKJKHMD4GtA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-normalize-whitespace/-/postcss-normalize-whitespace-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-ordered-values@^2.1.0":
  "integrity" "sha1-7sbCpntsQSqNsgQud/6NpD+VwR0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-ordered-values/-/postcss-ordered-values-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "postcss" "^5.0.4"
    "postcss-value-parser" "^3.0.1"

"postcss-ordered-values@^4.1.2":
  "integrity" "sha512-2fCObh5UanxvSxeXrtLtlwVThBvHn6MQcu4ksNT2tsaV2Fg76R2CV98W7wNSlX+5/pFwEyaDwKLLoEV7uRybAw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-ordered-values/-/postcss-ordered-values-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-reduce-idents@^2.2.2":
  "integrity" "sha1-wsbSDMlYKE9qv75j92Cb9AkFmtM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-reduce-idents/-/postcss-reduce-idents-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "postcss" "^5.0.4"
    "postcss-value-parser" "^3.0.2"

"postcss-reduce-initial@^1.0.0":
  "integrity" "sha1-aPgGlfBF0IJjqHmtJA343WT2ROo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-reduce-initial/-/postcss-reduce-initial-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "postcss" "^5.0.4"

"postcss-reduce-initial@^4.0.3":
  "integrity" "sha512-gKWmR5aUulSjbzOfD9AlJiHCGH6AEVLaM0AV+aSioxUDd16qXP1PCh8d1/BGVvpdWn8k/HiK7n6TjeoXN1F7DA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-reduce-initial/-/postcss-reduce-initial-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-api" "^3.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"

"postcss-reduce-transforms@^1.0.3":
  "integrity" "sha1-/3b02CEkN7McKYpC0uFEQCV3GuE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-reduce-transforms/-/postcss-reduce-transforms-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has" "^1.0.1"
    "postcss" "^5.0.8"
    "postcss-value-parser" "^3.0.1"

"postcss-reduce-transforms@^4.0.2":
  "integrity" "sha512-EEVig1Q2QJ4ELpJXMZR8Vt5DQx8/mo+dGWSR7vWXqcob2gQLyQGsionYcGKATXvQzMPn6DSN1vTN7yFximdIAg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-reduce-transforms/-/postcss-reduce-transforms-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-selector-parser@^2.0.0", "postcss-selector-parser@^2.2.2":
  "integrity" "sha1-+UN3iGBsPJrO4W/+jYsWKX8nu5A="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-selector-parser/-/postcss-selector-parser-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "flatten" "^1.0.2"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss-selector-parser@^3.0.0":
  "integrity" "sha512-h7fJ/5uWuRVyOtkO45pnt1Ih40CEleeyCHzipqAZO2e5H20g25Y48uYnFUiShvY4rZWNJ/Bib/KVPmanaCtOhA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-selector-parser/-/postcss-selector-parser-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "dot-prop" "^5.2.0"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss-selector-parser@^6.0.2":
  "integrity" "sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-selector-parser/-/postcss-selector-parser-6.0.10.tgz"
  "version" "6.0.10"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-svgo@^2.1.1":
  "integrity" "sha1-tt8YqmE7Zm4TPwittSGcJoSsEI0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-svgo/-/postcss-svgo-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "is-svg" "^2.0.0"
    "postcss" "^5.0.14"
    "postcss-value-parser" "^3.2.3"
    "svgo" "^0.7.0"

"postcss-svgo@^4.0.3":
  "integrity" "sha512-NoRbrcMWTtUghzuKSoIm6XV+sJdvZ7GZSc3wdBN0W19FTtp2ko8NqLsgoh/m9CzNhU3KLPvQmjIwtaNFkaFTvw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-svgo/-/postcss-svgo-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "svgo" "^1.0.0"

"postcss-unique-selectors@^2.0.2":
  "integrity" "sha1-mB1X0p3csz57Hf4f1DuGSfkzyh0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-unique-selectors/-/postcss-unique-selectors-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "alphanum-sort" "^1.0.1"
    "postcss" "^5.0.4"
    "uniqs" "^2.0.0"

"postcss-unique-selectors@^4.0.1":
  "integrity" "sha512-+JanVaryLo9QwZjKrmJgkI4Fn8SBgRO6WXQBJi7KiAVPlmxikB5Jzc4EvXMT2H0/m0RjrVVm9rGNhZddm/8Spg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-unique-selectors/-/postcss-unique-selectors-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "postcss" "^7.0.0"
    "uniqs" "^2.0.0"

"postcss-url@^7.2.1":
  "integrity" "sha512-QMV5mA+pCYZQcUEPQkmor9vcPQ2MT+Ipuu8qdi1gVxbNiIiErEGft+eny1ak19qALoBkccS5AHaCaCDzh7b9MA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-url/-/postcss-url-7.3.2.tgz"
  "version" "7.3.2"
  dependencies:
    "mime" "^1.4.1"
    "minimatch" "^3.0.4"
    "mkdirp" "^0.5.0"
    "postcss" "^6.0.1"
    "xxhashjs" "^0.2.1"

"postcss-value-parser@^3.0.0", "postcss-value-parser@^3.0.1", "postcss-value-parser@^3.0.2", "postcss-value-parser@^3.1.1", "postcss-value-parser@^3.1.2", "postcss-value-parser@^3.2.3", "postcss-value-parser@^3.3.0":
  "integrity" "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz"
  "version" "3.3.1"

"postcss-value-parser@^4.0.2":
  "integrity" "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  "version" "4.2.0"

"postcss-zindex@^2.0.1":
  "integrity" "sha1-0hCd3AVbka9n/EyzsCWUZjnSryI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss-zindex/-/postcss-zindex-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "has" "^1.0.1"
    "postcss" "^5.0.4"
    "uniqs" "^2.0.0"

"postcss@^5.0.10":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.11":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.12":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.13":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.14", "postcss@^5.2.16":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.16":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.2":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.4":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.5":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.6":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^5.0.8":
  "integrity" "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^6.0.0", "postcss@^6.0.1", "postcss@^6.0.17", "postcss@^6.0.8":
  "integrity" "sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss/-/postcss-6.0.23.tgz"
  "version" "6.0.23"
  dependencies:
    "chalk" "^2.4.1"
    "source-map" "^0.6.1"
    "supports-color" "^5.4.0"

"postcss@^7.0.0", "postcss@^7.0.27":
  "integrity" "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss/-/postcss-7.0.39.tgz"
  "version" "7.0.39"
  dependencies:
    "picocolors" "^0.2.1"
    "source-map" "^0.6.1"

"postcss@^7.0.1":
  "integrity" "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/postcss/-/postcss-7.0.39.tgz"
  "version" "7.0.39"
  dependencies:
    "picocolors" "^0.2.1"
    "source-map" "^0.6.1"

"prelude-ls@~1.1.2":
  "integrity" "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/prelude-ls/-/prelude-ls-1.1.2.tgz"
  "version" "1.1.2"

"prepend-http@^1.0.0":
  "integrity" "sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/prepend-http/-/prepend-http-1.0.4.tgz"
  "version" "1.0.4"

"prettier@^1.7.0":
  "integrity" "sha512-s7PoyDv/II1ObgQunCbB9PdLmUcBZcnWOcxDh7O0N/UwDEsHyqkW+Qh28jW+mVuCdx7gLB0BotYI1Y6uI9iyew=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/prettier/-/prettier-1.19.1.tgz"
  "version" "1.19.1"

"prettier@2.5.1":
  "integrity" "sha512-vBZcPRUR5MZJwoyi3ZoyQlc1rXeEck8KgeC9AwwOn+exuxLxq5toTRDTSaVrXHxelDMHy9zlicw8u66yxoSUFg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/prettier/-/prettier-2.5.1.tgz"
  "version" "2.5.1"

"pretty-error@^2.0.2":
  "integrity" "sha512-EY5oDzmsX5wvuynAByrmY0P0hcp+QpnAKbJng2A2MPjVKXCxrDSUkzghVJ4ZGPIv+JC4gX8fPUWscC0RtjsWGw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pretty-error/-/pretty-error-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "lodash" "^4.17.20"
    "renderkid" "^2.0.4"

"private@^0.1.6", "private@^0.1.8":
  "integrity" "sha512-VvivMrbvd2nKkiG38qjULzlc+4Vx4wm/whI9pQD35YrARNnhxeiRktSOhSukRLFNlzg6Br/cJPet5J/u19r/mg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/private/-/private-0.1.8.tgz"
  "version" "0.1.8"

"process-nextick-args@~2.0.0":
  "integrity" "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"process@^0.11.10":
  "integrity" "sha1-czIwDoQBYb2j5podHZGn1LwW8YI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/process/-/process-0.11.10.tgz"
  "version" "0.11.10"

"progress@^2.0.0":
  "integrity" "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/progress/-/progress-2.0.3.tgz"
  "version" "2.0.3"

"promise-inflight@^1.0.1":
  "integrity" "sha1-mEcocL8igTL8vdhoEputEsPAKeM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/promise-inflight/-/promise-inflight-1.0.1.tgz"
  "version" "1.0.1"

"proxy-addr@~2.0.7":
  "integrity" "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/proxy-addr/-/proxy-addr-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "forwarded" "0.2.0"
    "ipaddr.js" "1.9.1"

"prr@~1.0.1":
  "integrity" "sha1-0/wRS6BplaRexok/SEzrHXj19HY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/prr/-/prr-1.0.1.tgz"
  "version" "1.0.1"

"pseudomap@^1.0.2":
  "integrity" "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pseudomap/-/pseudomap-1.0.2.tgz"
  "version" "1.0.2"

"public-encrypt@^4.0.0":
  "integrity" "sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/public-encrypt/-/public-encrypt-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "browserify-rsa" "^4.0.0"
    "create-hash" "^1.1.0"
    "parse-asn1" "^5.0.0"
    "randombytes" "^2.0.1"
    "safe-buffer" "^5.1.2"

"pump@^2.0.0", "pump@^2.0.1":
  "integrity" "sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pump/-/pump-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pumpify@^1.3.3":
  "integrity" "sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/pumpify/-/pumpify-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "duplexify" "^3.6.0"
    "inherits" "^2.0.3"
    "pump" "^2.0.0"

"punycode@^1.2.4":
  "integrity" "sha1-wNWmOycYgArY4esPpSachN1BhF4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/punycode/-/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@^2.1.0":
  "integrity" "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/punycode/-/punycode-2.1.1.tgz"
  "version" "2.1.1"

"punycode@1.3.2":
  "integrity" "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/punycode/-/punycode-1.3.2.tgz"
  "version" "1.3.2"

"q@^1.1.2":
  "integrity" "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/q/-/q-1.5.1.tgz"
  "version" "1.5.1"

"qs@6.9.7":
  "integrity" "sha512-IhMFgUmuNpyRfxA90umL7ByLlgRXu6tIfKPpF5TmcfRLlLCckfP/g3IQmju6jjpu+Hh8rA+2p6A27ZSPOOHdKw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/qs/-/qs-6.9.7.tgz"
  "version" "6.9.7"

"query-string@^4.1.0":
  "integrity" "sha1-u7aTucqRXCMlFbIosaArYJBD2+s="
  "resolved" "https://repo.huaweicloud.com/repository/npm/query-string/-/query-string-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "object-assign" "^4.1.0"
    "strict-uri-encode" "^1.0.0"

"querystring-es3@^0.2.0":
  "integrity" "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/querystring-es3/-/querystring-es3-0.2.1.tgz"
  "version" "0.2.1"

"querystring@0.2.0":
  "integrity" "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/querystring/-/querystring-0.2.0.tgz"
  "version" "0.2.0"

"querystringify@^2.1.1":
  "integrity" "sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/querystringify/-/querystringify-2.2.0.tgz"
  "version" "2.2.0"

"randombytes@^2.0.0", "randombytes@^2.0.1", "randombytes@^2.0.5":
  "integrity" "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/randombytes/-/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"randomfill@^1.0.3":
  "integrity" "sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/randomfill/-/randomfill-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "randombytes" "^2.0.5"
    "safe-buffer" "^5.1.0"

"range-parser@^1.0.3", "range-parser@~1.2.1":
  "integrity" "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/range-parser/-/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"raw-body@2.4.3":
  "integrity" "sha512-UlTNLIcu0uzb4D2f4WltY6cVjLi+/jEN4lgEUj3E04tpMDpUlkBo/eSn6zou9hum2VMNpCCUone0O0WeJim07g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/raw-body/-/raw-body-2.4.3.tgz"
  "version" "2.4.3"
  dependencies:
    "bytes" "3.1.2"
    "http-errors" "1.8.1"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"read-cache@^1.0.0":
  "integrity" "sha1-5mTvMRYRZsl1HNvo28+GtftY93Q="
  "resolved" "https://repo.huaweicloud.com/repository/npm/read-cache/-/read-cache-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "pify" "^2.3.0"

"read-pkg-up@^1.0.1":
  "integrity" "sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/read-pkg-up/-/read-pkg-up-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "find-up" "^1.0.0"
    "read-pkg" "^1.0.0"

"read-pkg-up@^2.0.0":
  "integrity" "sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/read-pkg-up/-/read-pkg-up-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "^2.0.0"
    "read-pkg" "^2.0.0"

"read-pkg@^1.0.0":
  "integrity" "sha1-9f+qXs0pyzHAR0vKfXVra7KePyg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/read-pkg/-/read-pkg-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "load-json-file" "^1.0.0"
    "normalize-package-data" "^2.3.2"
    "path-type" "^1.0.0"

"read-pkg@^2.0.0":
  "integrity" "sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/read-pkg/-/read-pkg-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "load-json-file" "^2.0.0"
    "normalize-package-data" "^2.3.2"
    "path-type" "^2.0.0"

"readable-stream@^2.0.0", "readable-stream@^2.0.1", "readable-stream@^2.0.2", "readable-stream@^2.1.5", "readable-stream@^2.2.2", "readable-stream@^2.3.3", "readable-stream@^2.3.6", "readable-stream@~2.3.6", "readable-stream@1 || 2":
  "integrity" "sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/readable-stream/-/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.6":
  "integrity" "sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/readable-stream/-/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^3.1.1":
  "integrity" "sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/readable-stream/-/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^3.6.0":
  "integrity" "sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/readable-stream/-/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readdirp@^2.2.1":
  "integrity" "sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/readdirp/-/readdirp-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "graceful-fs" "^4.1.11"
    "micromatch" "^3.1.10"
    "readable-stream" "^2.0.2"

"readdirp@~3.6.0":
  "integrity" "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/readdirp/-/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"rechoir@^0.6.2":
  "integrity" "sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q="
  "resolved" "https://repo.huaweicloud.com/repository/npm/rechoir/-/rechoir-0.6.2.tgz"
  "version" "0.6.2"
  dependencies:
    "resolve" "^1.1.6"

"redent@^1.0.0":
  "integrity" "sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94="
  "resolved" "https://repo.huaweicloud.com/repository/npm/redent/-/redent-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "indent-string" "^2.1.0"
    "strip-indent" "^1.0.1"

"reduce-css-calc@^1.2.6":
  "integrity" "sha1-dHyRTgSWFKTJz7umKYca0dKSdxY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/reduce-css-calc/-/reduce-css-calc-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "balanced-match" "^0.4.2"
    "math-expression-evaluator" "^1.2.14"
    "reduce-function-call" "^1.0.1"

"reduce-function-call@^1.0.1":
  "integrity" "sha512-Hl/tuV2VDgWgCSEeWMLwxLZqX7OK59eU1guxXsRKTAyeYimivsKdtcV4fu3r710tpG5GmDKDhQ0HSZLExnNmyQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/reduce-function-call/-/reduce-function-call-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "balanced-match" "^1.0.0"

"regenerate@^1.2.1":
  "integrity" "sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/regenerate/-/regenerate-1.4.2.tgz"
  "version" "1.4.2"

"regenerator-runtime@^0.10.5":
  "integrity" "sha1-M2w+/BIgrc7dosn6tntaeVWjNlg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/regenerator-runtime/-/regenerator-runtime-0.10.5.tgz"
  "version" "0.10.5"

"regenerator-runtime@^0.11.0":
  "integrity" "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz"
  "version" "0.11.1"

"regenerator-transform@^0.10.0":
  "integrity" "sha512-PJepbvDbuK1xgIgnau7Y90cwaAmO/LCLMI2mPvaXq2heGMR3aWW5/BQvYrhJ8jgmQjXewXvBjzfqKcVOmhjZ6Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/regenerator-transform/-/regenerator-transform-0.10.1.tgz"
  "version" "0.10.1"
  dependencies:
    "babel-runtime" "^6.18.0"
    "babel-types" "^6.19.0"
    "private" "^0.1.6"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "integrity" "sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/regex-not/-/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"regexp.prototype.flags@^1.2.0":
  "integrity" "sha512-pMR7hBVUUGI7PMA37m2ofIdQCsomVnas+Jn5UPGAHQ+/LlwKm/aTLJHdasmHRzlfeZwHiAOaRSo2rbBDm3nNUQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/regexp.prototype.flags/-/regexp.prototype.flags-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"regexpp@^1.0.1":
  "integrity" "sha512-LOPw8FpgdQF9etWMaAfG/WRthIdXJGYp4mJ2Jgn/2lpkbod9jPn0t9UqN7AxBOKNfzRbYyVfgc7Vk4t/MpnXgw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/regexpp/-/regexpp-1.1.0.tgz"
  "version" "1.1.0"

"regexpu-core@^2.0.0":
  "integrity" "sha1-SdA4g3uNz4v6W5pCE5k45uoq4kA="
  "resolved" "https://repo.huaweicloud.com/repository/npm/regexpu-core/-/regexpu-core-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "regenerate" "^1.2.1"
    "regjsgen" "^0.2.0"
    "regjsparser" "^0.1.4"

"regjsgen@^0.2.0":
  "integrity" "sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/regjsgen/-/regjsgen-0.2.0.tgz"
  "version" "0.2.0"

"regjsparser@^0.1.4":
  "integrity" "sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/regjsparser/-/regjsparser-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "jsesc" "~0.5.0"

"relateurl@0.2.x":
  "integrity" "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/relateurl/-/relateurl-0.2.7.tgz"
  "version" "0.2.7"

"remove-trailing-separator@^1.0.1":
  "integrity" "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz"
  "version" "1.1.0"

"renderkid@^2.0.4":
  "integrity" "sha512-oCcFyxaMrKsKcTY59qnCAtmDVSLfPbrv6A3tVbPdFMMrv5jaK10V6m40cKsoPNhAqN6rmHW9sswW4o3ruSrwUQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/renderkid/-/renderkid-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "css-select" "^4.1.3"
    "dom-converter" "^0.2.0"
    "htmlparser2" "^6.1.0"
    "lodash" "^4.17.21"
    "strip-ansi" "^3.0.1"

"repeat-element@^1.1.2":
  "integrity" "sha512-LFiNfRcSu7KK3evMyYOuCzv3L10TW7yC1G2/+StMjK8Y6Vqd2MG7r/Qjw4ghtuCOjFvlnms/iMmLqpvW/ES/WQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/repeat-element/-/repeat-element-1.1.4.tgz"
  "version" "1.1.4"

"repeat-string@^1.5.2", "repeat-string@^1.6.1":
  "integrity" "sha1-jcrkcOHIirwtYA//Sndihtp15jc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/repeat-string/-/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"repeating@^2.0.0":
  "integrity" "sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/repeating/-/repeating-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-finite" "^1.0.0"

"require-directory@^2.1.1":
  "integrity" "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="
  "resolved" "https://repo.huaweicloud.com/repository/npm/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^1.1.0":
  "integrity" "sha1-UpyczvJzgK3+yaL5ZbZJu+5jZBg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/require-from-string/-/require-from-string-1.2.1.tgz"
  "version" "1.2.1"

"require-main-filename@^1.0.1":
  "integrity" "sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/require-main-filename/-/require-main-filename-1.0.1.tgz"
  "version" "1.0.1"

"require-uncached@^1.0.3":
  "integrity" "sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/require-uncached/-/require-uncached-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "caller-path" "^0.1.0"
    "resolve-from" "^1.0.0"

"requires-port@^1.0.0":
  "integrity" "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/requires-port/-/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"resolve-cwd@^2.0.0":
  "integrity" "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/resolve-cwd/-/resolve-cwd-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "resolve-from" "^3.0.0"

"resolve-from@^1.0.0":
  "integrity" "sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/resolve-from/-/resolve-from-1.0.1.tgz"
  "version" "1.0.1"

"resolve-from@^3.0.0":
  "integrity" "sha1-six699nWiBvItuZTM17rywoYh0g="
  "resolved" "https://repo.huaweicloud.com/repository/npm/resolve-from/-/resolve-from-3.0.0.tgz"
  "version" "3.0.0"

"resolve-url@^0.2.1":
  "integrity" "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/resolve-url/-/resolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@^1.1.6", "resolve@^1.1.7", "resolve@^1.10.0", "resolve@^1.20.0", "resolve@^1.3.3", "resolve@^1.4.0":
  "integrity" "sha512-Hhtrw0nLeSrFQ7phPp4OOcVjLPIeMnRlr5mcnVuMe7M/7eBn98A3hmFRLoFo3DLZkivSYwhRUJTyPyWAk56WLw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/resolve/-/resolve-1.22.0.tgz"
  "version" "1.22.0"
  dependencies:
    "is-core-module" "^2.8.1"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"restore-cursor@^2.0.0":
  "integrity" "sha1-n37ih/gv0ybU/RYpI9YhKe7g368="
  "resolved" "https://repo.huaweicloud.com/repository/npm/restore-cursor/-/restore-cursor-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "onetime" "^2.0.0"
    "signal-exit" "^3.0.2"

"ret@~0.1.10":
  "integrity" "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ret/-/ret-0.1.15.tgz"
  "version" "0.1.15"

"rgb-regex@^1.0.1":
  "integrity" "sha1-wODWiC3w4jviVKR16O3UGRX+rrE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/rgb-regex/-/rgb-regex-1.0.1.tgz"
  "version" "1.0.1"

"rgba-regex@^1.0.0":
  "integrity" "sha1-QzdOLiyglosO8VI0YLfXMP8i7rM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/rgba-regex/-/rgba-regex-1.0.0.tgz"
  "version" "1.0.0"

"right-align@^0.1.1":
  "integrity" "sha1-YTObci/mo1FWiSENJOFMlhSGE+8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/right-align/-/right-align-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "align-text" "^0.1.1"

"rimraf@^2.2.8", "rimraf@^2.5.4", "rimraf@^2.6.0", "rimraf@^2.6.1", "rimraf@^2.6.2":
  "integrity" "sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/rimraf/-/rimraf-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "glob" "^7.1.3"

"rimraf@~2.6.2":
  "integrity" "sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/rimraf/-/rimraf-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "glob" "^7.1.3"

"ripemd160@^2.0.0", "ripemd160@^2.0.1":
  "integrity" "sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ripemd160/-/ripemd160-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"

"run-async@^2.2.0":
  "integrity" "sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/run-async/-/run-async-2.4.1.tgz"
  "version" "2.4.1"

"run-queue@^1.0.0", "run-queue@^1.0.3":
  "integrity" "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec="
  "resolved" "https://repo.huaweicloud.com/repository/npm/run-queue/-/run-queue-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "aproba" "^1.1.1"

"rx-lite-aggregates@^4.0.8":
  "integrity" "sha1-dTuHqJoRyVRnxKwWJsTvxOBcZ74="
  "resolved" "https://repo.huaweicloud.com/repository/npm/rx-lite-aggregates/-/rx-lite-aggregates-4.0.8.tgz"
  "version" "4.0.8"
  dependencies:
    "rx-lite" "*"

"rx-lite@*", "rx-lite@^4.0.8":
  "integrity" "sha1-Cx4Rr4vESDbwSmQH6S2kJGe3lEQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/rx-lite/-/rx-lite-4.0.8.tgz"
  "version" "4.0.8"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.0", "safe-buffer@^5.1.1", "safe-buffer@^5.1.2", "safe-buffer@>=5.1.0", "safe-buffer@~5.1.0", "safe-buffer@~5.1.1", "safe-buffer@5.1.2":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@^5.2.0":
  "integrity" "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-buffer@5.2.1":
  "integrity" "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-regex@^1.1.0":
  "integrity" "sha1-QKNmnzsHfR6UPURinhV91IAjvy4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/safe-regex/-/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@^2.1.0", "safer-buffer@^2.1.2", "safer-buffer@>= 2.1.2 < 3":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sax@^1.2.4", "sax@~1.2.1", "sax@~1.2.4":
  "integrity" "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/sax/-/sax-1.2.4.tgz"
  "version" "1.2.4"

"schema-utils@^0.3.0":
  "integrity" "sha1-9YdyIs4+kx7a4DnxfrNxbnE3+M8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/schema-utils/-/schema-utils-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "ajv" "^5.0.0"

"schema-utils@^0.4.0":
  "integrity" "sha512-v/iwU6wvwGK8HbU9yi3/nhGzP0yGSuhQMzL6ySiec1FSrZZDkhm4noOSWzrNFo/jEc+SJY6jRTwuwbSXJPDUnQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/schema-utils/-/schema-utils-0.4.7.tgz"
  "version" "0.4.7"
  dependencies:
    "ajv" "^6.1.0"
    "ajv-keywords" "^3.1.0"

"schema-utils@^0.4.5":
  "integrity" "sha512-v/iwU6wvwGK8HbU9yi3/nhGzP0yGSuhQMzL6ySiec1FSrZZDkhm4noOSWzrNFo/jEc+SJY6jRTwuwbSXJPDUnQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/schema-utils/-/schema-utils-0.4.7.tgz"
  "version" "0.4.7"
  dependencies:
    "ajv" "^6.1.0"
    "ajv-keywords" "^3.1.0"

"select-hose@^2.0.0":
  "integrity" "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/select-hose/-/select-hose-2.0.0.tgz"
  "version" "2.0.0"

"selfsigned@^1.9.1":
  "integrity" "sha512-lkjaiAye+wBZDCBsu5BGi0XiLRxeUlsGod5ZP924CRSEoGuZAw/f7y9RKu28rwTfiHVhdavhB0qH0INV6P1lEA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/selfsigned/-/selfsigned-1.10.14.tgz"
  "version" "1.10.14"
  dependencies:
    "node-forge" "^0.10.0"

"semver@^5.3.0", "semver@^5.5.0", "semver@^5.6.0", "semver@2 || 3 || 4 || 5":
  "integrity" "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^6.3.0":
  "integrity" "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/semver/-/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@5.3.0":
  "integrity" "sha1-myzl094C0XxgEq0yaqa00M9U+U8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/semver/-/semver-5.3.0.tgz"
  "version" "5.3.0"

"send@0.17.2":
  "integrity" "sha512-UJYB6wFSJE3G00nEivR5rgWp8c2xXvJ3OPWPhmuteU0IKj8nKbG3DrjiOmLwpnHGYWAVwA69zmTm++YG0Hmwww=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/send/-/send-0.17.2.tgz"
  "version" "0.17.2"
  dependencies:
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "destroy" "~1.0.4"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "1.8.1"
    "mime" "1.6.0"
    "ms" "2.1.3"
    "on-finished" "~2.3.0"
    "range-parser" "~1.2.1"
    "statuses" "~1.5.0"

"serialize-javascript@^1.4.0":
  "integrity" "sha512-0Vb/54WJ6k5v8sSWN09S0ora+Hnr+cX40r9F170nT+mSkaxltoE/7R3OrIdBSUv1OoiobH1QoWQbCnAO+e8J1A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/serialize-javascript/-/serialize-javascript-1.9.1.tgz"
  "version" "1.9.1"

"serve-index@^1.9.1":
  "integrity" "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/serve-index/-/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "accepts" "~1.3.4"
    "batch" "0.6.1"
    "debug" "2.6.9"
    "escape-html" "~1.0.3"
    "http-errors" "~1.6.2"
    "mime-types" "~2.1.17"
    "parseurl" "~1.3.2"

"serve-static@1.14.2":
  "integrity" "sha512-+TMNA9AFxUEGuC0z2mevogSnn9MXKb4fa7ngeRMJaaGv8vTwnIEkKi+QGvPt33HSnf8pRS+WGM0EbMtCJLKMBQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/serve-static/-/serve-static-1.14.2.tgz"
  "version" "1.14.2"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.17.2"

"set-blocking@^2.0.0":
  "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/set-blocking/-/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-value@^2.0.0", "set-value@^2.0.1":
  "integrity" "sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/set-value/-/set-value-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setimmediate@^1.0.4":
  "integrity" "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/setimmediate/-/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"setprototypeof@1.1.0":
  "integrity" "sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/setprototypeof/-/setprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"setprototypeof@1.2.0":
  "integrity" "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/setprototypeof/-/setprototypeof-1.2.0.tgz"
  "version" "1.2.0"

"sha.js@^2.4.0", "sha.js@^2.4.8":
  "integrity" "sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/sha.js/-/sha.js-2.4.11.tgz"
  "version" "2.4.11"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"shebang-command@^1.2.0":
  "integrity" "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/shebang-command/-/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "^1.0.0"

"shebang-regex@^1.0.0":
  "integrity" "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/shebang-regex/-/shebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shelljs@^0.7.6":
  "integrity" "sha1-3svPh0sNHl+3LhSxZKloMEjprLM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/shelljs/-/shelljs-0.7.8.tgz"
  "version" "0.7.8"
  dependencies:
    "glob" "^7.0.0"
    "interpret" "^1.0.0"
    "rechoir" "^0.6.2"

"shellwords@^0.1.1":
  "integrity" "sha512-vFwSUfQvqybiICwZY5+DAWIPLKsWO31Q91JSKl3UYv+K5c2QRPzn0qzec6QPu1Qc9eHYItiP3NdJqNVqetYAww=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/shellwords/-/shellwords-0.1.1.tgz"
  "version" "0.1.1"

"side-channel@^1.0.4":
  "integrity" "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/side-channel/-/side-channel-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.0"
    "get-intrinsic" "^1.0.2"
    "object-inspect" "^1.9.0"

"signal-exit@^3.0.0", "signal-exit@^3.0.2":
  "integrity" "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/signal-exit/-/signal-exit-3.0.7.tgz"
  "version" "3.0.7"

"simple-swizzle@^0.2.2":
  "integrity" "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "is-arrayish" "^0.3.1"

"slash@^1.0.0":
  "integrity" "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/slash/-/slash-1.0.0.tgz"
  "version" "1.0.0"

"slice-ansi@1.0.0":
  "integrity" "sha512-POqxBK6Lb3q6s047D/XsDVNPnF9Dl8JSaqe9h9lURl0OdNqy/ujDrOiIHtsqXMGbWWTIomRzAMaTyawAU//Reg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/slice-ansi/-/slice-ansi-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"

"snapdragon-node@^2.0.1":
  "integrity" "sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/snapdragon-node/-/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "^1.0.0"
    "isobject" "^3.0.0"
    "snapdragon-util" "^3.0.1"

"snapdragon-util@^3.0.1":
  "integrity" "sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/snapdragon-util/-/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^3.2.0"

"snapdragon@^0.8.1":
  "integrity" "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/snapdragon/-/snapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"sockjs-client@1.1.5":
  "integrity" "sha1-G7fA9yIsQPQq3xT0RCy9Eml3GoM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/sockjs-client/-/sockjs-client-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "debug" "^2.6.6"
    "eventsource" "0.1.6"
    "faye-websocket" "~0.11.0"
    "inherits" "^2.0.1"
    "json3" "^3.3.2"
    "url-parse" "^1.1.8"

"sockjs@0.3.19":
  "integrity" "sha512-V48klKZl8T6MzatbLlzzRNhMepEys9Y4oGFpypBFFn1gLI/QQ9HtLLyWJNbPlwGLelOVOEijUbTTJeLLI59jLw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/sockjs/-/sockjs-0.3.19.tgz"
  "version" "0.3.19"
  dependencies:
    "faye-websocket" "^0.10.0"
    "uuid" "^3.0.1"

"sort-keys@^1.0.0":
  "integrity" "sha1-RBttTTRnmPG05J6JIK37oOVD+a0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/sort-keys/-/sort-keys-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "is-plain-obj" "^1.0.0"

"source-list-map@^2.0.0":
  "integrity" "sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/source-list-map/-/source-list-map-2.0.1.tgz"
  "version" "2.0.1"

"source-map-resolve@^0.5.0", "source-map-resolve@^0.5.2":
  "integrity" "sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/source-map-resolve/-/source-map-resolve-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "atob" "^2.1.2"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-support@^0.4.15":
  "integrity" "sha512-try0/JqxPLF9nOjvSta7tVondkP5dwgyLDjVoyMDlmjugT2lRZ1OfsrYTkCd2hkDnJTKRbO/Rl3orm8vlsUzbA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/source-map-support/-/source-map-support-0.4.18.tgz"
  "version" "0.4.18"
  dependencies:
    "source-map" "^0.5.6"

"source-map-url@^0.4.0":
  "integrity" "sha512-cPiFOTLUKvJFIg4SKVScy4ilPPW6rFgMgfuZJPNoDuMs3nC1HbMUycBoJw77xFIp6z1UJQJOfx6C9GMH80DiTw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/source-map-url/-/source-map-url-0.4.1.tgz"
  "version" "0.4.1"

"source-map@^0.5.0":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://repo.huaweicloud.com/repository/npm/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.5.3", "source-map@^0.5.6", "source-map@~0.5.1":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://repo.huaweicloud.com/repository/npm/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.5.7":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://repo.huaweicloud.com/repository/npm/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.1", "source-map@~0.6.0", "source-map@~0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.7.3":
  "integrity" "sha512-CkCj6giN3S+n9qrYiBTX5gystlENnRW5jZeNLHpe6aue+SrHcG5VYwujhW9s4dY31mEGsxBDrHR6oI69fTXsaQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/source-map/-/source-map-0.7.3.tgz"
  "version" "0.7.3"

"spdx-correct@^3.0.0":
  "integrity" "sha512-cOYcUWwhCuHCXi49RhFRCyJEK3iPj1Ziz9DpViV3tbZOwXD49QzIN3MpOLJNxh2qwq2lJJZaKMVw9qNi4jTC0w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/spdx-correct/-/spdx-correct-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz"
  "version" "2.3.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha512-Ctl2BrFiM0X3MANYgj3CkygxhRmr9mi6xhejbdO960nF6EDJApTYpn0BQnDKlnNBULKiCN1n3w9EBkHK8ZWg+g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/spdx-license-ids/-/spdx-license-ids-3.0.11.tgz"
  "version" "3.0.11"

"spdy-transport@^3.0.0":
  "integrity" "sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/spdy-transport/-/spdy-transport-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "debug" "^4.1.0"
    "detect-node" "^2.0.4"
    "hpack.js" "^2.1.6"
    "obuf" "^1.1.2"
    "readable-stream" "^3.0.6"
    "wbuf" "^1.7.3"

"spdy@^4.0.0":
  "integrity" "sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/spdy/-/spdy-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "debug" "^4.1.0"
    "handle-thing" "^2.0.0"
    "http-deceiver" "^1.2.7"
    "select-hose" "^2.0.0"
    "spdy-transport" "^3.0.0"

"split-string@^3.0.1", "split-string@^3.0.2":
  "integrity" "sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/split-string/-/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"sprintf-js@~1.0.2":
  "integrity" "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/sprintf-js/-/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"ssri@^5.2.4":
  "integrity" "sha512-XRSIPqLij52MtgoQavH/x/dU1qVKtWUAAZeOHsR9c2Ddi4XerFy3mc1alf+dLJKl9EUIm/Ht+EowFkTUOA6GAQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ssri/-/ssri-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "safe-buffer" "^5.1.1"

"stable@^0.1.8":
  "integrity" "sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/stable/-/stable-0.1.8.tgz"
  "version" "0.1.8"

"stackframe@^1.1.1":
  "integrity" "sha512-h88QkzREN/hy8eRdyNhhsO7RSJ5oyTqxxmmn0dzBIMUclZsjpfmrsg81vp8mjjAs2vAZ72nyWxRUwSwmh0e4xg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/stackframe/-/stackframe-1.2.1.tgz"
  "version" "1.2.1"

"static-extend@^0.1.1":
  "integrity" "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/static-extend/-/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", "statuses@~1.5.0":
  "integrity" "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="
  "resolved" "https://repo.huaweicloud.com/repository/npm/statuses/-/statuses-1.5.0.tgz"
  "version" "1.5.0"

"stream-browserify@^2.0.1":
  "integrity" "sha512-nX6hmklHs/gr2FuxYDltq8fJA1GDlxKQCz8O/IM4atRqBH8OORmBNgfvW5gG10GT/qQ9u0CzIvr2X5Pkt6ntqg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/stream-browserify/-/stream-browserify-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "inherits" "~2.0.1"
    "readable-stream" "^2.0.2"

"stream-each@^1.1.0":
  "integrity" "sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/stream-each/-/stream-each-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "end-of-stream" "^1.1.0"
    "stream-shift" "^1.0.0"

"stream-http@^2.7.2":
  "integrity" "sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/stream-http/-/stream-http-2.8.3.tgz"
  "version" "2.8.3"
  dependencies:
    "builtin-status-codes" "^3.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.3.6"
    "to-arraybuffer" "^1.0.0"
    "xtend" "^4.0.0"

"stream-shift@^1.0.0":
  "integrity" "sha512-AiisoFqQ0vbGcZgQPY1cdP2I76glaVA/RauYR4G4thNFgkTqr90yXTo4LYX60Jl+sIlPNHHdGSwo01AvbKUSVQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/stream-shift/-/stream-shift-1.0.1.tgz"
  "version" "1.0.1"

"strict-uri-encode@^1.0.0":
  "integrity" "sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/strict-uri-encode/-/strict-uri-encode-1.1.0.tgz"
  "version" "1.1.0"

"string_decoder@^1.0.0", "string_decoder@^1.1.1", "string_decoder@~1.1.1":
  "integrity" "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-width@^1.0.1", "string-width@^1.0.2":
  "integrity" "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M="
  "resolved" "https://repo.huaweicloud.com/repository/npm/string-width/-/string-width-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "code-point-at" "^1.0.0"
    "is-fullwidth-code-point" "^1.0.0"
    "strip-ansi" "^3.0.0"

"string-width@^2.0.0", "string-width@^2.1.0", "string-width@^2.1.1":
  "integrity" "sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/string-width/-/string-width-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^4.0.0"

"string.prototype.trimend@^1.0.4":
  "integrity" "sha512-y9xCjw1P23Awk8EvTpcyL2NIr1j7wJ39f+k6lvRnSMz+mz9CGz9NYPelDk42kOz6+ql8xjfK8oYzy3jAP5QU5A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"string.prototype.trimstart@^1.0.4":
  "integrity" "sha512-jh6e984OBfvxS50tdY2nRZnoC5/mLFKOREQfw8t5yytkoUsJRNxvI/E39qu1sD0OtWI3OC0XgKSmcWwziwYuZw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"strip-ansi@^3.0.0", "strip-ansi@^3.0.1":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/strip-ansi/-/strip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^4.0.0":
  "integrity" "sha1-qEeQIusaw2iocTibY1JixQXuNo8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/strip-ansi/-/strip-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-regex" "^3.0.0"

"strip-bom@^2.0.0":
  "integrity" "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/strip-bom/-/strip-bom-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "is-utf8" "^0.2.0"

"strip-bom@^3.0.0":
  "integrity" "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/strip-bom/-/strip-bom-3.0.0.tgz"
  "version" "3.0.0"

"strip-eof@^1.0.0":
  "integrity" "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/strip-eof/-/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-indent@^1.0.1":
  "integrity" "sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/strip-indent/-/strip-indent-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "get-stdin" "^4.0.1"

"strip-json-comments@~2.0.1":
  "integrity" "sha1-PFMZQukIwml8DsNEhYwobHygpgo="
  "resolved" "https://repo.huaweicloud.com/repository/npm/strip-json-comments/-/strip-json-comments-2.0.1.tgz"
  "version" "2.0.1"

"stylehacks@^4.0.0":
  "integrity" "sha512-7GlLk9JwlElY4Y6a/rmbH2MhVlTyVmiJd1PfTCqFaIBEGMYNsrO/v3SeGTdhBThLg4Z+NbOk/qFMwCa+J+3p/g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/stylehacks/-/stylehacks-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"

"stylus-loader@^3.0.2":
  "integrity" "sha512-+VomPdZ6a0razP+zinir61yZgpw2NfljeSsdUF5kJuEzlo3khXhY19Fn6l8QQz1GRJGtMCo8nG5C04ePyV7SUA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/stylus-loader/-/stylus-loader-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "loader-utils" "^1.0.2"
    "lodash.clonedeep" "^4.5.0"
    "when" "~3.6.x"

"stylus@^0.54.5":
  "integrity" "sha512-vr54Or4BZ7pJafo2mpf0ZcwA74rpuYCZbxrHBsH8kbcXOwSfvBFwsRfpGO5OD5fhG5HDCFW737PKaawI7OqEAg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/stylus/-/stylus-0.54.8.tgz"
  "version" "0.54.8"
  dependencies:
    "css-parse" "~2.0.0"
    "debug" "~3.1.0"
    "glob" "^7.1.6"
    "mkdirp" "~1.0.4"
    "safer-buffer" "^2.1.2"
    "sax" "~1.2.4"
    "semver" "^6.3.0"
    "source-map" "^0.7.3"

"supports-color@^2.0.0":
  "integrity" "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/supports-color/-/supports-color-2.0.0.tgz"
  "version" "2.0.0"

"supports-color@^3.2.3":
  "integrity" "sha1-ZawFBLOVQXHYpklGsq48u4pfVPY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/supports-color/-/supports-color-3.2.3.tgz"
  "version" "3.2.3"
  dependencies:
    "has-flag" "^1.0.0"

"supports-color@^4.2.1":
  "integrity" "sha1-vnoN5ITexcXN34s9WRJQRJEvY1s="
  "resolved" "https://repo.huaweicloud.com/repository/npm/supports-color/-/supports-color-4.5.0.tgz"
  "version" "4.5.0"
  dependencies:
    "has-flag" "^2.0.0"

"supports-color@^5.1.0", "supports-color@^5.3.0", "supports-color@^5.4.0":
  "integrity" "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-preserve-symlinks-flag@^1.0.0":
  "integrity" "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  "version" "1.0.0"

"svgo@^0.7.0":
  "integrity" "sha1-n1dyQTlSE1xv779Ar+ak+qiLS7U="
  "resolved" "https://repo.huaweicloud.com/repository/npm/svgo/-/svgo-0.7.2.tgz"
  "version" "0.7.2"
  dependencies:
    "coa" "~1.0.1"
    "colors" "~1.1.2"
    "csso" "~2.3.1"
    "js-yaml" "~3.7.0"
    "mkdirp" "~0.5.1"
    "sax" "~1.2.1"
    "whet.extend" "~0.9.9"

"svgo@^1.0.0":
  "integrity" "sha512-yhy/sQYxR5BkC98CY7o31VGsg014AKLEPxdfhora76l36hD9Rdy5NZA/Ocn6yayNPgSamYdtX2rFJdcv07AYVw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/svgo/-/svgo-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "chalk" "^2.4.1"
    "coa" "^2.0.2"
    "css-select" "^2.0.0"
    "css-select-base-adapter" "^0.1.1"
    "css-tree" "1.0.0-alpha.37"
    "csso" "^4.0.2"
    "js-yaml" "^3.13.1"
    "mkdirp" "~0.5.1"
    "object.values" "^1.1.0"
    "sax" "~1.2.4"
    "stable" "^0.1.8"
    "unquote" "~1.1.1"
    "util.promisify" "~1.0.0"

"table@4.0.2":
  "integrity" "sha512-UUkEAPdSGxtRpiV9ozJ5cMTtYiqz7Ni1OGqLXRCynrvzdtR1p+cfOWe2RJLwvUG8hNanaSRjecIqwOjqeatDsA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/table/-/table-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "ajv" "^5.2.3"
    "ajv-keywords" "^2.1.0"
    "chalk" "^2.1.0"
    "lodash" "^4.17.4"
    "slice-ansi" "1.0.0"
    "string-width" "^2.1.1"

"tapable@^0.2.7":
  "integrity" "sha512-2wsvQ+4GwBvLPLWsNfLCDYGsW6xb7aeC6utq2Qh0PFwgEy7K7dsma9Jsmb2zSQj7GvYAyUGSntLtsv++GmgL1A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/tapable/-/tapable-0.2.9.tgz"
  "version" "0.2.9"

"text-table@^0.2.0", "text-table@~0.2.0":
  "integrity" "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/text-table/-/text-table-0.2.0.tgz"
  "version" "0.2.0"

"through@^2.3.6":
  "integrity" "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/through/-/through-2.3.8.tgz"
  "version" "2.3.8"

"through2@^2.0.0":
  "integrity" "sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/through2/-/through2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"thunky@^1.0.2":
  "integrity" "sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/thunky/-/thunky-1.1.0.tgz"
  "version" "1.1.0"

"time-stamp@^2.0.0":
  "integrity" "sha512-zxke8goJQpBeEgD82CXABeMh0LSJcj7CXEd0OHOg45HgcofF7pxNwZm9+RknpxpDhwN4gFpySkApKfFYfRQnUA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/time-stamp/-/time-stamp-2.2.0.tgz"
  "version" "2.2.0"

"timers-browserify@^2.0.4":
  "integrity" "sha512-9phl76Cqm6FhSX9Xe1ZUAMLtm1BLkKj2Qd5ApyWkXzsMRaA7dgr81kf4wJmQf/hAvg8EEyJxDo3du/0KlhPiKQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/timers-browserify/-/timers-browserify-2.0.12.tgz"
  "version" "2.0.12"
  dependencies:
    "setimmediate" "^1.0.4"

"timsort@^0.3.0":
  "integrity" "sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q="
  "resolved" "https://repo.huaweicloud.com/repository/npm/timsort/-/timsort-0.3.0.tgz"
  "version" "0.3.0"

"tmp@^0.0.33":
  "integrity" "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/tmp/-/tmp-0.0.33.tgz"
  "version" "0.0.33"
  dependencies:
    "os-tmpdir" "~1.0.2"

"to-arraybuffer@^1.0.0":
  "integrity" "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M="
  "resolved" "https://repo.huaweicloud.com/repository/npm/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz"
  "version" "1.0.1"

"to-fast-properties@^1.0.3":
  "integrity" "sha1-uDVx+k2MJbguIxsG46MFXeTKGkc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/to-fast-properties/-/to-fast-properties-1.0.3.tgz"
  "version" "1.0.3"

"to-fast-properties@^2.0.0":
  "integrity" "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="
  "resolved" "https://repo.huaweicloud.com/repository/npm/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-object-path@^0.3.0":
  "integrity" "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68="
  "resolved" "https://repo.huaweicloud.com/repository/npm/to-object-path/-/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^2.1.0":
  "integrity" "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/to-regex-range/-/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"to-regex@^3.0.1", "to-regex@^3.0.2":
  "integrity" "sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/to-regex/-/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"toidentifier@1.0.1":
  "integrity" "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/toidentifier/-/toidentifier-1.0.1.tgz"
  "version" "1.0.1"

"toposort@^1.0.0":
  "integrity" "sha1-LmhELZ9k7HILjMieZEOsbKqVACk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/toposort/-/toposort-1.0.7.tgz"
  "version" "1.0.7"

"trim-newlines@^1.0.0":
  "integrity" "sha1-WIeWa7WCpFA6QetST301ARgVphM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/trim-newlines/-/trim-newlines-1.0.0.tgz"
  "version" "1.0.0"

"trim-right@^1.0.1":
  "integrity" "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/trim-right/-/trim-right-1.0.1.tgz"
  "version" "1.0.1"

"tryer@^1.0.0":
  "integrity" "sha512-c3zayb8/kWWpycWYg87P71E1S1ZL6b6IJxfb5fvsUgsf0S2MVGaDhDXXjDMpdCpfWXqptc+4mXwmiy1ypXqRAA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/tryer/-/tryer-1.0.1.tgz"
  "version" "1.0.1"

"tsconfig-paths@^3.12.0":
  "integrity" "sha512-fxDhWnFSLt3VuTwtvJt5fpwxBHg5AdKWMsgcPOOIilyjymcYVZoCQF8fvFRezCNfblEXmi+PcM1eYHeOAgXCOQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/tsconfig-paths/-/tsconfig-paths-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "@types/json5" "^0.0.29"
    "json5" "^1.0.1"
    "minimist" "^1.2.6"
    "strip-bom" "^3.0.0"

"tslib@^1.10.0":
  "integrity" "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tty-browserify@0.0.0":
  "integrity" "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY="
  "resolved" "https://repo.huaweicloud.com/repository/npm/tty-browserify/-/tty-browserify-0.0.0.tgz"
  "version" "0.0.0"

"type-check@~0.3.2":
  "integrity" "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I="
  "resolved" "https://repo.huaweicloud.com/repository/npm/type-check/-/type-check-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "prelude-ls" "~1.1.2"

"type-is@~1.6.18":
  "integrity" "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/type-is/-/type-is-1.6.18.tgz"
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.24"

"type@^1.0.1":
  "integrity" "sha512-+5nt5AAniqsCnu2cEQQdpzCAh33kVx8n0VoFidKpB1dVVLAN/F+bgVOqOJqOnEnrhp222clB5p3vUlD+1QAnfg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/type/-/type-1.2.0.tgz"
  "version" "1.2.0"

"type@^2.5.0":
  "integrity" "sha512-eiDBDOmkih5pMbo9OqsqPRGMljLodLcwd5XD5JbtNB0o89xZAwynY9EdCDsJU7LtcVCClu9DvM7/0Ep1hYX3EQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/type/-/type-2.6.0.tgz"
  "version" "2.6.0"

"typedarray@^0.0.6":
  "integrity" "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="
  "resolved" "https://repo.huaweicloud.com/repository/npm/typedarray/-/typedarray-0.0.6.tgz"
  "version" "0.0.6"

"uglify-es@^3.3.4":
  "integrity" "sha512-r+MU0rfv4L/0eeW3xZrd16t4NZfK8Ld4SWVglYBb7ez5uXFWHuVRs6xCTrf1yirs9a4j4Y27nn7SRfO6v67XsQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/uglify-es/-/uglify-es-3.3.9.tgz"
  "version" "3.3.9"
  dependencies:
    "commander" "~2.13.0"
    "source-map" "~0.6.1"

"uglify-js@^2.8.29":
  "integrity" "sha1-KcVzMUgFe7Th913zW3qcty5qWd0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/uglify-js/-/uglify-js-2.8.29.tgz"
  "version" "2.8.29"
  dependencies:
    "source-map" "~0.5.1"
    "yargs" "~3.10.0"
  optionalDependencies:
    "uglify-to-browserify" "~1.0.0"

"uglify-js@3.4.x":
  "integrity" "sha512-Y2VsbPVs0FIshJztycsO2SfPk7/KAF/T72qzv9u5EpQ4kB2hQoHlhNQTsNyy6ul7lQtqJN/AoWeS23OzEiEFxw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/uglify-js/-/uglify-js-3.4.10.tgz"
  "version" "3.4.10"
  dependencies:
    "commander" "~2.19.0"
    "source-map" "~0.6.1"

"uglify-to-browserify@~1.0.0":
  "integrity" "sha1-bgkk1r2mta/jSeOabWMoUKD4grc="
  "resolved" "https://repo.huaweicloud.com/repository/npm/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz"
  "version" "1.0.2"

"uglifyjs-webpack-plugin@^0.4.6":
  "integrity" "sha1-uVH0q7a9YX5m9j64kUmOORdj4wk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/uglifyjs-webpack-plugin/-/uglifyjs-webpack-plugin-0.4.6.tgz"
  "version" "0.4.6"
  dependencies:
    "source-map" "^0.5.6"
    "uglify-js" "^2.8.29"
    "webpack-sources" "^1.0.1"

"uglifyjs-webpack-plugin@^1.1.1":
  "integrity" "sha512-ovHIch0AMlxjD/97j9AYovZxG5wnHOPkL7T1GKochBADp/Zwc44pEWNqpKl1Loupp1WhFg7SlYmHZRUfdAacgw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/uglifyjs-webpack-plugin/-/uglifyjs-webpack-plugin-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "cacache" "^10.0.4"
    "find-cache-dir" "^1.0.0"
    "schema-utils" "^0.4.5"
    "serialize-javascript" "^1.4.0"
    "source-map" "^0.6.1"
    "uglify-es" "^3.3.4"
    "webpack-sources" "^1.1.0"
    "worker-farm" "^1.5.2"

"unbox-primitive@^1.0.1":
  "integrity" "sha512-tZU/3NqK3dA5gpE1KtyiJUrEB0lxnGkMFHptJ7q6ewdZ8s12QrODwNbhIJStmJkd1QDXa1NRA8aF2A1zk/Ypyw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/unbox-primitive/-/unbox-primitive-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "function-bind" "^1.1.1"
    "has-bigints" "^1.0.1"
    "has-symbols" "^1.0.2"
    "which-boxed-primitive" "^1.0.2"

"union-value@^1.0.0":
  "integrity" "sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/union-value/-/union-value-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^2.0.1"

"uniq@^1.0.1":
  "integrity" "sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/uniq/-/uniq-1.0.1.tgz"
  "version" "1.0.1"

"uniqs@^2.0.0":
  "integrity" "sha1-/+3ks2slKQaW5uFl1KWe25mOawI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/uniqs/-/uniqs-2.0.0.tgz"
  "version" "2.0.0"

"unique-filename@^1.1.0":
  "integrity" "sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/unique-filename/-/unique-filename-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "unique-slug" "^2.0.0"

"unique-slug@^2.0.0":
  "integrity" "sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/unique-slug/-/unique-slug-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "imurmurhash" "^0.1.4"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/unpipe/-/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unquote@~1.1.1":
  "integrity" "sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ="
  "resolved" "https://repo.huaweicloud.com/repository/npm/unquote/-/unquote-1.1.1.tgz"
  "version" "1.1.1"

"unset-value@^1.0.0":
  "integrity" "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/unset-value/-/unset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"upath@^1.1.1":
  "integrity" "sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/upath/-/upath-1.2.0.tgz"
  "version" "1.2.0"

"upper-case@^1.1.1":
  "integrity" "sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/upper-case/-/upper-case-1.1.3.tgz"
  "version" "1.1.3"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"urix@^0.1.0":
  "integrity" "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/urix/-/urix-0.1.0.tgz"
  "version" "0.1.0"

"url-loader@^0.5.8":
  "integrity" "sha512-B7QYFyvv+fOBqBVeefsxv6koWWtjmHaMFT6KZWti4KRw8YUD/hOU+3AECvXuzyVawIBx3z7zQRejXCDSO5kk1Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/url-loader/-/url-loader-0.5.9.tgz"
  "version" "0.5.9"
  dependencies:
    "loader-utils" "^1.0.2"
    "mime" "1.3.x"

"url-parse@^1.1.8", "url-parse@^1.4.3":
  "integrity" "sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/url-parse/-/url-parse-1.5.10.tgz"
  "version" "1.5.10"
  dependencies:
    "querystringify" "^2.1.1"
    "requires-port" "^1.0.0"

"url@^0.11.0":
  "integrity" "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/url/-/url-0.11.0.tgz"
  "version" "0.11.0"
  dependencies:
    "punycode" "1.3.2"
    "querystring" "0.2.0"

"use@^3.1.0":
  "integrity" "sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/use/-/use-3.1.1.tgz"
  "version" "3.1.1"

"util-deprecate@^1.0.1", "util-deprecate@^1.0.2", "util-deprecate@~1.0.1":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util.promisify@~1.0.0":
  "integrity" "sha512-g9JpC/3He3bm38zsLupWryXHoEcS22YHthuPQSJdMy6KNrzIRzWqcsHzD/WUnqe45whVou4VIsPew37DoXWNrA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/util.promisify/-/util.promisify-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.17.2"
    "has-symbols" "^1.0.1"
    "object.getownpropertydescriptors" "^2.1.0"

"util@^0.11.0":
  "integrity" "sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/util/-/util-0.11.1.tgz"
  "version" "0.11.1"
  dependencies:
    "inherits" "2.0.3"

"util@0.10.3":
  "integrity" "sha1-evsa/lCAUkZInj23/g7TeTNqwPk="
  "resolved" "https://repo.huaweicloud.com/repository/npm/util/-/util-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "inherits" "2.0.1"

"utila@~0.4":
  "integrity" "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/utila/-/utila-0.4.0.tgz"
  "version" "0.4.0"

"utils-merge@1.0.1":
  "integrity" "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="
  "resolved" "https://repo.huaweicloud.com/repository/npm/utils-merge/-/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"uuid@^3.0.1":
  "integrity" "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/uuid/-/uuid-3.4.0.tgz"
  "version" "3.4.0"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"vary@~1.1.2":
  "integrity" "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/vary/-/vary-1.1.2.tgz"
  "version" "1.1.2"

"vendors@^1.0.0":
  "integrity" "sha512-/juG65kTL4Cy2su4P8HjtkTxk6VmJDiOPBufWniqQ6wknac6jNiXS9vU+hO3wgusiyqWlzTbVHi0dyJqRONg3w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/vendors/-/vendors-1.0.4.tgz"
  "version" "1.0.4"

"vm-browserify@^1.0.1":
  "integrity" "sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/vm-browserify/-/vm-browserify-1.1.2.tgz"
  "version" "1.1.2"

"vue-eslint-parser@^2.0.3":
  "integrity" "sha512-ZezcU71Owm84xVF6gfurBQUGg8WQ+WZGxgDEQu1IHFBZNx7BFZg3L1yHxrCBNNwbwFtE1GuvfJKMtb6Xuwc/Bw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/vue-eslint-parser/-/vue-eslint-parser-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "debug" "^3.1.0"
    "eslint-scope" "^3.7.1"
    "eslint-visitor-keys" "^1.0.0"
    "espree" "^3.5.2"
    "esquery" "^1.0.0"
    "lodash" "^4.17.4"

"vue-hot-reload-api@^2.2.0":
  "integrity" "sha512-BXq3jwIagosjgNVae6tkHzzIk6a8MHFtzAdwhnV5VlvPTFxDCvIttgSiHWjdGoTJvXtmRu5HacExfdarRcFhog=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/vue-hot-reload-api/-/vue-hot-reload-api-2.3.4.tgz"
  "version" "2.3.4"

"vue-i18n@8.15.0":
  "integrity" "sha512-juJ/avAP39bOMycC+qQDLJ8U9z9LtLF/9PsRoJLBSfsYZo9bqYntyyX5QPicwlb1emJKjgxhZ3YofHiQcXBu0Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/vue-i18n/-/vue-i18n-8.15.0.tgz"
  "version" "8.15.0"

"vue-loader@^13.3.0":
  "integrity" "sha512-ACCwbfeC6HjY2pnDii+Zer+MZ6sdOtwvLmDXRK/BoD3WNR551V22R6KEagwHoTRJ0ZlIhpCBkptpCU6+Ri/05w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/vue-loader/-/vue-loader-13.7.3.tgz"
  "version" "13.7.3"
  dependencies:
    "consolidate" "^0.14.0"
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.1.0"
    "lru-cache" "^4.1.1"
    "postcss" "^6.0.8"
    "postcss-load-config" "^1.1.0"
    "postcss-selector-parser" "^2.0.0"
    "prettier" "^1.7.0"
    "resolve" "^1.4.0"
    "source-map" "^0.6.1"
    "vue-hot-reload-api" "^2.2.0"
    "vue-style-loader" "^3.0.0"
    "vue-template-es2015-compiler" "^1.6.0"

"vue-router@3.0.2":
  "integrity" "sha512-opKtsxjp9eOcFWdp6xLQPLmRGgfM932Tl56U9chYTnoWqKxQ8M20N7AkdEbM5beUh6wICoFGYugAX9vQjyJLFg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/vue-router/-/vue-router-3.0.2.tgz"
  "version" "3.0.2"

"vue-style-loader@^3.0.0", "vue-style-loader@^3.0.1":
  "integrity" "sha512-ICtVdK/p+qXWpdSs2alWtsXt9YnDoYjQe0w5616j9+/EhjoxZkbun34uWgsMFnC1MhrMMwaWiImz3K2jK1Yp2Q=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/vue-style-loader/-/vue-style-loader-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.0.2"

"vue-template-compiler@2.5.18":
  "integrity" "sha512-WG2G+r5YxqkbTyJnbpkJuISTVI9MvYNGAZVKnmn8S4AoP0R0OufIKrHEV+GKwilLa+p3t/Plo8FzJXdhL9m4Sw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/vue-template-compiler/-/vue-template-compiler-2.5.18.tgz"
  "version" "2.5.18"
  dependencies:
    "de-indent" "^1.0.2"
    "he" "^1.1.0"

"vue-template-es2015-compiler@^1.6.0":
  "integrity" "sha512-4gDntzrifFnCEvyoO8PqyJDmguXgVPxKiIxrBKjIowvL9l+N66196+72XVYR8BBf1Uv1Fgt3bGevJ+sEmxfZzw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/vue-template-es2015-compiler/-/vue-template-es2015-compiler-1.9.1.tgz"
  "version" "1.9.1"

"vue@2.5.18":
  "integrity" "sha512-j4oT4jQdhBf3dUxzu1ogjkhzMU44AtwqtLz7oq/u7340/lmUUogTG6/D6P4/J2cCosoM21MqMIUsQchPKS4elg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/vue/-/vue-2.5.18.tgz"
  "version" "2.5.18"

"vuex@3.0.1":
  "integrity" "sha512-wLoqz0B7DSZtgbWL1ShIBBCjv22GV5U+vcBFox658g6V0s4wZV9P4YjCNyoHSyIBpj1f29JBoNQIqD82cR4O3w=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/vuex/-/vuex-3.0.1.tgz"
  "version" "3.0.1"

"watchpack-chokidar2@^2.0.1":
  "integrity" "sha512-nCFfBIPKr5Sh61s4LPpy1Wtfi0HE8isJ3d2Yb5/Ppw2P2B/3eVSEBjKfN0fmHJSK14+31KwMKmcrzs2GM4P0Ww=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/watchpack-chokidar2/-/watchpack-chokidar2-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "chokidar" "^2.1.8"

"watchpack@^1.4.0":
  "integrity" "sha512-9P3MWk6SrKjHsGkLT2KHXdQ/9SNkyoJbabxnKOoJepsvJjJG8uYTR3yTPxPQvNDI3w4Nz1xnE0TLHK4RIVe/MQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/watchpack/-/watchpack-1.7.5.tgz"
  "version" "1.7.5"
  dependencies:
    "graceful-fs" "^4.1.2"
    "neo-async" "^2.5.0"
  optionalDependencies:
    "chokidar" "^3.4.1"
    "watchpack-chokidar2" "^2.0.1"

"wbuf@^1.1.0", "wbuf@^1.7.3":
  "integrity" "sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/wbuf/-/wbuf-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "minimalistic-assert" "^1.0.0"

"webpack-bundle-analyzer@^2.9.0":
  "integrity" "sha512-rwxyfecTAxoarCC9VlHlIpfQCmmJ/qWD5bpbjkof+7HrNhTNZIwZITxN6CdlYL2axGmwNUQ+tFgcSOiNXMf/sQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/webpack-bundle-analyzer/-/webpack-bundle-analyzer-2.13.1.tgz"
  "version" "2.13.1"
  dependencies:
    "acorn" "^5.3.0"
    "bfj-node4" "^5.2.0"
    "chalk" "^2.3.0"
    "commander" "^2.13.0"
    "ejs" "^2.5.7"
    "express" "^4.16.2"
    "filesize" "^3.5.11"
    "gzip-size" "^4.1.0"
    "lodash" "^4.17.4"
    "mkdirp" "^0.5.1"
    "opener" "^1.4.3"
    "ws" "^4.0.0"

"webpack-dev-middleware@1.12.2":
  "integrity" "sha512-FCrqPy1yy/sN6U/SaEZcHKRXGlqU0DUaEBL45jkUYoB8foVb6wCnbIJ1HKIx+qUFTW+3JpVcCJCxZ8VATL4e+A=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/webpack-dev-middleware/-/webpack-dev-middleware-1.12.2.tgz"
  "version" "1.12.2"
  dependencies:
    "memory-fs" "~0.4.1"
    "mime" "^1.5.0"
    "path-is-absolute" "^1.0.0"
    "range-parser" "^1.0.3"
    "time-stamp" "^2.0.0"

"webpack-dev-server@^2.9.1":
  "integrity" "sha512-7TdOKKt7G3sWEhPKV0zP+nD0c4V9YKUJ3wDdBwQsZNo58oZIRoVIu66pg7PYkBW8A74msP9C2kLwmxGHndz/pw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/webpack-dev-server/-/webpack-dev-server-2.11.5.tgz"
  "version" "2.11.5"
  dependencies:
    "ansi-html" "0.0.7"
    "array-includes" "^3.0.3"
    "bonjour" "^3.5.0"
    "chokidar" "^2.1.2"
    "compression" "^1.7.3"
    "connect-history-api-fallback" "^1.3.0"
    "debug" "^3.1.0"
    "del" "^3.0.0"
    "express" "^4.16.2"
    "html-entities" "^1.2.0"
    "http-proxy-middleware" "^0.19.1"
    "import-local" "^1.0.0"
    "internal-ip" "1.2.0"
    "ip" "^1.1.5"
    "killable" "^1.0.0"
    "loglevel" "^1.4.1"
    "opn" "^5.1.0"
    "portfinder" "^1.0.9"
    "selfsigned" "^1.9.1"
    "serve-index" "^1.9.1"
    "sockjs" "0.3.19"
    "sockjs-client" "1.1.5"
    "spdy" "^4.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^5.1.0"
    "webpack-dev-middleware" "1.12.2"
    "yargs" "6.6.0"

"webpack-merge@^4.1.0":
  "integrity" "sha512-TUE1UGoTX2Cd42j3krGYqObZbOD+xF7u28WB7tfUordytSjbWTIjK/8V0amkBfTYN4/pB/GIDlJZZ657BGG19g=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/webpack-merge/-/webpack-merge-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "lodash" "^4.17.15"

"webpack-sources@^1.0.1", "webpack-sources@^1.1.0":
  "integrity" "sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/webpack-sources/-/webpack-sources-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "source-list-map" "^2.0.0"
    "source-map" "~0.6.1"

"webpack@^3.6.0":
  "integrity" "sha512-Sw7MdIIOv/nkzPzee4o0EdvCuPmxT98+vVpIvwtcwcF1Q4SDSNp92vwcKc4REe7NItH9f1S4ra9FuQ7yuYZ8bQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/webpack/-/webpack-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "acorn" "^5.0.0"
    "acorn-dynamic-import" "^2.0.0"
    "ajv" "^6.1.0"
    "ajv-keywords" "^3.1.0"
    "async" "^2.1.2"
    "enhanced-resolve" "^3.4.0"
    "escope" "^3.6.0"
    "interpret" "^1.0.0"
    "json-loader" "^0.5.4"
    "json5" "^0.5.1"
    "loader-runner" "^2.3.0"
    "loader-utils" "^1.1.0"
    "memory-fs" "~0.4.1"
    "mkdirp" "~0.5.0"
    "node-libs-browser" "^2.0.0"
    "source-map" "^0.5.3"
    "supports-color" "^4.2.1"
    "tapable" "^0.2.7"
    "uglifyjs-webpack-plugin" "^0.4.6"
    "watchpack" "^1.4.0"
    "webpack-sources" "^1.0.1"
    "yargs" "^8.0.2"

"websocket-driver@>=0.5.1":
  "integrity" "sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/websocket-driver/-/websocket-driver-0.7.4.tgz"
  "version" "0.7.4"
  dependencies:
    "http-parser-js" ">=0.5.1"
    "safe-buffer" ">=5.1.0"
    "websocket-extensions" ">=0.1.1"

"websocket-extensions@>=0.1.1":
  "integrity" "sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/websocket-extensions/-/websocket-extensions-0.1.4.tgz"
  "version" "0.1.4"

"when@~3.6.x":
  "integrity" "sha1-RztRfsFZ4rhQBUl6E5g/CVQS404="
  "resolved" "https://repo.huaweicloud.com/repository/npm/when/-/when-3.6.4.tgz"
  "version" "3.6.4"

"whet.extend@~0.9.9":
  "integrity" "sha1-+HfVv2SMl+WqVC+twW1qJZucEaE="
  "resolved" "https://repo.huaweicloud.com/repository/npm/whet.extend/-/whet.extend-0.9.9.tgz"
  "version" "0.9.9"

"which-boxed-primitive@^1.0.2":
  "integrity" "sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-bigint" "^1.0.1"
    "is-boolean-object" "^1.1.0"
    "is-number-object" "^1.0.4"
    "is-string" "^1.0.5"
    "is-symbol" "^1.0.3"

"which-module@^1.0.0":
  "integrity" "sha1-u6Y8qGGUiZT/MHc2CJ47lgJsKk8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/which-module/-/which-module-1.0.0.tgz"
  "version" "1.0.0"

"which-module@^2.0.0":
  "integrity" "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="
  "resolved" "https://repo.huaweicloud.com/repository/npm/which-module/-/which-module-2.0.0.tgz"
  "version" "2.0.0"

"which@^1.2.9", "which@^1.3.0":
  "integrity" "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/which/-/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"window-size@0.1.0":
  "integrity" "sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0="
  "resolved" "https://repo.huaweicloud.com/repository/npm/window-size/-/window-size-0.1.0.tgz"
  "version" "0.1.0"

"word-wrap@~1.2.3":
  "integrity" "sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/word-wrap/-/word-wrap-1.2.3.tgz"
  "version" "1.2.3"

"wordwrap@0.0.2":
  "integrity" "sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/wordwrap/-/wordwrap-0.0.2.tgz"
  "version" "0.0.2"

"worker-farm@^1.5.2":
  "integrity" "sha512-rvw3QTZc8lAxyVrqcSGVm5yP/IJ2UcB3U0graE3LCFoZ0Yn2x4EoVSqJKdB/T5M+FLcRPjz4TDacRf3OCfNUzw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/worker-farm/-/worker-farm-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "errno" "~0.1.7"

"wrap-ansi@^2.0.0":
  "integrity" "sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU="
  "resolved" "https://repo.huaweicloud.com/repository/npm/wrap-ansi/-/wrap-ansi-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "string-width" "^1.0.1"
    "strip-ansi" "^3.0.1"

"wrappy@1":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
  "resolved" "https://repo.huaweicloud.com/repository/npm/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"write@^0.2.1":
  "integrity" "sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c="
  "resolved" "https://repo.huaweicloud.com/repository/npm/write/-/write-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "mkdirp" "^0.5.1"

"ws@^4.0.0":
  "integrity" "sha512-ZGh/8kF9rrRNffkLFV4AzhvooEclrOH0xaugmqGsIfFgOE/pIz4fMc4Ef+5HSQqTEug2S9JZIWDR47duDSLfaA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/ws/-/ws-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "async-limiter" "~1.0.0"
    "safe-buffer" "~5.1.0"

"xtend@^4.0.0", "xtend@~4.0.1":
  "integrity" "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/xtend/-/xtend-4.0.2.tgz"
  "version" "4.0.2"

"xxhashjs@^0.2.1":
  "integrity" "sha512-AkTuIuVTET12tpsVIQo+ZU6f/qDmKuRUcjaqR+OIvm+aCBsZ95i7UVY5WJ9TMsSaZ0DA2WxoZ4acu0sPH+OKAw=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/xxhashjs/-/xxhashjs-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "cuint" "^0.2.2"

"y18n@^3.2.1":
  "integrity" "sha512-uGZHXkHnhF0XeeAPgnKfPv1bgKAYyVvmNL1xlKsPYZPaIHxGti2hHqvOCQv71XMsLxu1QjergkqogUnms5D3YQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/y18n/-/y18n-3.2.2.tgz"
  "version" "3.2.2"

"y18n@^4.0.0":
  "integrity" "sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/y18n/-/y18n-4.0.3.tgz"
  "version" "4.0.3"

"yallist@^2.1.2":
  "integrity" "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="
  "resolved" "https://repo.huaweicloud.com/repository/npm/yallist/-/yallist-2.1.2.tgz"
  "version" "2.1.2"

"yargs-parser@^4.2.0":
  "integrity" "sha1-KczqwNxPA8bIe0qfIX3RjJ90hxw="
  "resolved" "https://repo.huaweicloud.com/repository/npm/yargs-parser/-/yargs-parser-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "camelcase" "^3.0.0"

"yargs-parser@^7.0.0":
  "integrity" "sha1-jQrELxbqVd69MyyvTEA4s+P139k="
  "resolved" "https://repo.huaweicloud.com/repository/npm/yargs-parser/-/yargs-parser-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "camelcase" "^4.1.0"

"yargs@^8.0.2":
  "integrity" "sha1-YpmpBVsc78lp/355wdkY3Osiw2A="
  "resolved" "https://repo.huaweicloud.com/repository/npm/yargs/-/yargs-8.0.2.tgz"
  "version" "8.0.2"
  dependencies:
    "camelcase" "^4.1.0"
    "cliui" "^3.2.0"
    "decamelize" "^1.1.1"
    "get-caller-file" "^1.0.1"
    "os-locale" "^2.0.0"
    "read-pkg-up" "^2.0.0"
    "require-directory" "^2.1.1"
    "require-main-filename" "^1.0.1"
    "set-blocking" "^2.0.0"
    "string-width" "^2.0.0"
    "which-module" "^2.0.0"
    "y18n" "^3.2.1"
    "yargs-parser" "^7.0.0"

"yargs@~3.10.0":
  "integrity" "sha1-9+572FfdfB0tOMDnTvvWgdFDH9E="
  "resolved" "https://repo.huaweicloud.com/repository/npm/yargs/-/yargs-3.10.0.tgz"
  "version" "3.10.0"
  dependencies:
    "camelcase" "^1.0.2"
    "cliui" "^2.1.0"
    "decamelize" "^1.0.0"
    "window-size" "0.1.0"

"yargs@6.6.0":
  "integrity" "sha1-eC7CHvQDNF+DCoCMo9UTr1YGUgg="
  "resolved" "https://repo.huaweicloud.com/repository/npm/yargs/-/yargs-6.6.0.tgz"
  "version" "6.6.0"
  dependencies:
    "camelcase" "^3.0.0"
    "cliui" "^3.2.0"
    "decamelize" "^1.1.1"
    "get-caller-file" "^1.0.1"
    "os-locale" "^1.4.0"
    "read-pkg-up" "^1.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^1.0.1"
    "set-blocking" "^2.0.0"
    "string-width" "^1.0.2"
    "which-module" "^1.0.0"
    "y18n" "^3.2.1"
    "yargs-parser" "^4.2.0"

"zrender@4.3.0":
  "integrity" "sha512-Dii6j2bDsPkxQayuVf2DXJeruIB/mKVxxcGRZQ9GExiBd4c3w7+oBuvo1O/JGHeFeA1nCmSDVDs/S7yKZG1nrA=="
  "resolved" "https://repo.huaweicloud.com/repository/npm/zrender/-/zrender-4.3.0.tgz"
  "version" "4.3.0"
