<template>
  <div class="sc-container" :class="{'disabled' : (disabled && !inside), 'inside' : inside}">
    <div class="left" ref="leftTitle">
      <div class="title" :style="{fontSize : fontSize}">{{ name }}</div>
      <div class="info">{{ info }}</div>
    </div>
    <div class="right">
      <range :id="idStr ? 'com.huawei.smarthome:id/slider_bar_' + idStr: ''" :class="{'rmiddle': !showXAxis, 'range' : showXAxis}" v-model="slideValue" :disabled="disabled" :showXAxis="showXAxis" :step="step" :min="min" :max="max" @change="onRangeChange"></range>
    </div>
    <div class="divider" v-if="divider"></div>
  </div>
</template>

<script>
import range from '../components/range/index';

export default {
  name: 'SliderBar',
  components: {
    range
  },
  props: {
    name: {
      default: 'Name',
      type: String
    },
    info: {
      default: 'Info',
      type: String
    },
    min: {
      type: Number,
      default: 0
    },
    value: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 100
    },
    step: {
      type: Number,
      default: 1
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showXAxis: {
      type: Boolean,
      default: false
    },
    inside: {
      type: Boolean,
      default: false
    },
    divider: {
      type: Boolean,
      default: false
    },
    idStr: {
      type: String,
      default: ''
    }
  },
  data(){
    return {
      slideValue: this.value,
      fontSize: '1.6rem',
    }
  },
  mounted() {
    //检查title字段英文模式下可能超过 最大1.6rem 最小1.2rem，允许换行
    setTimeout(() => {
      let divWidth = this.$refs.leftTitle.getBoundingClientRect().width;
      // 先尝试较大的字体，如果单行能放下就用单行
      for (var i = 0; i < 5; i++) {
        let textWidth = this.measureText(this.name, 1.6 - i * 0.1).width;
        if (textWidth <= divWidth) {
          this.fontSize = (1.6 - i * 0.1) + 'rem';
          return;
        }
      }
      // 如果都放不下，使用1.2rem并允许换行
      this.fontSize = '1.2rem';
    }, 40)
  },
  methods: {
    onRangeChange(value) {
      this.$emit('change', this.slideValue);
    },
   measureText(pText, pFontSize) {
    var lDiv = document.createElement('div');
    document.body.appendChild(lDiv);
    lDiv.style.fontSize = pFontSize + 'rem';
    lDiv.style.position = 'absolute';
    lDiv.style.left = -1000;
    lDiv.style.top = -1000;
    lDiv.innerHTML = pText;
    var lResult = {
      width: lDiv.clientWidth,
      height: lDiv.clientHeight
    };
    document.body.removeChild(lDiv);
    lDiv = null;
    return lResult;
    },
  },
  watch:{
    slideValue(val){
      this.$emit('input', val);
    },
    value(val){
      this.slideValue = val
    }
  }
};
</script>

<style scoped lang="less">
@import url("../style/public.less");

.sc-container {
  width: 100%;
  .cardStyle();
  position: relative;
  height: 6.4rem;
  display: flex;
  box-sizing: border-box;
  color: var(--emui_text_primary);

  &.inside {
    margin: 0px;
    padding: 0px 0.8rem;
  }

  &.disabled {
    opacity: .4;
  }

  .divider {
    position: absolute;
    width: calc(100% - 1.6rem);
    height: 1px;
    bottom: 0;
    left: 0.8rem;
    transform: scaleY(0.25);
    background: var(--emui_color_divider_horizontal);
  }

  .left {
    width: 6.4rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .title {
      font-size: 1.6rem;
      white-space: normal;
      word-wrap: break-word;
      word-break: break-word;
      line-height: 1.2;
    }

    .info {
      margin-top: 0.3rem;
      font-size: 1.2rem;
      color: var(--emui_text_secondary);
    }
  }

  .right {
    position: relative;
    flex: 1;
    display: flex;
    margin-left: 0.8rem;
    margin-right: 1.6rem;

    .range {
      position: absolute;
      bottom: 1rem;
      left: 0rem;
      width: 100%
    }

    .rmiddle{
      position: absolute;
      top: 50%;
      left: 0rem;
      width: 100%;
      transform: translateY(-50%);
    }
  }
}

</style>
