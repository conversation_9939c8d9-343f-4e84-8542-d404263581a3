export const goBack = {
    computed: {
        dialogShowList () {
            // 弹框集合标志位
            let resultArr = [];
            if (this.dialogList) {
                for (let val of this.dialogList) {
                    if (this[val]) {
                        resultArr.push(val);
                    }
                }
            }
            return resultArr;
        }
    },
    created () {
        this.setupGoBack();
    },
    activated () {
        // keep-alive组件激活时重新设置goBack
        this.setupGoBack();
    },
    methods: {
        setupGoBack () {
            window.goBack = () => {
                if (this.dialogShowList.length === 0) {
                    this.$router.goBack();
                } else {
                    for (let val of this.dialogShowList) {
                        this[val] = false;
                    }
                }
            };
        }
    }
};


