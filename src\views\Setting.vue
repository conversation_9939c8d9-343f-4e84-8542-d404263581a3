<template>
    <div id="setting-page">
        <Titlebar :title="$t('Home_btn11')" @leftClick="$router.back()" :showRightIcon="false" />
        <div class="content" :style="{ paddingTop: `${headerHeight}px` }">
            <div class="module-box">
                <List-Container>
                    <List-Item-Switch
                        :name="$t('memory_mode')"
                        @handleClick="
                            sendCommond(
                                'commonMemorySwitch',
                                'status',
                                1 - commonMemorySwitch,
                                commonMemorySwitch
                            )
                        "
                        :disabled="!canControl"
                        :active="commonMemorySwitch != 0 && canControl"
                    ></List-Item-Switch>
                    <List-Item-Switch
                        :name="$t('donot_disturb')"
                        @handleClick="
                            sendCommond(
                                'donotDisturb',
                                'on',
                                1 - donotDisturb,
                                donotDisturb
                            )
                        "
                        :disabled="!canControl"
                        :active="donotDisturb != 0 && canControl"
                    ></List-Item-Switch>
                    <List-Item-Picker
                        :name="$t('dim_duration')"
                        :info="$t(`duration_${dimDuration}`) + (dimDuration === 0 || dimDuration === 1 ? $t('duration_unit_singular') : $t('duration_unit'))"
                        :options="durationOptions"
                        valueKey="name"
                        :unit="dimDuration === 0 || dimDuration === 1 ? $t('duration_unit_singular') : $t('duration_unit')"
                        :defaultIndex="dimDuration"
                        @select="val => sendCommond('progressSwitch', 'fadeTime', val, dimDuration)"
                        :disabled="!canControl"
                        v-model="dimPickerVisible"
                    />
                    <!-- <List-Item-Picker
                        :name="$t('on_duration')"
                        :info="$t(`duration_${onDuration}`) + (onDuration === 0 || onDuration === 1 ? $t('duration_unit_singular') : $t('duration_unit_plural'))"
                        :options="durationOptions"
                        valueKey="name"
                        :unit="onDuration === 0 || onDuration === 1 ? $t('duration_unit_singular') : $t('duration_unit_plural')"
                        :defaultIndex="onDuration"
                        @select="val => sendCommond('fadeTime', 'fadetimeMs1', val, 0)"
                        :disabled="!canControl"
                        v-model="onPickerVisible"
                    />
                    <List-Item-Picker
                        :name="$t('off_duration')"
                        :info="$t(`duration_${offDuration}`) + (offDuration === 0 || offDuration === 1 ? $t('duration_unit_singular') : $t('duration_unit_plural'))"
                        :options="durationOptions"
                        valueKey="name"
                        :unit="offDuration === 0 || offDuration === 1 ? $t('duration_unit_singular') : $t('duration_unit_plural')"
                        :defaultIndex="offDuration"
                        @select="val => sendCommond('fadeTime', 'fadetimeMs2', val, 0)"
                        :disabled="!canControl"
                        v-model="offPickerVisible"
                    /> -->
                    <List-Item-Picker
                        :name="$t('common_mode')"
                        :info="$t(`common_mode_${commonMode}`)"
                        :options="commonModeOptions"
                        valueKey="name"
                        :defaultIndex="commonMode"
                        @select="val => sendCommond('commonMode', 'mode', val, 0)"
                        :disabled="!canControl"
                        :divider="false"
                        v-model="commonModePickerVisible"
                    />
                </List-Container>
            </div>
        </div>
    </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import { goBack } from "../util/mixins";
export default {
    name: "Setting",
    mixins: [goBack],
    data() {
        return {
            dialogList: ['dimPickerVisible', 'onPickerVisible', 'offPickerVisible', 'commonModePickerVisible'],
            dimPickerVisible: false,
            onPickerVisible: false,
            offPickerVisible: false,
            commonModePickerVisible: false,
            durationOptions: Array.from({length: 11}, (_, i) => ({
                name: this.$t(`duration_${i}`),
                value: i
            })),
            onDuration: 0,
            offDuration: 0,
            commonMode: 0,
            commonModeOptions: [
                {
                    name: this.$t('common_mode_0'),
                    value: 0
                },
                {
                    name: this.$t('common_mode_1'),
                    value: 1
                },
                {
                    name: this.$t('common_mode_2'),
                    value: 2
                },
                {
                    name: this.$t('common_mode_3'),
                    value: 3
                },
                {
                    name: this.$t('common_mode_4'),
                    value: 4
                },
                {
                    name: this.$t('common_mode_100'),
                    value: 100
                }
            ]
        }
    },
    computed: {
        ...mapGetters(["headerHeight", "statusBarHeight", "canControl"]),
        ...mapState({
            commonMemorySwitch: (state) => state.commonMemorySwitch.status,
            donotDisturb: (state) => state.donotDisturb.on,
            dimDuration: (state) => state.progressSwitch.fadeTime
        }),
    },
    activated() {
        this.onDuration = localStorage.getItem('onDuration') ? localStorage.getItem('onDuration') : 0;
        this.offDuration = localStorage.getItem('offDuration') ? localStorage.getItem('offDuration') : 0;
        this.commonMode = localStorage.getItem('commonMode') ? localStorage.getItem('commonMode') : 0;
    },
    methods: {
        sendCommond(sid, chara, value, curValue) {
            if (!this.canControl) {
                return;
            }
            if (chara === 'fadetimeMs1') {
                this.onDuration = value;
                localStorage.setItem('onDuration', value);
            } else if (chara === 'fadetimeMs2') {
                this.offDuration = value;
                localStorage.setItem('offDuration', value);
            } else if (chara === 'mode') {
                this.commonMode = value;
                localStorage.setItem('commonMode', value);
            }
            const data = {
                [sid]: {
                    [chara]: value,
                },
            };
            this.$store.dispatch("setDevInfo", data);
        },
    },
};
</script>

<style scoped>
#setting-page {
    .content {
        margin: 0 var(--second_margin) 0px var(--second_margin);
        position: relative;
    }
    .module-box {
        margin: 0.8rem 0.6rem 0;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-items: flex-start;
        justify-content: flex-start;
    }
}
</style>
