<template>
  <div class="dialog" @click.self="cancel(false)">
    <div class="dialog-container">
      <div class="content">
        <span>{{ name || $t('add_save') }}</span>
        <Loading />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'dialogSave',
  props: {
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  methods: {
    cancel() {}
  }
}
</script>

<style lang="less" scoped>
@import url('../../lib/style/public.less');
.dialog-container {
  .content {
    height: 4.8rem;
    margin: 2.4rem;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    & > span {
      font-size: 1.6rem;
      line-height: 2.2rem;
      color: var(--emui_text_primary);
    }
  }
}
</style>