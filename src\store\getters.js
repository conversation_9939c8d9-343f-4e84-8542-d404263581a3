const getters = {
// HEADER高度
    headerHeight(state) {
        return state.statusBarHeight + 56;
    },
    imgPath(state) {
        return state.isDarkMode ? 'dark/' : '';
    },
    statusBarHeight(state) {
        return state.statusBarHeight;
    },
    canControl(state) {
        if (window.hilink) {
            return true
        } else {
            return true
        }
    }
};
export default getters;
